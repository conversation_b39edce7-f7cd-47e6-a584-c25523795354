/**
 * @file 响应式Hook
 * @description 提供响应式断点检测和屏幕尺寸信息
 */

import { useState, useEffect } from 'react'
import { BREAKPOINTS } from '@/constants'

export interface ResponsiveState {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isLargeDesktop: boolean
  breakpoint: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  width: number
  height: number
}

// 将CSS断点值转换为数字
const breakpointValues = {
  sm: parseInt(BREAKPOINTS.sm),
  md: parseInt(BREAKPOINTS.md),
  lg: parseInt(BREAKPOINTS.lg),
  xl: parseInt(BREAKPOINTS.xl),
  '2xl': parseInt(BREAKPOINTS['2xl'])
}

// 获取当前断点
const getCurrentBreakpoint = (width: number): ResponsiveState['breakpoint'] => {
  if (width >= breakpointValues['2xl']) return '2xl'
  if (width >= breakpointValues.xl) return 'xl'
  if (width >= breakpointValues.lg) return 'lg'
  if (width >= breakpointValues.md) return 'md'
  return 'sm'
}

// 获取响应式状态
const getResponsiveState = (width: number, height: number): ResponsiveState => {
  const breakpoint = getCurrentBreakpoint(width)
  
  return {
    isMobile: width < breakpointValues.md,
    isTablet: width >= breakpointValues.md && width < breakpointValues.lg,
    isDesktop: width >= breakpointValues.lg && width < breakpointValues.xl,
    isLargeDesktop: width >= breakpointValues.xl,
    breakpoint,
    width,
    height
  }
}

export const useResponsive = (): ResponsiveState => {
  // 初始状态，避免SSR问题
  const [state, setState] = useState<ResponsiveState>(() => {
    // 在服务端渲染时使用默认值
    if (typeof window === 'undefined') {
      return {
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isLargeDesktop: false,
        breakpoint: 'lg' as const,
        width: 1024,
        height: 768
      }
    }
    
    return getResponsiveState(window.innerWidth, window.innerHeight)
  })

  useEffect(() => {
    // 如果在服务端，不执行任何操作
    if (typeof window === 'undefined') return

    const handleResize = () => {
      setState(getResponsiveState(window.innerWidth, window.innerHeight))
    }

    // 初始化时设置正确的状态
    handleResize()

    // 添加事件监听器
    window.addEventListener('resize', handleResize)
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return state
}

// 媒体查询Hook
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia(query)
    
    // 设置初始值
    setMatches(mediaQuery.matches)

    // 监听变化
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    mediaQuery.addEventListener('change', handleChange)

    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }, [query])

  return matches
}

// 预定义的媒体查询Hook
export const useBreakpoint = () => {
  const isSm = useMediaQuery(`(min-width: ${BREAKPOINTS.sm})`)
  const isMd = useMediaQuery(`(min-width: ${BREAKPOINTS.md})`)
  const isLg = useMediaQuery(`(min-width: ${BREAKPOINTS.lg})`)
  const isXl = useMediaQuery(`(min-width: ${BREAKPOINTS.xl})`)
  const is2Xl = useMediaQuery(`(min-width: ${BREAKPOINTS['2xl']})`)

  return {
    isSm,
    isMd,
    isLg,
    isXl,
    is2Xl,
    // 便捷的设备类型判断
    isMobile: !isMd,
    isTablet: isMd && !isLg,
    isDesktop: isLg
  }
}

// 屏幕方向Hook
export const useOrientation = () => {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait')

  useEffect(() => {
    if (typeof window === 'undefined') return

    const updateOrientation = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape')
    }

    // 初始化
    updateOrientation()

    // 监听变化
    window.addEventListener('resize', updateOrientation)
    window.addEventListener('orientationchange', updateOrientation)

    return () => {
      window.removeEventListener('resize', updateOrientation)
      window.removeEventListener('orientationchange', updateOrientation)
    }
  }, [])

  return orientation
}

// 视口尺寸Hook
export const useViewport = () => {
  const [viewport, setViewport] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768
  })

  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleResize = () => {
      setViewport({
        width: window.innerWidth,
        height: window.innerHeight
      })
    }

    window.addEventListener('resize', handleResize)
    
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return viewport
}
