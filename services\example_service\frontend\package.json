{"name": "tsif-micro-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "prepare": "husky install", "analyze": "vite-bundle-analyzer", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1", "axios": "^1.7.7", "zustand": "^4.5.5", "clsx": "^2.1.1", "tailwind-merge": "^2.5.2", "framer-motion": "^11.5.4", "react-hot-toast": "^2.4.1", "react-error-boundary": "^4.0.13", "workbox-window": "^7.1.0"}, "devDependencies": {"@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/ui": "^2.0.5", "@testing-library/react": "^16.0.1", "@testing-library/jest-dom": "^6.5.0", "@testing-library/user-event": "^14.5.2", "autoprefixer": "^10.4.20", "eslint": "^9.10.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.11", "eslint-plugin-storybook": "^0.8.0", "msw": "^2.4.4", "postcss": "^8.4.45", "tailwindcss": "^3.4.10", "typescript": "^5.6.2", "vite": "^5.4.5", "vitest": "^2.0.5", "@vitest/coverage-v8": "^2.0.5", "prettier": "^3.3.3", "husky": "^9.1.5", "lint-staged": "^15.2.10", "vite-bundle-analyzer": "^0.11.0", "@storybook/react": "^8.2.9", "@storybook/react-vite": "^8.2.9", "@storybook/addon-essentials": "^8.2.9", "@storybook/addon-interactions": "^8.2.9", "@storybook/addon-links": "^8.2.9", "storybook": "^8.2.9", "workbox-cli": "^7.1.0", "vite-plugin-pwa": "^0.20.5"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "msw": {"workerDirectory": "public"}}