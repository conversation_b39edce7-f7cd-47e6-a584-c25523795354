import os
import sys
from typing import Optional

from commonlib.configs.basic_configs import BasicConfig
from pydantic_settings import SettingsConfigDict


class AppSettings(BasicConfig):
    """统一配置模型，支持JSON文件读取"""

    app_name: str = "tsif"
    debug: bool = False
    basic_log_dir: Optional[str] = None

    @property
    def is_celery(self) -> bool:
        return "celery" in " ".join(sys.argv).lower()

    @property
    def is_test(self) -> bool:
        return os.getenv("RUN_MODE") == "test"

    @property
    def is_cli(self) -> bool:
        return os.getenv("RUN_MODE") == "cli"

    @property
    def is_arq(self) -> bool:
        return os.getenv("RUN_MODE") == "arq"

    @property
    def log_dir(self) -> str:
        basic_log_dir = self.basic_log_dir
        if self.is_cli:
            final_log_dir = f"{basic_log_dir}/cli"
        elif self.is_celery:
            final_log_dir = f"{basic_log_dir}/celery"
        elif self.is_test:
            final_log_dir = f"{basic_log_dir}/test"
        elif self.is_arq:
            final_log_dir = f"{basic_log_dir}/arq"
        elif self.application.project_name:
            dir_name = (
                self.application.project_name.strip().replace(" ", "").lower() or "app"
            )
            final_log_dir = f"{basic_log_dir}/{dir_name}"
        else:
            final_log_dir = f"{basic_log_dir}/app"
        return final_log_dir

    model_config = SettingsConfigDict(
        # read from dotenv format configs file
        env_file=".env",
        env_file_encoding="utf-8",
        # ignore extra attributes
        extra="ignore",
        # support JSON file loading
        json_file_encoding="utf-8",
    )
