/**
 * @file 主题相关类型定义
 * @description 主题系统的TypeScript类型定义
 */

// 主题模式
export type ThemeMode = 'light' | 'dark' | 'auto'

// 主题变体
export type ThemeVariant = 'default' | 'tenant' | 'custom'

// 颜色模式
export type ColorMode = 'light' | 'dark'

// 主题配置接口
export interface ThemeConfig {
  mode: ThemeMode
  variant: ThemeVariant
  colors: ThemeColors
  typography: ThemeTypography
  spacing: ThemeSpacing
  animations: ThemeAnimations
  breakpoints: ThemeBreakpoints
}

// 主题颜色接口
export interface ThemeColors {
  // 主色调
  primary: ColorPalette
  secondary: ColorPalette
  
  // 语义化颜色
  success: ColorPalette
  warning: ColorPalette
  error: ColorPalette
  info: ColorPalette
  
  // 中性色
  gray: ColorPalette
  
  // 背景色
  background: {
    primary: string
    secondary: string
    tertiary: string
    overlay: string
  }
  
  // 文字颜色
  text: {
    primary: string
    secondary: string
    tertiary: string
    inverse: string
    disabled: string
  }
  
  // 边框颜色
  border: {
    primary: string
    secondary: string
    focus: string
    error: string
  }
  
  // 阴影颜色
  shadow: {
    sm: string
    md: string
    lg: string
    xl: string
  }
}

// 颜色调色板接口
export interface ColorPalette {
  50: string
  100: string
  200: string
  300: string
  400: string
  500: string
  600: string
  700: string
  800: string
  900: string
}

// 字体排版接口
export interface ThemeTypography {
  fontFamily: {
    sans: string[]
    serif: string[]
    mono: string[]
  }
  fontSize: {
    xs: string
    sm: string
    base: string
    lg: string
    xl: string
    '2xl': string
    '3xl': string
    '4xl': string
    '5xl': string
    '6xl': string
  }
  fontWeight: {
    thin: number
    light: number
    normal: number
    medium: number
    semibold: number
    bold: number
    extrabold: number
  }
  lineHeight: {
    tight: number
    normal: number
    relaxed: number
    loose: number
  }
  letterSpacing: {
    tight: string
    normal: string
    wide: string
  }
}

// 间距系统接口
export interface ThemeSpacing {
  0: string
  1: string
  2: string
  3: string
  4: string
  5: string
  6: string
  8: string
  10: string
  12: string
  16: string
  20: string
  24: string
  32: string
  40: string
  48: string
  56: string
  64: string
}

// 动画配置接口
export interface ThemeAnimations {
  duration: {
    fast: string
    normal: string
    slow: string
  }
  easing: {
    linear: string
    easeIn: string
    easeOut: string
    easeInOut: string
  }
  keyframes: Record<string, Record<string, any>>
}

// 响应式断点接口
export interface ThemeBreakpoints {
  sm: string
  md: string
  lg: string
  xl: string
  '2xl': string
}

// 租户主题配置接口
export interface TenantThemeConfig {
  id: string
  name: string
  colors: Partial<ThemeColors>
  logo?: {
    light: string
    dark: string
  }
  favicon?: string
  customCss?: string
  fonts?: {
    primary: string
    secondary?: string
  }
}

// 主题状态接口
export interface ThemeState {
  mode: ThemeMode
  variant: ThemeVariant
  config: ThemeConfig
  tenantConfig?: TenantThemeConfig
  isLoading: boolean
  error?: string
}

// 主题上下文接口
export interface ThemeContextValue {
  theme: ThemeState
  setMode: (mode: ThemeMode) => void
  setVariant: (variant: ThemeVariant) => void
  setTenantConfig: (config: TenantThemeConfig) => void
  resetTheme: () => void
  toggleMode: () => void
}

// CSS变量映射接口
export interface CSSVariables {
  [key: string]: string
}

// 主题提供者属性接口
export interface ThemeProviderProps {
  children: React.ReactNode
  defaultMode?: ThemeMode
  defaultVariant?: ThemeVariant
  tenantConfig?: TenantThemeConfig
  storageKey?: string
}

// 主题切换器属性接口
export interface ThemeSwitcherProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'button' | 'dropdown' | 'toggle'
  showLabel?: boolean
  className?: string
}

// 主题预览接口
export interface ThemePreview {
  id: string
  name: string
  mode: ColorMode
  colors: {
    primary: string
    background: string
    text: string
  }
  thumbnail?: string
}
