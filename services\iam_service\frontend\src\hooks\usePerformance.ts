/**
 * @file 性能优化Hook
 * @description 提供防抖、节流、懒加载和性能监控功能
 */

import React from 'react'

// 防抖Hook
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value)

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// 节流Hook
export const useThrottle = <T>(value: T, limit: number): T => {
  const [throttledValue, setThrottledValue] = React.useState<T>(value)
  const lastRan = React.useRef<number>(Date.now())

  React.useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value)
        lastRan.current = Date.now()
      }
    }, limit - (Date.now() - lastRan.current))

    return () => {
      clearTimeout(handler)
    }
  }, [value, limit])

  return throttledValue
}

// 防抖回调Hook
export const useDebouncedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = React.useRef<NodeJS.Timeout>()

  const debouncedCallback = React.useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args)
      }, delay)
    },
    [callback, delay]
  ) as T

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return debouncedCallback
}

// 节流回调Hook
export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  limit: number
): T => {
  const lastRan = React.useRef<number>(0)

  const throttledCallback = React.useCallback(
    (...args: Parameters<T>) => {
      if (Date.now() - lastRan.current >= limit) {
        callback(...args)
        lastRan.current = Date.now()
      }
    },
    [callback, limit]
  ) as T

  return throttledCallback
}

// 懒加载Hook
export const useLazyLoad = (
  threshold: number = 0.1,
  rootMargin: string = '0px'
) => {
  const [isVisible, setIsVisible] = React.useState(false)
  const [isLoaded, setIsLoaded] = React.useState(false)
  const elementRef = React.useRef<HTMLElement>(null)

  React.useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isLoaded) {
          setIsVisible(true)
          setIsLoaded(true)
          observer.unobserve(element)
        }
      },
      {
        threshold,
        rootMargin
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [threshold, rootMargin, isLoaded])

  return { elementRef, isVisible, isLoaded }
}

// 性能监控Hook
export interface PerformanceMetrics {
  renderTime: number
  mountTime: number
  updateCount: number
  lastUpdateTime: number
}

export const usePerformanceMonitor = (componentName?: string) => {
  const [metrics, setMetrics] = React.useState<PerformanceMetrics>({
    renderTime: 0,
    mountTime: 0,
    updateCount: 0,
    lastUpdateTime: 0
  })

  const mountTimeRef = React.useRef<number>(0)
  const renderStartRef = React.useRef<number>(0)
  const updateCountRef = React.useRef<number>(0)

  // 记录挂载时间
  React.useEffect(() => {
    mountTimeRef.current = performance.now()
    setMetrics(prev => ({
      ...prev,
      mountTime: mountTimeRef.current
    }))
  }, [])

  // 记录渲染时间
  React.useLayoutEffect(() => {
    const renderTime = performance.now() - renderStartRef.current
    updateCountRef.current += 1

    setMetrics(prev => ({
      ...prev,
      renderTime,
      updateCount: updateCountRef.current,
      lastUpdateTime: performance.now()
    }))

    if (componentName && renderTime > 16) { // 超过一帧的时间
      console.warn(`${componentName} render took ${renderTime.toFixed(2)}ms`)
    }
  })

  // 记录渲染开始时间
  renderStartRef.current = performance.now()

  const logMetrics = React.useCallback(() => {
    console.group(`Performance Metrics${componentName ? ` - ${componentName}` : ''}`)
    console.log('Mount Time:', metrics.mountTime.toFixed(2), 'ms')
    console.log('Last Render Time:', metrics.renderTime.toFixed(2), 'ms')
    console.log('Update Count:', metrics.updateCount)
    console.log('Last Update Time:', new Date(metrics.lastUpdateTime).toISOString())
    console.groupEnd()
  }, [metrics, componentName])

  return { metrics, logMetrics }
}

// 内存使用监控Hook
export const useMemoryMonitor = () => {
  const [memoryInfo, setMemoryInfo] = React.useState<{
    usedJSHeapSize: number
    totalJSHeapSize: number
    jsHeapSizeLimit: number
  } | null>(null)

  React.useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit
        })
      }
    }

    updateMemoryInfo()
    const interval = setInterval(updateMemoryInfo, 5000) // 每5秒更新一次

    return () => clearInterval(interval)
  }, [])

  const formatBytes = React.useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }, [])

  return {
    memoryInfo,
    formatBytes,
    memoryUsagePercentage: memoryInfo 
      ? (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100 
      : 0
  }
}

// 虚拟滚动Hook
export const useVirtualScroll = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = React.useState(0)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = React.useMemo(() => {
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index
    }))
  }, [items, startIndex, endIndex])

  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  const handleScroll = React.useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll
  }
}

// 图片懒加载Hook
export const useImageLazyLoad = (src: string, placeholder?: string) => {
  const [imageSrc, setImageSrc] = React.useState(placeholder || '')
  const [isLoading, setIsLoading] = React.useState(true)
  const [hasError, setHasError] = React.useState(false)
  const { elementRef, isVisible } = useLazyLoad()

  React.useEffect(() => {
    if (!isVisible || !src) return

    const img = new Image()
    
    img.onload = () => {
      setImageSrc(src)
      setIsLoading(false)
      setHasError(false)
    }

    img.onerror = () => {
      setIsLoading(false)
      setHasError(true)
    }

    img.src = src
  }, [isVisible, src])

  return {
    elementRef,
    imageSrc,
    isLoading,
    hasError,
    isVisible
  }
}
