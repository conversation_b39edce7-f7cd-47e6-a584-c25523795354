{"app_name": "ThingsMore", "config_path": "./config/config_prd.json", "basic_log_dir": "../../../log", "debug": true, "application": {"debug": true, "title": "ThingsMore API", "project_name": "ThingsMore", "description": "ThingsMore Application API", "secret_key": "your-production-secret-key-here", "docs_url": "/docs", "openapi_url": "/openapi.json", "redoc_url": "/redoc"}, "connection_priority": {"redis": {"enabled": true, "connect_priority": 1, "shutdown_priority": 5}, "arq_redis": {"enabled": false, "connect_priority": 2, "shutdown_priority": 4}, "mysql": {"enabled": false, "connect_priority": 3, "shutdown_priority": 3}, "mongodb": {"enabled": true, "connect_priority": 4, "shutdown_priority": 2}, "postgres": {"enabled": true, "connect_priority": 4, "shutdown_priority": 2}, "rabbitmq": {"enabled": false, "connect_priority": 5, "shutdown_priority": 1}}, "persistence": {"mysql": {"MYSQL_HOST": "**************", "MYSQL_PORT": 3306, "MYSQL_USERNAME": "root", "MYSQL_PASSWORD": "thismore@123456", "MYSQL_DATABASE": "thingsmore", "MYSQL_SCHEME": "mysql+aiomysql", "MYSQL_POOL_SIZE": 30, "MYSQL_TIMEOUT": 10, "MYSQL_POOL_RECYCLE": 3600, "MYSQL_POOL_PRE_PING": true, "MYSQL_ECHO": false}, "postgres": {"POSTGRES_HOST": "**************", "POSTGRES_PORT": 5432, "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "thismore@123456", "POSTGRES_DATABASE": "thingsmore", "POSTGRES_SCHEMA": "public", "POSTGRES_POOL_SIZE": 30, "POSTGRES_POOL_TIMEOUT": 10, "POSTGRES_POOL_RECYCLE": 3600, "POSTGRES_POOL_PRE_PING": true, "POSTGRES_ECHO": false}, "redis": {"REDIS_HOST": "**************", "REDIS_PORT": 6378, "REDIS_DB": 0, "REDIS_USERNAME": "", "REDIS_PASSWORD": "thismore@123456", "REDIS_SSL": false, "REDIS_POOL_SIZE": 10, "REDIS_MAX_CONNECTIONS": 10, "REDIS_POOL_TIMEOUT": 5, "REDIS_POOL_RECYCLE": 3600, "REDIS_RETRY_ON_TIMEOUT": true, "REDIS_POOL_PRE_PING": true, "REDIS_DECODE_RESPONSE": true}, "mongodb": {"MONGODB_HOST": "**************", "MONGODB_PORT": 27017, "MONGODB_USERNAME": "root", "MONGODB_PASSWORD": "thismore@123456", "MONGODB_DATABASE": "thingsmore", "MONGODB_AUTH_SOURCE": "admin", "MONGODB_AUTH_MECHANISM": "SCRAM-SHA-256", "MONGODB_MIN_POOL_SIZE": 10, "MONGODB_MAX_POOL_SIZE": 50, "MONGODB_MAX_IDLE_TIME_MS": 10000, "MONGODB_CONNECT_TIMEOUT_MS": 20000}}, "messaging": {"rabbitmq": {"ENABLED": true, "RABBITMQ_HOST": "**************", "RABBITMQ_PORT": 5672, "RABBITMQ_USERNAME": "root", "RABBITMQ_PASSWORD": "thismore@123456", "RABBITMQ_VHOST": "/", "RABBITMQ_SCHEME": "amqp", "RABBITMQ_POOL_SIZE": 10, "RABBITMQ_POOL_TIMEOUT": 30, "RABBITMQ_POOL_RECYCLE": 3600, "RABBITMQ_SSL": false, "RABBITMQ_HEARTBEAT": 60, "RABBITMQ_CONNECTION_TIMEOUT": 30, "RABBITMQ_CHANNEL_MAX": 2048, "RABBITMQ_FRAME_MAX": 131072, "RABBITMQ_RETRY_DELAY": 5, "RABBITMQ_MAX_RETRIES": 3}, "celery": {"ENABLED": false, "CELERY_BROKER_URL": "amqp://root:thismore%40123456@**************:5672//", "CELERY_RESULT_BACKEND": "*****************************************************", "CELERY_TIMEZONE": "Asia/Shanghai", "CELERY_ENABLE_UTC": true}}, "middleware": {"cors": {"AllowOrigins": ["*"], "AllowMethods": ["*"], "AllowHeaders": ["*"], "AllowCredentials": true, "ExposeHeaders": [], "MaxAge": 600}, "security": {"SSLRedirect": false, "ForceSSL": false, "FrameDeny": true, "ContentTypeNosniff": true, "BrowserXSSFilter": true, "HSTS": {"IncludeSubdomains": true, "Preload": false, "MaxAge": 31536000}}, "compression": {"Enabled": true, "Level": 9, "MinimumSize": 500}}}