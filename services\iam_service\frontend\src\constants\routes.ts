/**
 * @file 路由相关常量
 * @description 应用路由的常量定义
 */

// 认证相关路由
export const AUTH_ROUTES = {
  LOGIN: '/login',
  REGISTER: '/register',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  VERIFY_EMAIL: '/verify-email',
  ACTIVATE: '/activate'
} as const

// 主应用路由
export const APP_ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  SETTINGS: '/settings',
  HELP: '/help',
  ABOUT: '/about'
} as const

// 管理员路由
export const ADMIN_ROUTES = {
  ADMIN: '/admin',
  USERS: '/admin/users',
  TENANTS: '/admin/tenants',
  ROLES: '/admin/roles',
  PERMISSIONS: '/admin/permissions',
  AUDIT_LOGS: '/admin/audit-logs',
  SYSTEM_SETTINGS: '/admin/system-settings'
} as const

// 错误页面路由
export const ERROR_ROUTES = {
  NOT_FOUND: '/404',
  FORBIDDEN: '/403',
  INTERNAL_ERROR: '/500',
  MAINTENANCE: '/maintenance'
} as const

// 所有路由的集合
export const ALL_ROUTES = {
  ...AUTH_ROUTES,
  ...APP_ROUTES,
  ...ADMIN_ROUTES,
  ...ERROR_ROUTES
} as const

// 公开路由（不需要认证）
export const PUBLIC_ROUTES = [
  AUTH_ROUTES.LOGIN,
  AUTH_ROUTES.REGISTER,
  AUTH_ROUTES.FORGOT_PASSWORD,
  AUTH_ROUTES.RESET_PASSWORD,
  AUTH_ROUTES.VERIFY_EMAIL,
  AUTH_ROUTES.ACTIVATE,
  ERROR_ROUTES.NOT_FOUND,
  ERROR_ROUTES.FORBIDDEN,
  ERROR_ROUTES.INTERNAL_ERROR,
  ERROR_ROUTES.MAINTENANCE,
  APP_ROUTES.ABOUT
] as const

// 需要认证的路由
export const PROTECTED_ROUTES = [
  APP_ROUTES.HOME,
  APP_ROUTES.DASHBOARD,
  APP_ROUTES.PROFILE,
  APP_ROUTES.SETTINGS,
  APP_ROUTES.HELP
] as const

// 管理员专用路由
export const ADMIN_ONLY_ROUTES = [
  ADMIN_ROUTES.ADMIN,
  ADMIN_ROUTES.USERS,
  ADMIN_ROUTES.TENANTS,
  ADMIN_ROUTES.ROLES,
  ADMIN_ROUTES.PERMISSIONS,
  ADMIN_ROUTES.AUDIT_LOGS,
  ADMIN_ROUTES.SYSTEM_SETTINGS
] as const

// 路由元数据
export const ROUTE_META = {
  [AUTH_ROUTES.LOGIN]: {
    title: '登录',
    description: '用户登录页面',
    layout: 'auth',
    requireAuth: false
  },
  [AUTH_ROUTES.REGISTER]: {
    title: '注册',
    description: '用户注册页面',
    layout: 'auth',
    requireAuth: false
  },
  [AUTH_ROUTES.FORGOT_PASSWORD]: {
    title: '忘记密码',
    description: '密码重置页面',
    layout: 'auth',
    requireAuth: false
  },
  [AUTH_ROUTES.RESET_PASSWORD]: {
    title: '重置密码',
    description: '设置新密码页面',
    layout: 'auth',
    requireAuth: false
  },
  [AUTH_ROUTES.VERIFY_EMAIL]: {
    title: '验证邮箱',
    description: '邮箱验证页面',
    layout: 'auth',
    requireAuth: false
  },
  [AUTH_ROUTES.ACTIVATE]: {
    title: '激活账户',
    description: '账户激活页面',
    layout: 'auth',
    requireAuth: false
  },
  [APP_ROUTES.HOME]: {
    title: '首页',
    description: '应用首页',
    layout: 'main',
    requireAuth: true
  },
  [APP_ROUTES.DASHBOARD]: {
    title: '仪表板',
    description: '用户仪表板',
    layout: 'main',
    requireAuth: true
  },
  [APP_ROUTES.PROFILE]: {
    title: '个人资料',
    description: '用户个人资料页面',
    layout: 'main',
    requireAuth: true
  },
  [APP_ROUTES.SETTINGS]: {
    title: '设置',
    description: '用户设置页面',
    layout: 'main',
    requireAuth: true
  },
  [APP_ROUTES.HELP]: {
    title: '帮助',
    description: '帮助文档页面',
    layout: 'main',
    requireAuth: true
  },
  [APP_ROUTES.ABOUT]: {
    title: '关于',
    description: '关于页面',
    layout: 'main',
    requireAuth: false
  },
  [ERROR_ROUTES.NOT_FOUND]: {
    title: '页面未找到',
    description: '404错误页面',
    layout: 'error',
    requireAuth: false
  },
  [ERROR_ROUTES.FORBIDDEN]: {
    title: '访问被拒绝',
    description: '403错误页面',
    layout: 'error',
    requireAuth: false
  },
  [ERROR_ROUTES.INTERNAL_ERROR]: {
    title: '服务器错误',
    description: '500错误页面',
    layout: 'error',
    requireAuth: false
  },
  [ERROR_ROUTES.MAINTENANCE]: {
    title: '系统维护',
    description: '系统维护页面',
    layout: 'error',
    requireAuth: false
  }
} as const

// 导航菜单配置
export const NAVIGATION_MENU = [
  {
    id: 'dashboard',
    label: '仪表板',
    icon: 'dashboard',
    path: APP_ROUTES.DASHBOARD,
    permission: 'dashboard.view'
  },
  {
    id: 'profile',
    label: '个人资料',
    icon: 'user',
    path: APP_ROUTES.PROFILE,
    permission: 'profile.view'
  },
  {
    id: 'settings',
    label: '设置',
    icon: 'settings',
    path: APP_ROUTES.SETTINGS,
    permission: 'settings.view'
  },
  {
    id: 'help',
    label: '帮助',
    icon: 'help',
    path: APP_ROUTES.HELP,
    permission: 'help.view'
  }
] as const

// 管理员菜单配置
export const ADMIN_NAVIGATION_MENU = [
  {
    id: 'admin-dashboard',
    label: '管理面板',
    icon: 'admin',
    path: ADMIN_ROUTES.ADMIN,
    permission: 'admin.view'
  },
  {
    id: 'users',
    label: '用户管理',
    icon: 'users',
    path: ADMIN_ROUTES.USERS,
    permission: 'users.manage'
  },
  {
    id: 'tenants',
    label: '租户管理',
    icon: 'tenants',
    path: ADMIN_ROUTES.TENANTS,
    permission: 'tenants.manage'
  },
  {
    id: 'roles',
    label: '角色管理',
    icon: 'roles',
    path: ADMIN_ROUTES.ROLES,
    permission: 'roles.manage'
  },
  {
    id: 'permissions',
    label: '权限管理',
    icon: 'permissions',
    path: ADMIN_ROUTES.PERMISSIONS,
    permission: 'permissions.manage'
  },
  {
    id: 'audit-logs',
    label: '审计日志',
    icon: 'logs',
    path: ADMIN_ROUTES.AUDIT_LOGS,
    permission: 'audit.view'
  },
  {
    id: 'system-settings',
    label: '系统设置',
    icon: 'system',
    path: ADMIN_ROUTES.SYSTEM_SETTINGS,
    permission: 'system.manage'
  }
] as const

// 面包屑导航配置
export const BREADCRUMB_CONFIG = {
  [APP_ROUTES.DASHBOARD]: [
    { label: '首页', path: APP_ROUTES.HOME },
    { label: '仪表板', path: APP_ROUTES.DASHBOARD }
  ],
  [APP_ROUTES.PROFILE]: [
    { label: '首页', path: APP_ROUTES.HOME },
    { label: '个人资料', path: APP_ROUTES.PROFILE }
  ],
  [APP_ROUTES.SETTINGS]: [
    { label: '首页', path: APP_ROUTES.HOME },
    { label: '设置', path: APP_ROUTES.SETTINGS }
  ],
  [ADMIN_ROUTES.USERS]: [
    { label: '管理面板', path: ADMIN_ROUTES.ADMIN },
    { label: '用户管理', path: ADMIN_ROUTES.USERS }
  ],
  [ADMIN_ROUTES.TENANTS]: [
    { label: '管理面板', path: ADMIN_ROUTES.ADMIN },
    { label: '租户管理', path: ADMIN_ROUTES.TENANTS }
  ]
} as const

// 路由参数类型
export type RouteParams = {
  id?: string
  token?: string
  code?: string
  tenant?: string
}

// 查询参数类型
export type QueryParams = {
  redirect?: string
  tenant?: string
  email?: string
  code?: string
  token?: string
  page?: string
  size?: string
  sort?: string
  filter?: string
}
