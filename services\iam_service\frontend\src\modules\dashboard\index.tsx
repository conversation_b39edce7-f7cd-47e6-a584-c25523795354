/**
 * @file 仪表板模块
 * @description 示例模块 - 仪表板功能
 * @status 框架示例 - 完成
 */

import React from 'react'
import { RouteObject } from 'react-router-dom'
import { DashboardPage } from './pages/DashboardPage'
import { Module, ModuleConfig } from '@/utils/ModuleLoader'



// 模块配置
const config: ModuleConfig = {
  id: 'dashboard',
  name: '仪表板',
  version: '1.0.0',
  description: '系统仪表板和概览',
  enabled: true,
  lazy: false,
}

// 路由配置
const routes: RouteObject[] = [
  {
    path: '/dashboard',
    element: <DashboardPage />,
  },
]

// 模块定义
export const dashboardModule: Module = {
  config,
  component: DashboardPage,
  routes,
  initialize: async () => {
    console.log('Dashboard module initialized')
  },
  destroy: async () => {
    console.log('Dashboard module destroyed')
  },
}
