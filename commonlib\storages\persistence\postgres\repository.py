from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from uuid import UUID

from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import Select

T = TypeVar("T")


class PostgresRepository(Generic[T]):
    """
    PostgreSQL 数据访问层基类
    提供基础的 CRUD 操作和通用查询方法
    """

    def __init__(self, session: AsyncSession, model: Type[T]):
        """
        初始化仓储
        :param session: SQLAlchemy 异步会话
        :param model: SQLAlchemy 模型类
        """
        self._session = session
        self._model = model

    async def create(self, **kwargs) -> T:
        """
        创建记录
        :param kwargs: 模型字段值
        :return: 创建的记录
        """
        instance = self._model(**kwargs)
        self._session.add(instance)
        await self._session.flush()
        return instance

    async def create_many(self, items: List[Dict[str, Any]]) -> List[T]:
        """
        批量创建记录
        :param items: 记录列表
        :return: 创建的记录列表
        """
        instances = [self._model(**item) for item in items]
        self._session.add_all(instances)
        await self._session.flush()
        return instances

    async def get(self, id: Union[int, str, UUID]) -> Optional[T]:
        """
        根据 ID 获取记录
        :param id: 记录 ID
        :return: 记录实例或 None
        """
        return await self._session.get(self._model, id)

    async def get_by_filter(self, **kwargs) -> Optional[T]:
        """
        根据过滤条件获取单条记录
        :param kwargs: 过滤条件
        :return: 记录实例或 None
        """
        query = select(self._model).filter_by(**kwargs)
        result = await self._session.execute(query)
        return result.scalar_one_or_none()

    async def list(
        self,
        *,
        offset: int = 0,
        limit: Optional[int] = None,
        order_by: Optional[Any] = None,
        **kwargs,
    ) -> List[T]:
        """
        获取记录列表
        :param offset: 偏移量
        :param limit: 限制数量
        :param order_by: 排序字段
        :param kwargs: 过滤条件
        :return: 记录列表
        """
        query = select(self._model).filter_by(**kwargs)

        if order_by is not None:
            query = query.order_by(order_by)

        if offset:
            query = query.offset(offset)
        if limit:
            query = query.limit(limit)

        result = await self._session.execute(query)
        return list(result.scalars().all())

    async def update(self, id: Union[int, str, UUID], **kwargs) -> Optional[T]:
        """
        更新记录
        :param id: 记录 ID
        :param kwargs: 更新的字段值
        :return: 更新后的记录或 None
        """
        query = (
            update(self._model)
            .where(self._model.id == id)
            .values(**kwargs)
            .returning(self._model)
        )
        result = await self._session.execute(query)
        return result.scalar_one_or_none()

    async def delete(self, id: Union[int, str, UUID]) -> bool:
        """
        删除记录
        :param id: 记录 ID
        :return: 是否删除成功
        """
        query = delete(self._model).where(self._model.id == id)
        result = await self._session.execute(query)
        return result.rowcount > 0

    async def count(self, **kwargs) -> int:
        """
        获取记录数量
        :param kwargs: 过滤条件
        :return: 记录数量
        """
        query = select(self._model).filter_by(**kwargs)
        result = await self._session.execute(query)
        return len(result.scalars().all())

    def query(self) -> Select:
        """
        获取基础查询对象，用于构建复杂查询
        :return: SQLAlchemy Select 对象
        """
        return select(self._model)

    async def execute(self, query: Select) -> List[T]:
        """
        执行自定义查询
        :param query: SQLAlchemy Select 对象
        :return: 查询结果列表
        """
        result = await self._session.execute(query)
        return list(result.scalars().all())
