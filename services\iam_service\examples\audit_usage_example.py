"""
审计服务使用示例

演示如何使用审计服务进行日志记录、查询、统计和导出
"""

import asyncio
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from services.iam_service.services.audit_service import AuditService
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import User, Tenant, AuditLog


async def create_audit_service():
    """创建审计服务实例"""
    # 创建数据库引擎和会话
    engine = create_async_engine("sqlite+aiosqlite:///example.db")
    async_session = sessionmaker(engine, class_=AsyncSession)
    session = async_session()
    
    # 创建Redis仓库
    redis_repo = RedisRepository(host="localhost", port=6379, db=0)
    
    # 创建审计服务
    audit_service = AuditService(
        session=session,
        redis_repo=redis_repo,
        user_model=User,
        tenant_model=Tenant,
        audit_log_model=AuditLog
    )
    
    return audit_service, session


async def demo_audit_log_creation():
    """演示审计日志创建功能"""
    audit_service, session = await create_audit_service()
    
    try:
        print("=== 审计日志创建演示 ===")
        
        # 1. 创建成功登录日志
        print("\n1. 创建成功登录日志")
        login_result = await audit_service.create_audit_log(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            action="LOGIN",
            resource_type="USER",
            description="用户成功登录系统",
            ip_address="*************",
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
            status="success"
        )
        print(f"登录日志ID: {login_result['log_id']}")
        print(f"创建时间: {login_result['created_at']}")
        
        # 2. 创建失败操作日志
        print("\n2. 创建失败操作日志")
        failure_result = await audit_service.create_audit_log(
            tenant_id="tenant_demo",
            user_id="user_demo_456",
            action="DELETE_USER",
            resource_type="USER",
            resource_id="user_demo_789",
            description="删除用户操作失败：权限不足",
            details={
                "target_user": "user_demo_789",
                "error_code": "PERMISSION_DENIED",
                "attempted_by": "user_demo_456"
            },
            ip_address="*************",
            status="failure"
        )
        print(f"失败操作日志ID: {failure_result['log_id']}")
        
        # 3. 创建系统错误日志
        print("\n3. 创建系统错误日志")
        error_result = await audit_service.create_audit_log(
            tenant_id="tenant_demo",
            user_id=None,  # 系统操作，无用户ID
            action="SYSTEM_BACKUP",
            resource_type="SYSTEM",
            description="系统备份过程中发生错误",
            details={
                "backup_type": "full",
                "error_message": "磁盘空间不足",
                "backup_size": "500GB"
            },
            status="error"
        )
        print(f"系统错误日志ID: {error_result['log_id']}")
        
        # 4. 创建高风险操作日志
        print("\n4. 创建高风险操作日志")
        admin_result = await audit_service.create_audit_log(
            tenant_id="tenant_demo",
            user_id="admin_user_123",
            action="ADMIN_LOGIN",
            resource_type="ADMIN",
            description="管理员登录系统",
            details={
                "login_method": "password",
                "mfa_enabled": True,
                "login_location": "Beijing, China"
            },
            ip_address="***********",
            status="success"
        )
        print(f"管理员登录日志ID: {admin_result['log_id']}")
        
    except Exception as e:
        print(f"审计日志创建演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_audit_log_query():
    """演示审计日志查询功能"""
    audit_service, session = await create_audit_service()
    
    try:
        print("\n=== 审计日志查询演示 ===")
        
        # 1. 基本查询
        print("\n1. 查询所有审计日志")
        all_logs = await audit_service.query_audit_logs(
            tenant_id="tenant_demo",
            page=1,
            page_size=10
        )
        print(f"总日志数: {all_logs['total']}")
        print(f"当前页日志数: {len(all_logs['logs'])}")
        
        # 2. 按用户查询
        print("\n2. 按用户查询日志")
        user_logs = await audit_service.query_audit_logs(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            page=1,
            page_size=5
        )
        print(f"用户 user_demo_123 的日志数: {user_logs['total']}")
        
        # 3. 按操作类型查询
        print("\n3. 按操作类型查询")
        login_logs = await audit_service.query_audit_logs(
            tenant_id="tenant_demo",
            action="LOGIN",
            page=1,
            page_size=5
        )
        print(f"登录操作日志数: {login_logs['total']}")
        
        # 4. 按时间范围查询
        print("\n4. 按时间范围查询")
        start_time = (datetime.utcnow() - timedelta(hours=1)).isoformat()
        end_time = datetime.utcnow().isoformat()
        
        recent_logs = await audit_service.query_audit_logs(
            tenant_id="tenant_demo",
            start_time=start_time,
            end_time=end_time,
            page=1,
            page_size=10
        )
        print(f"最近1小时的日志数: {recent_logs['total']}")
        
        # 5. 按状态查询
        print("\n5. 按状态查询")
        failed_logs = await audit_service.query_audit_logs(
            tenant_id="tenant_demo",
            status="failure",
            page=1,
            page_size=5
        )
        print(f"失败操作日志数: {failed_logs['total']}")
        
        # 6. 复合条件查询
        print("\n6. 复合条件查询")
        complex_logs = await audit_service.query_audit_logs(
            tenant_id="tenant_demo",
            resource_type="USER",
            status="success",
            order_by="created_at",
            order_direction="desc",
            page=1,
            page_size=5
        )
        print(f"用户相关成功操作日志数: {complex_logs['total']}")
        
        # 显示查询结果详情
        if complex_logs['logs']:
            print("\n查询结果示例:")
            for log in complex_logs['logs'][:3]:  # 显示前3条
                print(f"  - {log['action']} by {log['username'] or '系统'} at {log['created_at']}")
                print(f"    状态: {log['status']}, 风险级别: {log['risk_level']}")
        
    except Exception as e:
        print(f"审计日志查询演示出错: {e}")
    finally:
        await session.close()


async def demo_audit_statistics():
    """演示审计统计功能"""
    audit_service, session = await create_audit_service()
    
    try:
        print("\n=== 审计统计演示 ===")
        
        # 1. 获取基本统计
        print("\n1. 获取基本统计数据")
        stats = await audit_service.get_audit_statistics(
            tenant_id="tenant_demo",
            group_by="day"
        )
        
        print(f"统计周期: {stats['period']}")
        print(f"总日志数: {stats['total_logs']}")
        print(f"成功操作: {stats['success_logs']}")
        print(f"失败操作: {stats['failed_logs']}")
        print(f"高风险操作: {stats['high_risk_logs']}")
        
        # 2. 热门操作统计
        print("\n2. 热门操作统计")
        print("热门操作:")
        for action in stats['top_actions']:
            print(f"  - {action['action']}: {action['count']} 次")
        
        # 3. 活跃用户统计
        print("\n3. 活跃用户统计")
        print("活跃用户:")
        for user in stats['top_users']:
            print(f"  - {user['username']}: {user['count']} 次操作")
        
        # 4. 风险分布统计
        print("\n4. 风险分布统计")
        risk_dist = stats['risk_distribution']
        print(f"低风险: {risk_dist['low']} 次")
        print(f"中风险: {risk_dist['medium']} 次")
        print(f"高风险: {risk_dist['high']} 次")
        print(f"严重风险: {risk_dist['critical']} 次")
        
        # 5. 时间序列数据
        print("\n5. 时间序列趋势")
        print("每日操作趋势:")
        for data in stats['time_series'][:5]:  # 显示前5天
            print(f"  {data['time']}: 总计 {data['total']}, 成功 {data['success']}, 失败 {data['failed']}")
        
        # 6. 按小时分组统计
        print("\n6. 按小时分组统计")
        hourly_stats = await audit_service.get_audit_statistics(
            tenant_id="tenant_demo",
            start_time=(datetime.utcnow() - timedelta(days=1)).isoformat(),
            end_time=datetime.utcnow().isoformat(),
            group_by="hour"
        )
        print(f"最近24小时总操作数: {hourly_stats['total_logs']}")
        
    except Exception as e:
        print(f"审计统计演示出错: {e}")
    finally:
        await session.close()


async def demo_audit_export():
    """演示审计日志导出功能"""
    audit_service, session = await create_audit_service()
    
    try:
        print("\n=== 审计日志导出演示 ===")
        
        # 1. 导出CSV格式
        print("\n1. 导出CSV格式")
        csv_export = await audit_service.export_audit_logs(
            tenant_id="tenant_demo",
            format="csv",
            include_details=True
        )
        print(f"CSV导出ID: {csv_export['export_id']}")
        print(f"文件名: {csv_export['file_name']}")
        print(f"文件大小: {csv_export['file_size']} 字节")
        print(f"记录数量: {csv_export['record_count']}")
        print(f"下载链接: {csv_export['download_url']}")
        print(f"过期时间: {csv_export['expires_at']}")
        
        # 2. 导出JSON格式
        print("\n2. 导出JSON格式")
        json_export = await audit_service.export_audit_logs(
            tenant_id="tenant_demo",
            format="json",
            include_details=False
        )
        print(f"JSON导出ID: {json_export['export_id']}")
        print(f"文件名: {json_export['file_name']}")
        print(f"记录数量: {json_export['record_count']}")
        
        # 3. 按条件导出
        print("\n3. 按条件导出")
        filtered_export = await audit_service.export_audit_logs(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            start_time=(datetime.utcnow() - timedelta(days=7)).isoformat(),
            end_time=datetime.utcnow().isoformat(),
            format="csv",
            include_details=True
        )
        print(f"条件导出ID: {filtered_export['export_id']}")
        print(f"导出记录数: {filtered_export['record_count']}")
        
        print("\n导出功能演示完成！")
        print("注意：实际使用中，导出的文件会存储到文件系统或对象存储中")
        
    except Exception as e:
        print(f"审计日志导出演示出错: {e}")
    finally:
        await session.close()


async def main():
    """主函数"""
    print("审计服务使用示例")
    print("=" * 50)
    
    # 运行各个演示
    await demo_audit_log_creation()
    await demo_audit_log_query()
    await demo_audit_statistics()
    await demo_audit_export()
    
    print("\n演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
