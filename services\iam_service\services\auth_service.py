"""
认证服务

提供认证与安全相关的业务逻辑实现
支持多种认证方式、会话管理、MFA、密码管理等功能
"""
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Type

from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    DatabaseError, BusinessError, AuthenticationError, AuthorizationError
)
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission,
    UserSessionHistory, UserMFA, VerificationCode, PasswordHistory,
    AuditLog, AuditLogBuilder
)
from security.jwt_manager import JWTManager, TokenPair, TokenPayload
from security.session_manager import SessionManager, SessionInfo, DeviceInfo
from security.security_utils import SecurityUtils


class AuthService:
    """认证服务类"""

    # 支持的登录类型
    LOGIN_TYPES = ["username", "email", "phone"]

    # 支持的确认方式
    CONFIRMATION_METHODS = ["password", "sms", "email", "totp"]

    # 支持的MFA类型
    MFA_TYPES = ["totp", "sms", "email"]

    def __init__(
            self,
            session: AsyncSession,
            redis_repo: RedisRepository,
            user_model: Type[User],
            tenant_model: Type[Tenant],
            role_model: Type[Role],
            permission_model: Type[Permission],
            user_role_model: Type[UserRole],
            role_permission_model: Type[RolePermission],
            user_session_history_model: Type[UserSessionHistory],
            user_mfa_model: Type[UserMFA],
            verification_code_model: Type[VerificationCode],
            password_history_model: Type[PasswordHistory],
            audit_log_model: Type[AuditLog],
            jwt_manager: JWTManager,
            session_manager: SessionManager,
            security_utils: SecurityUtils
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_repo = redis_repo

        # 核心业务模型
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.role_model = role_model
        self.permission_model = permission_model

        # 关联关系模型
        self.user_role_model = user_role_model
        self.role_permission_model = role_permission_model

        # 认证安全模型
        self.user_session_history_model = user_session_history_model
        self.user_mfa_model = user_mfa_model
        self.verification_code_model = verification_code_model
        self.password_history_model = password_history_model

        # 审计模型
        self.audit_log_model = audit_log_model

        # 安全组件
        self.jwt_manager = jwt_manager
        self.session_manager = session_manager
        self.security_utils = security_utils

        # 登录类型定义
        self.LOGIN_TYPES = {
            "username": "username",
            "email": "email",
            "phone": "phone"
        }

        # MFA类型定义
        self.MFA_TYPES = {
            "totp": "totp",
            "sms": "sms",
            "email": "email"
        }

        # 确认方式定义
        self.CONFIRMATION_METHODS = {
            "sms": "sms",
            "email": "email",
            "totp": "totp",
            "password": "password"
        }

    async def login(
            self,
            tenant_id: str,
            login_type: str,
            identifier: str,
            credential: str,
            remember_me: bool = False,
            mfa_code: Optional[str] = None,
            device_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        用户登录

        支持多种登录方式和安全验证的用户认证
        """
        try:
            # 验证租户存在性和状态
            tenant = await self._get_tenant_by_id(tenant_id)
            if not tenant or tenant.status != CommonStatus.ACTIVE:
                raise NotFoundError("租户不存在或未激活")

            # 根据登录类型查找用户
            user = await self._find_user_by_identifier(tenant_id, login_type, identifier)
            if not user:
                raise AuthenticationError("用户不存在")

            # 验证用户状态
            if user.status not in ["active"]:
                raise AuthenticationError("用户账户已被锁定或禁用")

            # 验证登录凭证（密码或验证码）
            if not await self._verify_credential(user, credential, login_type):
                # await self._handle_login_failure(user)
                raise AuthenticationError("用户名或密码错误")

            # 检查登录频率限制
            await self._check_login_rate_limit(user.user_id, device_info)

            # 验证MFA（如果启用）
            # if user.mfa_enabled and not await self._verify_mfa(user, mfa_code):
            #     raise AuthenticationError("MFA验证失败")

            # 生成会话和令牌
            session_id = await self._create_user_session(user, device_info, remember_me)
            token_pair = await self._generate_tokens(user, session_id, remember_me)

            # 更新用户登录信息
            # await self._update_user_login_info(user)

            # 记录登录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user.user_id,
                action="LOGIN",
                resource_type="AUTH",
                details={"login_type": login_type, "device_info": device_info}
            )

            # 获取用户角色和权限
            user_roles = await self._get_user_roles(user.user_id)
            user_permissions = await self._get_user_permissions(user.user_id)

            # 生成安全警告
            # security_warnings = await self._generate_security_warnings(user)

            return {
                "access_token": token_pair.access_token,
                "refresh_token": token_pair.refresh_token,
                "token_type": token_pair.token_type,
                "expires_in": token_pair.access_expires_in,
                "refresh_expires_in": token_pair.refresh_expires_in,
                "session_id": session_id,
                "user_info": {
                    "user_id": user.user_id,
                    "username": user.username,
                    "nickname": user.nickname,
                    "email": user.email,
                    "phone": user.phone,
                    "avatar": user.profile.get("avatar") if user.profile else None,
                    "roles": [role["role_name"] for role in user_roles],
                    "permissions": user_permissions,
                    "mfa_enabled": user.mfa_enabled or False,
                    "password_expires_in": self._calculate_password_expires_days(user)
                },
                "tenant_info": {
                    "tenant_id": tenant.tenant_id,
                    "tenant_name": tenant.tenant_name
                },
                "security_warnings": security_warnings
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (AuthenticationError, ValidationError)):
                raise e
            raise BusinessError(f"登录失败: {str(e)}")

    async def logout(
            self,
            tenant_id: str,
            session_id: str,
            logout_all_devices: bool = False
    ) -> Dict[str, Any]:
        """
        用户登出

        支持单设备和全设备登出的用户登出功能
        """
        try:
            # TODO: 验证会话有效性
            # session = await self._get_session_by_id(session_id)
            # if not session:
            #     raise NotFoundError("会话不存在")

            # TODO: 获取用户信息
            # user = await self._get_user_by_id(session.user_id)
            # if not user:
            #     raise NotFoundError("用户不存在")

            # TODO: 执行登出操作
            # if logout_all_devices:
            #     logged_out_sessions = await self._logout_all_sessions(user.user_id)
            # else:
            #     logged_out_sessions = await self._logout_session(session_id)

            # TODO: 清理令牌和缓存
            # await self._invalidate_tokens(session_id, logout_all_devices)

            # TODO: 记录登出审计日志
            # await self._create_audit_log(
            #     tenant_id=tenant_id,
            #     user_id=user.user_id,
            #     action="LOGOUT",
            #     resource_type="AUTH",
            #     details={"session_id": session_id, "logout_all_devices": logout_all_devices}
            # )

            # 临时返回模拟数据
            user_id = f"user_{uuid.uuid4()}"
            return {
                "user_id": user_id,
                "logout_time": datetime.utcnow().isoformat(),
                "logged_out_sessions": 3 if logout_all_devices else 1,
                "remaining_sessions": 0 if logout_all_devices else 2
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (NotFoundError, ValidationError)):
                raise e
            raise BusinessError(f"登出失败: {str(e)}")

    async def refresh_token(
            self,
            tenant_id: str,
            refresh_token: str,
            session_id: str
    ) -> Dict[str, Any]:
        """
        刷新令牌

        使用刷新令牌获取新的访问令牌
        """
        try:
            # TODO: 验证刷新令牌
            # token_payload = await self.jwt_manager.verify_refresh_token(refresh_token)
            # if not token_payload:
            #     raise AuthenticationError("刷新令牌无效")

            # TODO: 验证会话状态
            # session = await self._get_session_by_id(session_id)
            # if not session or not session.is_active:
            #     raise AuthenticationError("会话已过期")

            # TODO: 验证设备信息
            # if not await self._verify_device_info(session, token_payload):
            #     raise AuthenticationError("设备信息不匹配")

            # TODO: 生成新的令牌对
            # new_token_pair = await self.jwt_manager.refresh_tokens(refresh_token)

            # TODO: 更新会话活动时间
            # await self._update_session_activity(session_id)

            # TODO: 记录令牌刷新日志
            # await self._create_audit_log(
            #     tenant_id=tenant_id,
            #     user_id=token_payload.user_id,
            #     action="REFRESH_TOKEN",
            #     resource_type="AUTH",
            #     details={"session_id": session_id}
            # )

            # 临时返回模拟数据
            return {
                "access_token": f"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.{uuid.uuid4()}",
                "refresh_token": f"refresh_token_{uuid.uuid4()}",
                "token_type": "Bearer",
                "expires_in": 7200,
                "refresh_expires_in": 86400,
                "issued_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            if isinstance(e, (AuthenticationError, ValidationError)):
                raise e
            raise BusinessError(f"令牌刷新失败: {str(e)}")

    async def change_password(
            self,
            tenant_id: str,
            user_id: str,
            old_password: str,
            new_password: str,
            logout_other_sessions: bool = True
    ) -> Dict[str, Any]:
        """
        修改密码

        用户主动修改密码功能
        """
        try:
            # TODO: 获取用户信息
            # user = await self._get_user_by_id(user_id)
            # if not user:
            #     raise NotFoundError("用户不存在")

            # TODO: 验证原密码
            # if not self.security_utils.verify_password(old_password, user.password_hash):
            #     raise AuthenticationError("原密码错误")

            # TODO: 验证新密码策略
            # await self._validate_password_policy(tenant_id, new_password)

            # TODO: 检查密码历史
            # if await self._is_password_in_history(user_id, new_password):
            #     raise ValidationError("新密码不能与历史密码重复")

            # TODO: 更新密码
            # new_password_hash = self.security_utils.hash_password(new_password)
            # user.password_hash = new_password_hash
            # user.password_changed_at = datetime.utcnow()
            # user.password_expires_at = datetime.utcnow() + timedelta(days=90)
            # await self.session.commit()

            # TODO: 添加密码历史记录
            # await self._add_password_history(user_id, new_password_hash)

            # TODO: 登出其他会话（如果需要）
            # sessions_terminated = 0
            # if logout_other_sessions:
            #     sessions_terminated = await self._logout_other_sessions(user_id)

            # TODO: 记录密码修改日志
            # await self._create_audit_log(
            #     tenant_id=tenant_id,
            #     user_id=user_id,
            #     action="CHANGE_PASSWORD",
            #     resource_type="AUTH",
            #     details={"logout_other_sessions": logout_other_sessions}
            # )

            # 临时返回模拟数据
            return {
                "user_id": user_id,
                "password_changed_at": datetime.utcnow().isoformat(),
                "password_expires_at": (datetime.utcnow() + timedelta(days=90)).isoformat(),
                "sessions_terminated": 3 if logout_other_sessions else 0
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (NotFoundError, AuthenticationError, ValidationError)):
                raise e
            raise BusinessError(f"修改密码失败: {str(e)}")

    async def forgot_password(
            self,
            tenant_id: str,
            identifier: str,
            identifier_type: str,
            captcha_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        忘记密码

        发起密码重置流程
        """
        try:
            # TODO: 验证图形验证码（防暴力破解）
            # if captcha_token and not await self._verify_captcha(captcha_token):
            #     raise ValidationError("图形验证码错误")

            # TODO: 根据标识符类型查找用户
            # user = await self._find_user_by_identifier(tenant_id, identifier_type, identifier)
            # if not user:
            #     # 为了安全，不暴露用户是否存在
            #     pass

            # TODO: 检查重置频率限制
            # await self._check_reset_rate_limit(identifier)

            # TODO: 生成重置令牌
            # reset_token = f"reset_{uuid.uuid4()}"
            # reset_token_id = f"reset_token_{uuid.uuid4()}"

            # TODO: 存储重置令牌到Redis
            # await self.redis_repo.set(
            #     f"reset_token:{reset_token_id}",
            #     {"user_id": user.user_id if user else None, "identifier": identifier},
            #     ttl=1800  # 30分钟过期
            # )

            # TODO: 发送重置邮件或短信
            # if user:
            #     if identifier_type == "email":
            #         await self._send_reset_email(user.email, reset_token)
            #     elif identifier_type == "phone":
            #         await self._send_reset_sms(user.phone, reset_token)

            # TODO: 记录重置请求日志
            # await self._create_audit_log(
            #     tenant_id=tenant_id,
            #     user_id=user.user_id if user else None,
            #     action="FORGOT_PASSWORD",
            #     resource_type="AUTH",
            #     details={"identifier_type": identifier_type, "identifier": identifier}
            # )

            # 临时返回模拟数据
            reset_token_id = f"reset_{uuid.uuid4()}"
            masked_target = self._mask_identifier(identifier, identifier_type)

            return {
                "reset_token_id": reset_token_id,
                "expires_in": 1800,
                "sent_to": masked_target
            }

        except Exception as e:
            if isinstance(e, (ValidationError, NotFoundError)):
                raise e
            raise BusinessError(f"忘记密码处理失败: {str(e)}")

    async def reset_password(
            self,
            reset_token: str,
            new_password: str,
            confirm_password: str
    ) -> Dict[str, Any]:
        """
        重置密码

        通过重置令牌重置用户密码
        """
        try:
            # TODO: 验证密码确认
            if new_password != confirm_password:
                raise ValidationError("两次输入的密码不一致")

            # TODO: 验证重置令牌
            # reset_data = await self.redis_repo.get(f"reset_token:{reset_token}")
            # if not reset_data:
            #     raise ValidationError("重置令牌无效或已过期")

            # TODO: 获取用户信息
            # user = await self._get_user_by_id(reset_data["user_id"])
            # if not user:
            #     raise NotFoundError("用户不存在")

            # TODO: 验证新密码策略
            # await self._validate_password_policy(user.tenant_id, new_password)

            # TODO: 更新密码
            # new_password_hash = self.security_utils.hash_password(new_password)
            # user.password_hash = new_password_hash
            # user.password_changed_at = datetime.utcnow()
            # user.password_expires_at = datetime.utcnow() + timedelta(days=90)
            # await self.session.commit()

            # TODO: 删除重置令牌
            # await self.redis_repo.delete(f"reset_token:{reset_token}")

            # TODO: 强制所有会话下线
            # await self._logout_all_sessions(user.user_id)

            # TODO: 记录密码重置日志
            # await self._create_audit_log(
            #     tenant_id=user.tenant_id,
            #     user_id=user.user_id,
            #     action="RESET_PASSWORD",
            #     resource_type="AUTH",
            #     details={"reset_token": reset_token}
            # )

            # 临时返回模拟数据
            user_id = f"user_{uuid.uuid4()}"
            return {
                "user_id": user_id,
                "password_reset_at": datetime.utcnow().isoformat(),
                "all_sessions_terminated": True
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (ValidationError, NotFoundError)):
                raise e
            raise BusinessError(f"重置密码失败: {str(e)}")

    async def setup_mfa(
            self,
            tenant_id: str,
            user_id: str,
            mfa_type: str,
            device_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        设置多因子认证

        初始化MFA设置流程
        """
        try:
            # TODO: 获取用户信息
            # user = await self._get_user_by_id(user_id)
            # if not user:
            #     raise NotFoundError("用户不存在")

            # TODO: 检查MFA是否已启用
            # if user.mfa_enabled:
            #     raise ValidationError("MFA已启用")

            # TODO: 根据MFA类型生成设置信息
            # if mfa_type == "totp":
            #     secret_key = self.security_utils.generate_totp_secret()
            #     qr_code_url = await self._generate_qr_code(user, secret_key)
            # else:
            #     secret_key = None
            #     qr_code_url = None

            # TODO: 生成备用恢复码
            # backup_codes = [self.security_utils.generate_backup_code() for _ in range(10)]

            # TODO: 生成设置令牌
            # setup_token = f"setup_{uuid.uuid4()}"
            # await self.redis_repo.set(
            #     f"mfa_setup:{setup_token}",
            #     {
            #         "user_id": user_id,
            #         "mfa_type": mfa_type,
            #         "secret_key": secret_key,
            #         "backup_codes": backup_codes,
            #         "device_name": device_name
            #     },
            #     ttl=600  # 10分钟过期
            # )

            # TODO: 记录MFA设置日志
            # await self._create_audit_log(
            #     tenant_id=tenant_id,
            #     user_id=user_id,
            #     action="SETUP_MFA",
            #     resource_type="AUTH",
            #     details={"mfa_type": mfa_type, "device_name": device_name}
            # )

            # 临时返回模拟数据
            setup_token = f"setup_{uuid.uuid4()}"
            return {
                "secret_key": "JBSWY3DPEHPK3PXP",
                "qr_code_url": f"https://domain.com/qr/mfa_{uuid.uuid4()}.png",
                "backup_codes": ["123456", "789012", "345678", "901234", "567890"],
                "setup_token": setup_token
            }

        except Exception as e:
            if isinstance(e, (NotFoundError, ValidationError)):
                raise e
            raise BusinessError(f"设置MFA失败: {str(e)}")

    async def verify_mfa(
            self,
            tenant_id: str,
            user_id: str,
            setup_token: str,
            verification_code: str
    ) -> Dict[str, Any]:
        """
        验证并启用MFA

        验证MFA设置并启用多因子认证
        """
        try:
            # TODO: 验证设置令牌
            # setup_data = await self.redis_repo.get(f"mfa_setup:{setup_token}")
            # if not setup_data:
            #     raise ValidationError("设置令牌无效或已过期")

            # TODO: 验证用户ID匹配
            # if setup_data["user_id"] != user_id:
            #     raise ValidationError("用户ID不匹配")

            # TODO: 验证MFA验证码
            # if setup_data["mfa_type"] == "totp":
            #     if not self.security_utils.verify_totp(setup_data["secret_key"], verification_code):
            #         raise ValidationError("验证码错误")

            # TODO: 启用MFA
            # user = await self._get_user_by_id(user_id)
            # user.mfa_enabled = True
            # await self.session.commit()

            # TODO: 保存MFA配置
            # mfa_config = self.user_mfa_model(
            #     user_id=user_id,
            #     mfa_type=setup_data["mfa_type"],
            #     secret_key=setup_data["secret_key"],
            #     backup_codes=setup_data["backup_codes"],
            #     device_name=setup_data["device_name"],
            #     enabled_at=datetime.utcnow()
            # )
            # self.session.add(mfa_config)
            # await self.session.commit()

            # TODO: 删除设置令牌
            # await self.redis_repo.delete(f"mfa_setup:{setup_token}")

            # TODO: 记录MFA启用日志
            # await self._create_audit_log(
            #     tenant_id=tenant_id,
            #     user_id=user_id,
            #     action="ENABLE_MFA",
            #     resource_type="AUTH",
            #     details={"mfa_type": setup_data["mfa_type"]}
            # )

            # 临时返回模拟数据
            return {
                "user_id": user_id,
                "mfa_enabled": True,
                "enabled_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (ValidationError, NotFoundError)):
                raise e
            raise BusinessError(f"启用MFA失败: {str(e)}")

    async def list_sessions(
            self,
            tenant_id: str,
            user_id: str,
            include_expired: bool = False
    ) -> Dict[str, Any]:
        """
        查看用户会话

        获取用户的活跃会话列表
        """
        try:
            # TODO: 获取用户会话列表
            # sessions = await self._get_user_sessions(user_id, include_expired)

            # TODO: 格式化会话信息
            # session_list = []
            # for session in sessions:
            #     session_info = {
            #         "session_id": session.session_id,
            #         "device_info": {
            #             "device_name": session.device_name,
            #             "os": session.os,
            #             "browser": session.browser,
            #             "ip_address": session.ip_address
            #         },
            #         "created_at": session.created_at.isoformat(),
            #         "last_activity": session.last_activity.isoformat(),
            #         "expires_at": session.expires_at.isoformat(),
            #         "is_current": session.is_current
            #     }
            #     session_list.append(session_info)

            # 临时返回模拟数据
            session_list = [
                {
                    "session_id": f"session_{uuid.uuid4()}",
                    "device_info": {
                        "device_name": "iPhone 15",
                        "os": "iOS 17.0",
                        "browser": "Safari",
                        "ip_address": "*************"
                    },
                    "created_at": (datetime.utcnow() - timedelta(hours=2)).isoformat(),
                    "last_activity": datetime.utcnow().isoformat(),
                    "expires_at": (datetime.utcnow() + timedelta(hours=6)).isoformat(),
                    "is_current": True
                },
                {
                    "session_id": f"session_{uuid.uuid4()}",
                    "device_info": {
                        "device_name": "MacBook Pro",
                        "os": "macOS 14.0",
                        "browser": "Chrome",
                        "ip_address": "*************"
                    },
                    "created_at": (datetime.utcnow() - timedelta(days=1)).isoformat(),
                    "last_activity": (datetime.utcnow() - timedelta(hours=3)).isoformat(),
                    "expires_at": (datetime.utcnow() + timedelta(hours=3)).isoformat(),
                    "is_current": False
                }
            ]

            return {
                "user_id": user_id,
                "active_sessions": session_list,
                "total_sessions": len(session_list)
            }

        except Exception as e:
            raise BusinessError(f"查询会话失败: {str(e)}")

    async def terminate_session(
            self,
            tenant_id: str,
            user_id: str,
            session_id: str,
            reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        强制下线会话

        强制终止指定的用户会话
        """
        try:
            # TODO: 验证会话存在性
            # session = await self._get_session_by_id(session_id)
            # if not session:
            #     raise NotFoundError("会话不存在")

            # TODO: 验证会话所有权
            # if session.user_id != user_id:
            #     raise AuthorizationError("无权限操作此会话")

            # TODO: 终止会话
            # await self._terminate_session(session_id)

            # TODO: 清理相关令牌和缓存
            # await self._invalidate_session_tokens(session_id)

            # TODO: 记录会话终止日志
            # await self._create_audit_log(
            #     tenant_id=tenant_id,
            #     user_id=user_id,
            #     action="TERMINATE_SESSION",
            #     resource_type="AUTH",
            #     details={"session_id": session_id, "reason": reason}
            # )

            # 临时返回模拟数据
            return {
                "session_id": session_id,
                "terminated_at": datetime.utcnow().isoformat(),
                "reason": reason
            }

        except Exception as e:
            if isinstance(e, (NotFoundError, AuthorizationError)):
                raise e
            raise BusinessError(f"强制下线会话失败: {str(e)}")

    async def verify_code(
            self,
            code_id: str,
            verification_code: str,
            code_type: str
    ) -> Dict[str, Any]:
        """
        验证验证码

        验证各种类型的验证码
        """
        try:
            # TODO: 获取验证码信息
            # code_data = await self.redis_repo.get(f"verification_code:{code_id}")
            # if not code_data:
            #     raise ValidationError("验证码不存在或已过期")

            # TODO: 验证验证码
            # if code_data["code"] != verification_code:
            #     # 增加失败次数
            #     await self._increment_verification_failures(code_id)
            #     raise ValidationError("验证码错误")

            # TODO: 检查验证码类型
            # if code_data["type"] != code_type:
            #     raise ValidationError("验证码类型不匹配")

            # TODO: 标记验证码已使用
            # await self.redis_repo.delete(f"verification_code:{code_id}")

            # TODO: 记录验证成功日志
            # await self._create_audit_log(
            #     tenant_id=code_data.get("tenant_id"),
            #     user_id=code_data.get("user_id"),
            #     action="VERIFY_CODE",
            #     resource_type="AUTH",
            #     details={"code_type": code_type, "code_id": code_id}
            # )

            # 临时返回模拟数据
            return {
                "code_id": code_id,
                "verified": True,
                "verified_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            if isinstance(e, ValidationError):
                raise e
            raise BusinessError(f"验证验证码失败: {str(e)}")

    async def confirm_operation(
            self,
            tenant_id: str,
            user_id: str,
            operation_type: str,
            operation_data: Dict[str, Any],
            confirmation_method: str,
            verification_code: str
    ) -> Dict[str, Any]:
        """
        敏感操作确认

        对敏感操作进行二次确认
        """
        try:
            # 验证确认方式
            if confirmation_method not in self.CONFIRMATION_METHODS:
                raise ValidationError("不支持的确认方式")

            # 根据确认方式验证
            if confirmation_method == "password":
                user = await self._get_user_by_id(user_id)
                if not user:
                    raise NotFoundError("用户不存在")
                if not self.security_utils.verify_password(verification_code, user.password_hash):
                    raise AuthenticationError("密码错误")
            elif confirmation_method in ["sms", "email", "totp"]:
                # 验证验证码
                await self._verify_confirmation_code(user_id, confirmation_method, verification_code)

            # 生成操作令牌
            operation_token = f"op_token_{uuid.uuid4()}"
            await self.redis_repo.set(
                f"operation_token:{operation_token}",
                {
                    "user_id": user_id,
                    "tenant_id": tenant_id,
                    "operation_type": operation_type,
                    "operation_data": operation_data,
                    "confirmed_at": datetime.utcnow().isoformat()
                },
                ttl=300  # 5分钟过期
            )

            # 记录操作确认日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="CONFIRM_OPERATION",
                resource_type="AUTH",
                details={
                    "operation_type": operation_type,
                    "confirmation_method": confirmation_method,
                    "operation_token": operation_token
                }
            )

            # 临时返回模拟数据
            operation_token = f"op_token_{uuid.uuid4()}"
            return {
                "operation_token": operation_token,
                "expires_in": 300,
                "confirmed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            if isinstance(e, (ValidationError, AuthenticationError)):
                raise e
            raise BusinessError(f"操作确认失败: {str(e)}")

    # ===== 辅助方法 =====

    def _mask_identifier(self, identifier: str, identifier_type: str) -> str:
        """脱敏显示标识符"""
        if identifier_type == "email":
            parts = identifier.split("@")
            if len(parts) == 2:
                username = parts[0]
                domain = parts[1]
                masked_username = username[:2] + "****" if len(username) > 2 else "****"
                return f"{masked_username}@{domain}"
        elif identifier_type == "phone":
            if len(identifier) >= 7:
                return f"{identifier[:3]}****{identifier[-4:]}"
        return "****"

    # ===== 辅助方法实现 =====

    async def _get_tenant_by_id(self, tenant_id: str):
        """根据ID获取租户"""
        stmt = select(self.tenant_model).where(self.tenant_model.tenant_id == tenant_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _find_user_by_identifier(self, tenant_id: str, login_type: str, identifier: str):
        """根据标识符查找用户"""
        if login_type == "username":
            condition = self.user_model.username == identifier
        elif login_type == "email":
            condition = self.user_model.email == identifier
        elif login_type == "phone":
            condition = self.user_model.phone == identifier
        else:
            raise ValidationError("不支持的登录类型")

        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                condition,
                self.user_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _verify_credential(self, user, credential: str, login_type: str) -> bool:
        """验证登录凭证"""
        # 对于密码登录，验证密码
        if login_type in ["username", "email", "phone"]:
            return self.security_utils.verify_password(credential, user.password_hash)

        # TODO: 支持其他验证方式（如验证码登录）
        return False

    async def _handle_login_failure(self, user):
        """处理登录失败"""
        # 增加失败次数
        user.failed_login_attempts = (user.failed_login_attempts or 0) + 1
        user.last_failed_login = datetime.utcnow()

        # 如果失败次数过多，锁定账户
        if user.failed_login_attempts >= 5:
            user.locked_until = datetime.utcnow() + timedelta(minutes=30)
            user.status = "locked"

        await self.session.commit()

    async def _check_login_rate_limit(self, user_id: str, device_info: Optional[Dict[str, Any]]):
        """检查登录频率限制"""
        # 检查用户登录频率
        rate_limit_key = f"login_rate_limit:{user_id}"
        current_attempts = await self.redis_repo.get(rate_limit_key) or 0

        if current_attempts >= 10:  # 每小时最多10次登录尝试
            raise AuthenticationError("登录频率过高，请稍后再试")

        # 增加尝试次数
        await self.redis_repo.set(rate_limit_key, current_attempts + 1, ttl=3600)

    async def _verify_mfa(self, user, mfa_code: Optional[str]) -> bool:
        """验证MFA"""
        if not mfa_code:
            return False

        # 获取用户MFA配置
        mfa_config = await self._get_user_mfa(user.user_id)
        if not mfa_config:
            return False

        # 根据MFA类型进行验证
        if mfa_config.mfa_type == "totp":
            return self.security_utils.verify_totp(mfa_config.secret_key, mfa_code)
        elif mfa_config.mfa_type == "sms":
            # TODO: 实现SMS MFA验证
            # 这里应该验证短信验证码
            return await self._verify_sms_mfa(user.phone, mfa_code)
        elif mfa_config.mfa_type == "email":
            # TODO: 实现Email MFA验证
            # 这里应该验证邮箱验证码
            return await self._verify_email_mfa(user.email, mfa_code)

        return False

    async def _create_user_session(self, user, device_info: Optional[Dict[str, Any]], remember_me: bool) -> str:
        """创建用户会话"""
        from security.session_manager import DeviceInfo

        # 构造设备信息
        device = DeviceInfo(
            device_name=device_info.get("device_name", "Unknown") if device_info else "Unknown",
            os=device_info.get("os", "Unknown") if device_info else "Unknown",
            browser=device_info.get("browser") if device_info else None,
            ip_address=device_info.get("ip_address", "0.0.0.0") if device_info else "0.0.0.0",
            user_agent=device_info.get("user_agent", "") if device_info else ""
        )

        # 创建会话
        session_info = await self.session_manager.create_session(
            user_id=user.user_id,
            tenant_id=user.tenant_id,
            device_info=device,
            remember_me=remember_me
        )

        return session_info.session_id

    async def _generate_tokens(self, user, session_id: str, remember_me: bool):
        """生成JWT令牌对"""
        # 获取用户角色和权限
        user_roles = await self._get_user_roles(user.user_id)
        user_permissions = await self._get_user_permissions(user.user_id)

        # 生成令牌对
        return self.jwt_manager.generate_token_pair(
            user_id=user.user_id,
            tenant_id=user.tenant_id,
            session_id=session_id,
            username=user.username,
            email=user.email,
            roles=[role["role_name"] for role in user_roles],
            permissions=user_permissions,
            remember_me=remember_me
        )

    async def _update_user_login_info(self, user):
        """更新用户登录信息"""
        user.last_login = datetime.utcnow()
        user.login_count = (user.login_count or 0) + 1
        user.failed_login_attempts = 0  # 重置失败次数
        user.locked_until = None  # 解除锁定

        await self.session.commit()

    async def _get_user_roles(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户角色"""
        try:
            # 查询用户角色关联
            stmt = (
                select(self.user_role_model, self.role_model)
                .join(self.role_model, self.user_role_model.role_id == self.role_model.role_id)
                .where(
                    and_(
                        self.user_role_model.user_id == user_id,
                        self.user_role_model.status == CommonStatus.ACTIVE,
                        self.role_model.status == CommonStatus.ACTIVE,
                        or_(
                            self.user_role_model.expiry_date.is_(None),
                            self.user_role_model.expiry_date > datetime.utcnow().date()
                        )
                    )
                )
            )
            result = await self.session.execute(stmt)
            user_roles = result.all()

            return [
                {
                    "role_id": user_role.Role.role_id,
                    "role_name": user_role.Role.role_name,
                    "role_code": user_role.Role.role_code
                }
                for user_role in user_roles
            ]
        except Exception as e:
            # 如果查询失败，返回默认角色
            print(f"获取用户角色失败: {e}")
            return [{"role_name": "USER", "role_id": f"role_{uuid.uuid4()}"}]

    async def _get_user_permissions(self, user_id: str) -> List[str]:
        """获取用户权限"""
        try:
            # 通过用户角色获取权限
            stmt = (
                select(self.permission_model.permission_code)
                .join(self.role_permission_model,
                      self.permission_model.permission_id == self.role_permission_model.permission_id)
                .join(self.user_role_model,
                      self.role_permission_model.role_id == self.user_role_model.role_id)
                .where(
                    and_(
                        self.user_role_model.user_id == user_id,
                        self.user_role_model.status == CommonStatus.ACTIVE,
                        self.role_permission_model.status == CommonStatus.ACTIVE,
                        self.permission_model.status == CommonStatus.ACTIVE,
                        or_(
                            self.user_role_model.expiry_date.is_(None),
                            self.user_role_model.expiry_date > datetime.utcnow().date()
                        )
                    )
                )
                .distinct()
            )
            result = await self.session.execute(stmt)
            permissions = result.scalars().all()

            return list(permissions)
        except Exception as e:
            # 如果查询失败，返回基础权限
            print(f"获取用户权限失败: {e}")
            return ["user:read", "document:read"]

    async def _generate_security_warnings(self, user) -> List[Dict[str, Any]]:
        """生成安全警告"""
        warnings = []

        # 检查密码过期
        if user.password_expires_at:
            days_until_expire = (user.password_expires_at - datetime.utcnow()).days
            if days_until_expire <= 30:
                warnings.append({
                    "type": "password_expiring",
                    "message": f"密码将在{days_until_expire}天后过期",
                    "action_required": days_until_expire <= 7
                })

        # 检查是否启用MFA
        if not user.mfa_enabled:
            warnings.append({
                "type": "mfa_not_enabled",
                "message": "建议启用多因子认证以提高账户安全性",
                "action_required": False
            })

        return warnings

    async def _calculate_password_expires_days(self, user) -> Optional[int]:
        """计算密码过期天数"""
        if user.password_expires_at:
            days = (user.password_expires_at - datetime.utcnow()).days
            return max(0, days)
        return None

    async def _create_audit_log(self, tenant_id: str, user_id: str, action: str, resource_type: str,
                                details: Dict[str, Any]):
        """创建审计日志"""
        try:
            # 使用AuditLogBuilder创建审计日志
            audit_log = AuditLogBuilder.create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=user_id,
                result="success",
                details=details or {}
            )

            self.session.add(audit_log)
            # 注意：这里不提交，由调用方决定何时提交

        except Exception as e:
            # 审计日志失败不应该影响主业务
            print(f"审计日志记录失败: {str(e)}")

    async def _clear_backup_codes(self, user_id: str):
        """清除用户的备用恢复码"""
        # TODO: 实现备用恢复码清除逻辑
        # 这里应该从Redis或数据库中清除备用恢复码
        cache_key = f"mfa_backup_codes:{user_id}"
        await self.redis_repo.delete(cache_key)

    async def _get_user_by_id(self, user_id: str):
        """根据ID获取用户"""
        stmt = select(self.user_model).where(
            and_(
                self.user_model.user_id == user_id,
                self.user_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_user_mfa(self, user_id: str):
        """获取用户MFA配置"""
        stmt = select(self.user_mfa_model).where(
            and_(
                self.user_mfa_model.user_id == user_id,
                self.user_mfa_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _validate_password_policy(self, password: str, tenant_id: str) -> bool:
        """验证密码策略"""
        try:
            # TODO: 从数据库获取租户的密码策略配置
            # 这里使用默认的密码策略
            policy = {
                "min_length": 8,
                "require_uppercase": True,
                "require_lowercase": True,
                "require_numbers": True,
                "require_symbols": False,
                "max_length": 128,
                "forbidden_patterns": ["123456", "password", "admin"]
            }

            # 长度检查
            if len(password) < policy["min_length"]:
                raise ValidationError(f"密码长度至少{policy['min_length']}位")

            if len(password) > policy["max_length"]:
                raise ValidationError(f"密码长度不能超过{policy['max_length']}位")

            # 字符类型检查
            if policy["require_uppercase"] and not any(c.isupper() for c in password):
                raise ValidationError("密码必须包含大写字母")

            if policy["require_lowercase"] and not any(c.islower() for c in password):
                raise ValidationError("密码必须包含小写字母")

            if policy["require_numbers"] and not any(c.isdigit() for c in password):
                raise ValidationError("密码必须包含数字")

            if policy["require_symbols"] and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
                raise ValidationError("密码必须包含特殊字符")

            # 禁用模式检查
            password_lower = password.lower()
            for pattern in policy["forbidden_patterns"]:
                if pattern in password_lower:
                    raise ValidationError(f"密码不能包含常见模式: {pattern}")

            return True

        except ValidationError:
            raise
        except Exception as e:
            print(f"密码策略验证失败: {e}")
            # 如果策略验证失败，使用基本验证
            if len(password) < 8:
                raise ValidationError("密码长度至少8位")
            return True

    async def _send_verification_code(self, identifier: str, identifier_type: str, code: str) -> bool:
        """发送验证码"""
        try:
            if identifier_type == "email":
                # TODO: 集成邮件服务发送验证码
                # 这里应该调用邮件服务API
                print(f"发送邮件验证码 {code} 到 {identifier}")
                # 模拟邮件发送
                # await email_service.send_verification_code(identifier, code)

            elif identifier_type == "phone":
                # TODO: 集成短信服务发送验证码
                # 这里应该调用短信服务API
                print(f"发送短信验证码 {code} 到 {identifier}")
                # 模拟短信发送
                # await sms_service.send_verification_code(identifier, code)

            else:
                raise ValidationError(f"不支持的验证码发送类型: {identifier_type}")

            # 将验证码存储到Redis，设置5分钟过期
            code_key = f"verification_code:{identifier}:{identifier_type}"
            await self.redis_repo.set(
                code_key,
                {
                    "code": code,
                    "identifier": identifier,
                    "type": identifier_type,
                    "created_at": datetime.utcnow().isoformat(),
                    "attempts": 0
                },
                ttl=300  # 5分钟过期
            )

            return True

        except Exception as e:
            print(f"发送验证码失败: {e}")
            return False

    async def _verify_reset_token(self, reset_token: str) -> Dict[str, Any]:
        """验证重置令牌"""
        # 从Redis获取重置令牌信息
        token_key = f"reset_token:{reset_token}"
        token_data = await self.redis_repo.get(token_key)

        if not token_data:
            raise ValidationError("重置令牌无效或已过期")

        return token_data

    async def _verify_confirmation_code(self, user_id: str, confirmation_method: str, verification_code: str):
        """验证确认验证码"""
        if confirmation_method == "totp":
            # 获取用户MFA配置
            mfa_config = await self._get_user_mfa(user_id)
            if not mfa_config or mfa_config.mfa_type != "totp":
                raise ValidationError("用户未启用TOTP MFA")

            # 验证TOTP验证码
            if not self.security_utils.verify_totp(mfa_config.secret_key, verification_code):
                raise AuthenticationError("TOTP验证码错误")

        elif confirmation_method in ["sms", "email"]:
            # TODO: 实现SMS和Email验证码验证
            # 这里应该从Redis或数据库中获取验证码进行验证
            # 暂时模拟验证成功
            print(f"验证 {confirmation_method} 验证码: {verification_code}")

        else:
            raise ValidationError(f"不支持的确认方式: {confirmation_method}")

    async def _verify_sms_mfa(self, phone: str, mfa_code: str) -> bool:
        """验证SMS MFA"""
        # TODO: 实现SMS MFA验证逻辑
        # 这里应该从Redis或数据库中获取发送的验证码进行验证
        # 暂时模拟验证成功
        print(f"验证SMS MFA: {phone} - {mfa_code}")
        return True

    async def _verify_email_mfa(self, email: str, mfa_code: str) -> bool:
        """验证Email MFA"""
        # TODO: 实现Email MFA验证逻辑
        # 这里应该从Redis或数据库中获取发送的验证码进行验证
        # 暂时模拟验证成功
        print(f"验证Email MFA: {email} - {mfa_code}")
        return True

    async def disable_mfa(
            self,
            tenant_id: str,
            user_id: str,
            password: str,
            mfa_code: Optional[str] = None,
            reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        禁用多因子认证

        验证用户身份后禁用MFA功能
        """
        try:
            # 验证用户存在
            user = await self._get_user_by_id(user_id)
            if not user or user.tenant_id != tenant_id:
                raise NotFoundError("用户不存在")

            # 验证用户密码
            if not SecurityUtils.verify_password(password, user.password_hash):
                raise AuthenticationError("密码错误")

            # 检查用户是否启用了MFA
            if not user.mfa_enabled:
                raise ValidationError("用户未启用MFA")

            # 获取MFA配置
            mfa_config = await self._get_user_mfa(user_id)
            if not mfa_config:
                raise NotFoundError("MFA配置不存在")

            # 如果提供了MFA验证码，需要验证
            if mfa_code:
                if mfa_config.mfa_type == "totp":
                    if not SecurityUtils.verify_totp(mfa_config.secret_key, mfa_code):
                        raise AuthenticationError("MFA验证码错误")
                # TODO: 实现SMS和Email MFA验证

            # 禁用MFA
            user.mfa_enabled = False
            user.updated_at = datetime.utcnow()

            # 删除MFA配置
            mfa_config.status = CommonStatus.INACTIVE
            mfa_config.updated_at = datetime.utcnow()

            # 清除备用恢复码
            await self._clear_backup_codes(user_id)

            await self.session.commit()

            # 创建审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="DISABLE_MFA",
                resource_type="USER_MFA",
                resource_id=user_id,
                details={
                    "reason": reason,
                    "disabled_at": datetime.utcnow().isoformat()
                }
            )

            return {
                "user_id": user_id,
                "mfa_enabled": False,
                "disabled_at": datetime.utcnow().isoformat(),
                "reason": reason
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (NotFoundError, AuthenticationError, ValidationError)):
                raise e
            raise BusinessError(f"禁用MFA失败: {str(e)}")
