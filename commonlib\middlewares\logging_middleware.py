import json
import time
import uuid
from typing import Any, Callable, Dict

from commonlib.core.context import recyclable_app_correlation_id
from commonlib.core.logging.tsif_logging import app_logger
from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class AsyncIteratorWrapper:
    """The following is a utility class that transforms a
    regular iterable to an asynchronous one.

    link: https://www.python.org/dev/peps/pep-0492/#example-2
    """

    def __init__(self, obj):
        self._it = iter(obj)

    def __aiter__(self):
        return self

    async def __anext__(self):
        try:
            value = next(self._it)
        except StopIteration:
            raise StopAsyncIteration
        return value


class LoggingMiddleware(BaseHTTPMiddleware):
    """统一的日志中间件，记录请求和响应信息"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并记录日志"""
        start_time = time.perf_counter()
        request_id = recyclable_app_correlation_id.get() or uuid.uuid4().hex
        recyclable_app_correlation_id.set(request_id)
        # 记录请求信息
        try:
            request_dict = await self._log_request(request)
            response = await call_next(request)
            response_dict = await self._process_response(response)

            # 计算请求处理时间
            cost_time = time.perf_counter() - start_time

            # 构建结构化日志
            log_data = {
                "type": "api_access",
                "method": request.method,
                "path": str(request.url.path),
                "query_params": str(request.query_params),
                "client_host": request.client.host if request.client else "unknown",
                "status_code": response.status_code,
                "cost_time": round(cost_time, 3),
                "request": request_dict,
                "response": response_dict,
                "request_id": request_id,
            }

            # 使用extra参数传递结构化数据
            app_logger.biz(log_data)

            return response

        except Exception as e:
            # 记录异常信息
            error_data = {
                "type": "api_error",
                "method": request.method,
                "path": str(request.url.path),
                "error": str(e),
                "request_id": request_id,
                "cost_time": round(time.perf_counter() - start_time, 3),
            }

            # 使用extra参数传递错误信息
            app_logger.error(
                "API Error:{}".format({"structured_data": error_data}), exception=True
            )
            # raise

    async def _log_request(self, request: Request) -> Dict[str, Any]:
        """处理并记录请求信息"""
        request_info = {
            "method": request.method,
            "path": str(request.url.path),
            "headers": self._filter_headers(dict(request.headers.items())),
            "client_host": request.client.host if request.client else "unknown",
            "body": None,
        }

        # 处理请求体
        content_type = request.headers.get("content-type", "").lower()
        try:
            if "services/json" in content_type:
                request_info["body"] = await request.json()
            elif "multipart/form-data" in content_type:
                request_info["body"] = "multipart/form-data content"
            elif "services/x-www-form-urlencoded" in content_type:
                form = await request.form()
                request_info["body"] = dict(form)
        except Exception as e:
            request_info["body"] = f"Error parsing request body: {str(e)}"

        return request_info

    @staticmethod
    def _filter_headers(headers: Dict[str, str]) -> Dict[str, str]:
        """过滤敏感header信息"""
        sensitive_headers = {"authorization", "cookie", "x-src-key"}
        return {
            k: v if k.lower() not in sensitive_headers else "[FILTERED]"
            for k, v in headers.items()
        }

    async def _process_response(self, response: Response) -> Dict[str, Any]:
        """处理响应信息"""
        response_info = {
            "status_code": response.status_code,
            "headers": self._filter_headers(dict(response.headers.items())),
            "body": None,
        }

        content_type = response.headers.get("content-type", "").lower()

        if "services/json" in content_type:
            try:
                body = []
                async for chunk in response.__dict__["body_iterator"]:
                    body.append(chunk)

                # 重新设置body_iterator以供后续使用
                response.__setattr__("body_iterator", AsyncIteratorWrapper(body))

                # 解析响应体
                if body:
                    content = body[0].decode("utf-8")
                    try:
                        parsed_body = json.loads(content)
                        # 如果响应体太大，只保留部分内容
                        if len(content) > 1000:
                            response_info["body"] = {
                                "truncated": True,
                                "preview": str(parsed_body)[:1000] + "...",
                                "total_length": len(content),
                            }
                        else:
                            response_info["body"] = parsed_body
                    except json.JSONDecodeError:
                        response_info["body"] = {
                            "raw": (
                                content[:1000] + "..."
                                if len(content) > 1000
                                else content
                            )
                        }
            except Exception as e:
                response_info["body"] = f"Error processing response body: {str(e)}"

        return response_info


def setup_logging_middleware(app: FastAPI, *args, **kwargs) -> None:
    app.add_middleware(LoggingMiddleware)
    app_logger.info("logging  middleware configured")
