"""
RBAC服务

提供基于角色的访问控制（RBAC）相关的业务逻辑实现
支持角色管理、权限管理、用户角色分配和权限检查
"""

import uuid
import json
from datetime import datetime, timedelta, date
from typing import Dict, Any, Optional, List, Type

from sqlalchemy import select, func, and_, or_, delete, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    DatabaseError, BusinessError, AuthorizationError
)
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission,
    AuditLog, AuditLogBuilder
)


class RBACService:
    """RBAC服务类"""

    def __init__(
        self,
        session: AsyncSession,
        redis_repo: RedisRepository,
        user_model: Type[User],
        tenant_model: Type[Tenant],
        role_model: Type[Role],
        permission_model: Type[Permission],
        user_role_model: Type[UserRole],
        role_permission_model: Type[RolePermission],
        audit_log_model: Type[AuditLog]
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_repo = redis_repo

        # 核心业务模型
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.role_model = role_model
        self.permission_model = permission_model

        # 关联关系模型
        self.user_role_model = user_role_model
        self.role_permission_model = role_permission_model

        # 审计模型
        self.audit_log_model = audit_log_model

        # 缓存键前缀
        self.role_cache_prefix = "role_info:"
        self.permission_cache_prefix = "permission_info:"
        self.user_roles_cache_prefix = "user_roles:"
        self.user_permissions_cache_prefix = "user_permissions:"
        self.role_permissions_cache_prefix = "role_permissions:"

    # ===== 角色管理 =====

    async def create_role(
        self,
        tenant_id: str,
        role_name: str,
        role_code: str,
        description: Optional[str] = None,
        parent_role_id: Optional[str] = None,
        level: int = 1,
        is_system: bool = False,
        permissions: List[str] = None
    ) -> Dict[str, Any]:
        """
        创建角色
        
        创建新角色并分配权限
        """
        try:
            # 验证租户存在性
            tenant = await self._get_tenant_by_id(tenant_id)
            if not tenant or tenant.status != CommonStatus.ACTIVE:
                raise NotFoundError("租户不存在或未激活")

            # 验证角色代码唯一性
            await self._validate_role_code_uniqueness(tenant_id, role_code)

            # 验证父角色和层级
            if parent_role_id:
                parent_role = await self._get_role_by_id(parent_role_id)
                if not parent_role or parent_role.tenant_id != tenant_id:
                    raise NotFoundError("父角色不存在")
                if level <= parent_role.level:
                    raise ValidationError("子角色层级必须大于父角色层级")

            # 生成角色ID
            role_id = f"role_{uuid.uuid4()}"

            # 创建角色记录
            role = self.role_model(
                role_id=role_id,
                tenant_id=tenant_id,
                role_name=role_name,
                role_code=role_code,
                description=description,
                parent_role_id=parent_role_id,
                level=level,
                status=CommonStatus.ACTIVE,
                meta_data={"is_system": is_system},
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            self.session.add(role)
            await self.session.flush()

            # 分配权限
            permissions_assigned = 0
            if permissions:
                permissions_assigned = await self._assign_permissions_to_role(
                    role_id, permissions, tenant_id
                )

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # 创建者ID
                action="CREATE_ROLE",
                resource_type="ROLE",
                resource_id=role_id,
                details={
                    "role_name": role_name,
                    "role_code": role_code,
                    "permissions_count": permissions_assigned
                }
            )

            await self.session.commit()

            # 缓存角色信息
            await self._cache_role_info(role_id, {
                "role_id": role_id,
                "role_name": role_name,
                "role_code": role_code,
                "tenant_id": tenant_id,
                "level": level,
                "is_system": is_system
            })

            return {
                "role_id": role_id,
                "role_name": role_name,
                "role_code": role_code,
                "created_at": role.created_at.isoformat(),
                "permissions_assigned": permissions_assigned
            }

        except IntegrityError as e:
            await self.session.rollback()
            if "role_code" in str(e):
                raise DuplicateResourceError("角色代码已存在")
            else:
                raise DatabaseError("数据库约束错误")
        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"创建角色失败: {str(e)}")

    async def query_roles(
        self,
        tenant_id: str,
        role_name: Optional[str] = None,
        parent_role_id: Optional[str] = None,
        level: Optional[int] = None,
        include_permissions: bool = False,
        include_users: bool = False,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        查询角色列表
        
        支持多条件查询和分页
        """
        try:
            # 构建查询条件
            conditions = [self.role_model.tenant_id == tenant_id]
            
            if role_name:
                conditions.append(self.role_model.role_name.ilike(f"%{role_name}%"))
            
            if parent_role_id:
                conditions.append(self.role_model.parent_role_id == parent_role_id)
            
            if level is not None:
                conditions.append(self.role_model.level == level)

            # 查询总数
            count_stmt = select(func.count(self.role_model.role_id)).where(and_(*conditions))
            total_result = await self.session.execute(count_stmt)
            total = total_result.scalar()

            # 分页查询
            offset = (page - 1) * page_size
            stmt = (
                select(self.role_model)
                .where(and_(*conditions))
                .order_by(self.role_model.level, self.role_model.created_at)
                .offset(offset)
                .limit(page_size)
            )
            result = await self.session.execute(stmt)
            roles = result.scalars().all()

            # 构建角色列表
            role_list = []
            for role in roles:
                # 从meta_data中获取is_system字段
                is_system = False
                if role.meta_data and isinstance(role.meta_data, dict):
                    is_system = role.meta_data.get("is_system", False)

                role_info = {
                    "role_id": role.role_id,
                    "role_name": role.role_name,
                    "role_code": role.role_code,
                    "description": role.description,
                    "parent_role_id": role.parent_role_id,
                    "level": role.level,
                    "is_system": is_system,
                    "status": role.status,
                    "created_at": role.created_at.isoformat(),
                    "updated_at": role.updated_at.isoformat() if role.updated_at else None
                }

                # 包含权限信息
                if include_permissions:
                    permissions = await self._get_role_permissions(role.role_id)
                    role_info["permissions"] = permissions

                # 包含用户数量
                if include_users:
                    user_count = await self._get_role_user_count(role.role_id)
                    role_info["user_count"] = user_count

                role_list.append(role_info)

            return {
                "roles": role_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "has_next": offset + page_size < total
            }

        except Exception as e:
            raise BusinessError(f"查询角色失败: {str(e)}")

    async def update_role(
        self,
        tenant_id: str,
        role_id: str,
        role_name: Optional[str] = None,
        description: Optional[str] = None,
        parent_role_id: Optional[str] = None,
        level: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        更新角色
        
        更新角色的基本信息和层级关系
        """
        try:
            # 获取角色
            role = await self._get_role_by_id(role_id)
            if not role or role.tenant_id != tenant_id:
                raise NotFoundError("角色不存在")

            # 检查系统角色保护
            is_system = False
            if role.meta_data and isinstance(role.meta_data, dict):
                is_system = role.meta_data.get("is_system", False)

            if is_system and (parent_role_id is not None or level is not None):
                raise ValidationError("系统角色的层级关系不可修改")

            # 验证父角色和层级
            if parent_role_id:
                parent_role = await self._get_role_by_id(parent_role_id)
                if not parent_role or parent_role.tenant_id != tenant_id:
                    raise NotFoundError("父角色不存在")
                if level and level <= parent_role.level:
                    raise ValidationError("子角色层级必须大于父角色层级")

            # 更新角色信息
            if role_name is not None:
                role.role_name = role_name
            if description is not None:
                role.description = description
            if parent_role_id is not None:
                role.parent_role_id = parent_role_id
            if level is not None:
                role.level = level
            
            role.updated_at = datetime.utcnow()

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="UPDATE_ROLE",
                resource_type="ROLE",
                resource_id=role_id,
                details={
                    "role_name": role_name,
                    "description": description,
                    "parent_role_id": parent_role_id,
                    "level": level
                }
            )

            await self.session.commit()

            # 清除缓存
            await self._clear_role_cache(role_id)

            return {
                "role_id": role_id,
                "role_name": role.role_name,
                "role_code": role.role_code,
                "created_at": role.created_at.isoformat(),
                "permissions_assigned": 0  # 更新操作不涉及权限
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更新角色失败: {str(e)}")

    async def delete_role(
        self,
        tenant_id: str,
        role_id: str,
        force_delete: bool = False,
        transfer_to_role_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        删除角色

        删除指定角色并处理用户关联
        """
        try:
            # 获取角色
            role = await self._get_role_by_id(role_id)
            if not role or role.tenant_id != tenant_id:
                raise NotFoundError("角色不存在")

            # 检查系统角色保护
            is_system = False
            if role.meta_data and isinstance(role.meta_data, dict):
                is_system = role.meta_data.get("is_system", False)

            if is_system:
                raise ValidationError("系统角色不可删除")

            # 检查用户关联
            user_count = await self._get_role_user_count(role_id)
            if user_count > 0 and not force_delete and not transfer_to_role_id:
                raise ValidationError("角色有用户关联，请先转移用户或使用强制删除")

            # 处理用户转移
            affected_users = 0
            if user_count > 0:
                if transfer_to_role_id:
                    # 验证目标角色
                    target_role = await self._get_role_by_id(transfer_to_role_id)
                    if not target_role or target_role.tenant_id != tenant_id:
                        raise NotFoundError("目标角色不存在")

                    # 转移用户
                    affected_users = await self._transfer_users_to_role(role_id, transfer_to_role_id)
                elif force_delete:
                    # 强制删除用户关联
                    affected_users = await self._remove_users_from_role(role_id)

            # 删除角色权限关联
            await self._remove_role_permissions(role_id)

            # 删除角色
            await self.session.delete(role)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="DELETE_ROLE",
                resource_type="ROLE",
                resource_id=role_id,
                details={
                    "role_name": role.role_name,
                    "affected_users": affected_users,
                    "transfer_to_role_id": transfer_to_role_id,
                    "force_delete": force_delete
                }
            )

            await self.session.commit()

            # 清除缓存
            await self._clear_role_cache(role_id)

            return {
                "role_id": role_id,
                "affected_users": affected_users,
                "transferred_to": transfer_to_role_id,
                "deleted_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"删除角色失败: {str(e)}")

    # ===== 权限管理 =====

    async def create_permission(
        self,
        tenant_id: str,
        permission_code: str,
        permission_name: str,
        description: Optional[str] = None,
        resource_type: str = "unknown",
        action: str = "unknown",
        category: str = "business",
        is_system: bool = False
    ) -> Dict[str, Any]:
        """
        创建权限

        创建新的系统权限定义
        """
        try:
            # 验证权限代码唯一性
            await self._validate_permission_code_uniqueness(tenant_id, permission_code)

            # 生成权限ID
            permission_id = f"perm_{uuid.uuid4()}"

            # 创建权限记录
            permission = self.permission_model(
                permission_id=permission_id,
                tenant_id=tenant_id,
                permission_code=permission_code,
                permission_name=permission_name,
                description=description,
                resource=resource_type,
                action=action,
                status=CommonStatus.ACTIVE,
                meta_data={"category": category, "is_system": is_system},
                created_at=datetime.utcnow()
            )
            self.session.add(permission)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="CREATE_PERMISSION",
                resource_type="PERMISSION",
                resource_id=permission_id,
                details={
                    "permission_code": permission_code,
                    "permission_name": permission_name,
                    "resource_type": resource_type,
                    "action": action
                }
            )

            await self.session.commit()

            return {
                "permission_id": permission_id,
                "permission_code": permission_code,
                "permission_name": permission_name,
                "created_at": permission.created_at.isoformat()
            }

        except IntegrityError as e:
            await self.session.rollback()
            if "permission_code" in str(e):
                raise DuplicateResourceError("权限代码已存在")
            else:
                raise DatabaseError("数据库约束错误")
        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"创建权限失败: {str(e)}")

    async def assign_role_permissions(
        self,
        tenant_id: str,
        role_id: str,
        permission_codes: List[str],
        operation: str = "assign"
    ) -> Dict[str, Any]:
        """
        分配角色权限

        为角色分配、移除或替换权限
        """
        try:
            # 验证角色存在性
            role = await self._get_role_by_id(role_id)
            if not role or role.tenant_id != tenant_id:
                raise NotFoundError("角色不存在")

            # 验证权限存在性
            valid_permissions = await self._validate_permissions_exist(tenant_id, permission_codes)

            affected_count = 0
            if operation == "assign":
                # 分配权限（追加）
                affected_count = await self._add_permissions_to_role(role_id, valid_permissions)
            elif operation == "remove":
                # 移除权限
                affected_count = await self._remove_permissions_from_role(role_id, valid_permissions)
            elif operation == "replace":
                # 替换权限（清空后重新分配）
                await self._clear_role_permissions(role_id)
                affected_count = await self._add_permissions_to_role(role_id, valid_permissions)
            else:
                raise ValidationError("不支持的操作类型")

            # 获取当前权限列表
            current_permissions = await self._get_role_permissions(role_id)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="ASSIGN_ROLE_PERMISSIONS",
                resource_type="ROLE",
                resource_id=role_id,
                details={
                    "operation": operation,
                    "permission_codes": permission_codes,
                    "affected_count": affected_count
                }
            )

            await self.session.commit()

            # 清除相关缓存
            await self._clear_role_permissions_cache(role_id)

            return {
                "role_id": role_id,
                "operation": operation,
                "affected_permissions": affected_count,
                "current_permissions": current_permissions,
                "operation_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"分配角色权限失败: {str(e)}")

    async def assign_user_roles(
        self,
        tenant_id: str,
        user_id: str,
        role_ids: List[str],
        operation: str = "assign",
        effective_time: Optional[str] = None,
        expire_time: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        分配用户角色

        为用户分配、移除或替换角色
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id)
            if not user or user.tenant_id != tenant_id:
                raise NotFoundError("用户不存在")

            # 验证角色存在性
            valid_roles = await self._validate_roles_exist(tenant_id, role_ids)

            # 解析时间
            effective_dt = datetime.fromisoformat(effective_time) if effective_time else datetime.utcnow()
            expire_dt = datetime.fromisoformat(expire_time) if expire_time else None

            affected_count = 0
            if operation == "assign":
                # 分配角色（追加）
                affected_count = await self._add_roles_to_user(user_id, valid_roles, effective_dt, expire_dt)
            elif operation == "remove":
                # 移除角色
                affected_count = await self._remove_roles_from_user(user_id, valid_roles)
            elif operation == "replace":
                # 替换角色（清空后重新分配）
                await self._clear_user_roles(user_id)
                affected_count = await self._add_roles_to_user(user_id, valid_roles, effective_dt, expire_dt)
            else:
                raise ValidationError("不支持的操作类型")

            # 获取当前角色列表
            current_roles = await self._get_user_roles(user_id)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="ASSIGN_USER_ROLES",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "operation": operation,
                    "role_ids": role_ids,
                    "affected_count": affected_count
                }
            )

            await self.session.commit()

            # 清除用户权限缓存
            await self._clear_user_permissions_cache(user_id)

            return {
                "user_id": user_id,
                "operation": operation,
                "affected_roles": affected_count,
                "current_roles": [role["role_id"] for role in current_roles],
                "operation_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"分配用户角色失败: {str(e)}")

    async def check_permission(
        self,
        tenant_id: str,
        user_id: str,
        permission_code: str,
        resource_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        权限检查

        检查用户是否具有指定权限
        """
        try:
            cache_hit = False
            permission_source = None

            # 尝试从缓存获取用户权限
            user_permissions = await self._get_user_permissions_from_cache(user_id)
            if user_permissions:
                cache_hit = True
                has_permission = permission_code in user_permissions
                if has_permission:
                    permission_source = user_permissions.get(f"{permission_code}_source")
            else:
                # 从数据库查询用户权限
                user_permissions_data = await self._calculate_user_permissions(user_id)
                has_permission = permission_code in user_permissions_data["permissions"]
                if has_permission:
                    permission_source = user_permissions_data["sources"].get(permission_code)

                # 缓存权限信息
                await self._cache_user_permissions(user_id, user_permissions_data)

            # TODO: 实现数据级权限检查
            # if resource_id and has_permission:
            #     has_permission = await self._check_data_level_permission(
            #         user_id, permission_code, resource_id, context
            #     )

            return {
                "user_id": user_id,
                "permission_code": permission_code,
                "has_permission": has_permission,
                "permission_source": permission_source,
                "checked_at": datetime.utcnow().isoformat(),
                "cache_hit": cache_hit
            }

        except Exception as e:
            raise BusinessError(f"权限检查失败: {str(e)}")

    # ===== 辅助方法 =====

    async def _get_tenant_by_id(self, tenant_id: str):
        """根据ID获取租户"""
        stmt = select(self.tenant_model).where(self.tenant_model.tenant_id == tenant_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_role_by_id(self, role_id: str):
        """根据ID获取角色"""
        stmt = select(self.role_model).where(self.role_model.role_id == role_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_user_by_id(self, user_id: str):
        """根据ID获取用户"""
        stmt = select(self.user_model).where(self.user_model.user_id == user_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _validate_role_code_uniqueness(self, tenant_id: str, role_code: str):
        """验证角色代码唯一性"""
        stmt = select(self.role_model).where(
            and_(
                self.role_model.tenant_id == tenant_id,
                self.role_model.role_code == role_code
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("角色代码已存在")

    async def _validate_permission_code_uniqueness(self, tenant_id: str, permission_code: str):
        """验证权限代码唯一性"""
        stmt = select(self.permission_model).where(
            and_(
                self.permission_model.tenant_id == tenant_id,
                self.permission_model.permission_code == permission_code
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("权限代码已存在")

    async def _assign_permissions_to_role(self, role_id: str, permissions: List[str], tenant_id: str) -> int:
        """为角色分配权限"""
        if not permissions:
            return 0

        # 获取权限ID
        stmt = select(self.permission_model).where(
            and_(
                self.permission_model.tenant_id == tenant_id,
                self.permission_model.permission_code.in_(permissions)
            )
        )
        result = await self.session.execute(stmt)
        permission_records = result.scalars().all()

        # 创建角色权限关联
        assigned_count = 0
        for permission in permission_records:
            # 检查是否已存在
            existing_stmt = select(self.role_permission_model).where(
                and_(
                    self.role_permission_model.role_id == role_id,
                    self.role_permission_model.permission_id == permission.permission_id
                )
            )
            existing_result = await self.session.execute(existing_stmt)
            if not existing_result.scalar_one_or_none():
                role_permission = self.role_permission_model(
                    role_id=role_id,
                    permission_id=permission.permission_id,
                    assigned_at=datetime.utcnow()
                )
                self.session.add(role_permission)
                assigned_count += 1

        return assigned_count

    async def _get_role_permissions(self, role_id: str) -> List[str]:
        """获取角色权限列表"""
        stmt = (
            select(self.permission_model.permission_code)
            .join(self.role_permission_model)
            .where(self.role_permission_model.role_id == role_id)
        )
        result = await self.session.execute(stmt)
        return [row[0] for row in result.fetchall()]

    async def _get_role_user_count(self, role_id: str) -> int:
        """获取角色关联的用户数量"""
        stmt = select(func.count(self.user_role_model.user_id)).where(
            and_(
                self.user_role_model.role_id == role_id,
                self.user_role_model.status == CommonStatus.ACTIVE,
                or_(
                    self.user_role_model.expiry_date.is_(None),
                    self.user_role_model.expiry_date > datetime.utcnow().date()
                )
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar() or 0

    async def _get_user_roles(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户角色列表"""
        stmt = (
            select(
                self.role_model.role_id,
                self.role_model.role_name,
                self.user_role_model.assigned_at,
                self.user_role_model.effective_date,
                self.user_role_model.expiry_date
            )
            .join(self.user_role_model)
            .where(
                and_(
                    self.user_role_model.user_id == user_id,
                    self.user_role_model.status == CommonStatus.ACTIVE,
                    or_(
                        self.user_role_model.expiry_date.is_(None),
                        self.user_role_model.expiry_date > datetime.utcnow().date()
                    )
                )
            )
        )
        result = await self.session.execute(stmt)
        return [
            {
                "role_id": row[0],
                "role_name": row[1],
                "assigned_at": row[2].isoformat(),
                "effective_time": row[3].isoformat() if row[3] else None,
                "expire_time": row[4].isoformat() if row[4] else None
            }
            for row in result.fetchall()
        ]

    async def _calculate_user_permissions(self, user_id: str) -> Dict[str, Any]:
        """计算用户的所有权限"""
        # 获取用户直接角色的权限
        stmt = (
            select(
                self.permission_model.permission_code,
                self.role_model.role_name
            )
            .join(self.role_permission_model, self.permission_model.permission_id == self.role_permission_model.permission_id)
            .join(self.role_model, self.role_permission_model.role_id == self.role_model.role_id)
            .join(self.user_role_model, self.role_model.role_id == self.user_role_model.role_id)
            .where(
                and_(
                    self.user_role_model.user_id == user_id,
                    self.user_role_model.status == CommonStatus.ACTIVE,
                    self.role_permission_model.status == CommonStatus.ACTIVE,
                    or_(
                        self.user_role_model.expiry_date.is_(None),
                        self.user_role_model.expiry_date > datetime.utcnow().date()
                    )
                )
            )
        )
        result = await self.session.execute(stmt)

        permissions = set()
        sources = {}
        for permission_code, role_name in result.fetchall():
            permissions.add(permission_code)
            sources[permission_code] = role_name

        return {
            "permissions": list(permissions),
            "sources": sources
        }

    async def _cache_role_info(self, role_id: str, role_info: Dict[str, Any]):
        """缓存角色信息"""
        await self.redis_repo.set(
            f"{self.role_cache_prefix}{role_id}",
            role_info,
            ttl=3600
        )

    async def _cache_user_permissions(self, user_id: str, permissions_data: Dict[str, Any]):
        """缓存用户权限"""
        # 构建缓存数据
        cache_data = {}
        for permission in permissions_data["permissions"]:
            cache_data[permission] = True
            cache_data[f"{permission}_source"] = permissions_data["sources"].get(permission)

        await self.redis_repo.set(
            f"{self.user_permissions_cache_prefix}{user_id}",
            cache_data,
            ttl=1800  # 30分钟
        )

    async def _get_user_permissions_from_cache(self, user_id: str) -> Optional[Dict[str, Any]]:
        """从缓存获取用户权限"""
        return await self.redis_repo.get(f"{self.user_permissions_cache_prefix}{user_id}")

    async def _clear_role_cache(self, role_id: str):
        """清除角色缓存"""
        await self.redis_repo.delete(f"{self.role_cache_prefix}{role_id}")

    async def _clear_user_permissions_cache(self, user_id: str):
        """清除用户权限缓存"""
        await self.redis_repo.delete(f"{self.user_permissions_cache_prefix}{user_id}")

    async def _clear_role_permissions_cache(self, role_id: str):
        """清除角色权限缓存"""
        await self.redis_repo.delete(f"{self.role_permissions_cache_prefix}{role_id}")

    async def _create_audit_log(self, tenant_id: str, user_id: Optional[str], action: str, resource_type: str, resource_id: str, details: Dict[str, Any]):
        """创建审计日志"""
        try:
            audit_log = AuditLogBuilder.create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=details,
                result="success"
            )
            self.session.add(audit_log)
            # 注意：不在这里commit，由调用方决定
        except Exception as e:
            # 审计日志失败不应该影响主业务
            print(f"审计日志记录失败: {action} - {resource_type}:{resource_id} by {user_id} in {tenant_id} - {str(e)}")

    async def _validate_permissions_exist(self, tenant_id: str, permission_codes: List[str]) -> List[str]:
        """验证权限存在性"""
        if not permission_codes:
            return []

        stmt = select(self.permission_model.permission_code).where(
            and_(
                self.permission_model.tenant_id == tenant_id,
                self.permission_model.permission_code.in_(permission_codes),
                self.permission_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        existing_codes = [row[0] for row in result.fetchall()]

        # 检查是否有不存在的权限
        missing_codes = set(permission_codes) - set(existing_codes)
        if missing_codes:
            raise NotFoundError(f"权限不存在: {', '.join(missing_codes)}")

        return existing_codes

    async def _validate_roles_exist(self, tenant_id: str, role_ids: List[str]) -> List[str]:
        """验证角色存在性"""
        if not role_ids:
            return []

        stmt = select(self.role_model.role_id).where(
            and_(
                self.role_model.tenant_id == tenant_id,
                self.role_model.role_id.in_(role_ids),
                self.role_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        existing_ids = [row[0] for row in result.fetchall()]

        # 检查是否有不存在的角色
        missing_ids = set(role_ids) - set(existing_ids)
        if missing_ids:
            raise NotFoundError(f"角色不存在: {', '.join(missing_ids)}")

        return existing_ids

    async def _add_permissions_to_role(self, role_id: str, permissions: List[str]) -> int:
        """为角色添加权限"""
        if not permissions:
            return 0

        # 获取权限ID映射
        stmt = select(
            self.permission_model.permission_id,
            self.permission_model.permission_code
        ).where(
            self.permission_model.permission_code.in_(permissions)
        )
        result = await self.session.execute(stmt)
        permission_map = {code: perm_id for perm_id, code in result.fetchall()}

        # 检查已存在的权限关联
        existing_stmt = select(self.role_permission_model.permission_id).where(
            and_(
                self.role_permission_model.role_id == role_id,
                self.role_permission_model.permission_id.in_(list(permission_map.values())),
                self.role_permission_model.status == CommonStatus.ACTIVE
            )
        )
        existing_result = await self.session.execute(existing_stmt)
        existing_permission_ids = {row[0] for row in existing_result.fetchall()}

        # 添加新的权限关联
        added_count = 0
        for code in permissions:
            permission_id = permission_map.get(code)
            if permission_id and permission_id not in existing_permission_ids:
                role_permission = self.role_permission_model(
                    tenant_id=await self._get_role_tenant_id(role_id),
                    role_id=role_id,
                    permission_id=permission_id,
                    status=CommonStatus.ACTIVE,
                    assigned_at=datetime.utcnow()
                )
                self.session.add(role_permission)
                added_count += 1

        return added_count

    async def _remove_permissions_from_role(self, role_id: str, permissions: List[str]) -> int:
        """从角色移除权限"""
        if not permissions:
            return 0

        # 获取权限ID
        stmt = select(self.permission_model.permission_id).where(
            self.permission_model.permission_code.in_(permissions)
        )
        result = await self.session.execute(stmt)
        permission_ids = [row[0] for row in result.fetchall()]

        if not permission_ids:
            return 0

        # 软删除权限关联
        update_stmt = (
            update(self.role_permission_model)
            .where(
                and_(
                    self.role_permission_model.role_id == role_id,
                    self.role_permission_model.permission_id.in_(permission_ids),
                    self.role_permission_model.status == CommonStatus.ACTIVE
                )
            )
            .values(
                status=CommonStatus.INACTIVE,
                updated_at=datetime.utcnow()
            )
        )
        result = await self.session.execute(update_stmt)
        return result.rowcount

    async def _clear_role_permissions(self, role_id: str):
        """清空角色权限"""
        update_stmt = (
            update(self.role_permission_model)
            .where(
                and_(
                    self.role_permission_model.role_id == role_id,
                    self.role_permission_model.status == CommonStatus.ACTIVE
                )
            )
            .values(
                status=CommonStatus.INACTIVE,
                updated_at=datetime.utcnow()
            )
        )
        await self.session.execute(update_stmt)

    async def _add_roles_to_user(self, user_id: str, roles: List[str], effective_time: datetime, expire_time: Optional[datetime]) -> int:
        """为用户添加角色"""
        if not roles:
            return 0

        # 获取用户租户ID
        user_tenant_id = await self._get_user_tenant_id(user_id)

        # 检查已存在的角色关联
        existing_stmt = select(self.user_role_model.role_id).where(
            and_(
                self.user_role_model.user_id == user_id,
                self.user_role_model.role_id.in_(roles),
                self.user_role_model.status == CommonStatus.ACTIVE,
                or_(
                    self.user_role_model.expiry_date.is_(None),
                    self.user_role_model.expiry_date > datetime.utcnow().date()
                )
            )
        )
        existing_result = await self.session.execute(existing_stmt)
        existing_role_ids = {row[0] for row in existing_result.fetchall()}

        # 添加新的角色关联
        added_count = 0
        for role_id in roles:
            if role_id not in existing_role_ids:
                user_role = self.user_role_model(
                    tenant_id=user_tenant_id,
                    user_id=user_id,
                    role_id=role_id,
                    assignment_type="permanent" if expire_time is None else "temporary",
                    effective_date=effective_time.date() if effective_time else datetime.utcnow().date(),
                    expiry_date=expire_time.date() if expire_time else None,
                    status=CommonStatus.ACTIVE,
                    assigned_at=datetime.utcnow()
                )
                self.session.add(user_role)
                added_count += 1

        return added_count

    async def _remove_roles_from_user(self, user_id: str, roles: List[str]) -> int:
        """从用户移除角色"""
        if not roles:
            return 0

        # 软删除角色关联
        update_stmt = (
            update(self.user_role_model)
            .where(
                and_(
                    self.user_role_model.user_id == user_id,
                    self.user_role_model.role_id.in_(roles),
                    self.user_role_model.status == CommonStatus.ACTIVE
                )
            )
            .values(
                status=CommonStatus.INACTIVE,
                updated_at=datetime.utcnow()
            )
        )
        result = await self.session.execute(update_stmt)
        return result.rowcount

    async def _clear_user_roles(self, user_id: str):
        """清空用户角色"""
        update_stmt = (
            update(self.user_role_model)
            .where(
                and_(
                    self.user_role_model.user_id == user_id,
                    self.user_role_model.status == CommonStatus.ACTIVE
                )
            )
            .values(
                status=CommonStatus.INACTIVE,
                updated_at=datetime.utcnow()
            )
        )
        await self.session.execute(update_stmt)

    async def _transfer_users_to_role(self, from_role_id: str, to_role_id: str) -> int:
        """转移用户到其他角色"""
        # 获取需要转移的用户
        stmt = select(self.user_role_model).where(
            and_(
                self.user_role_model.role_id == from_role_id,
                self.user_role_model.status == CommonStatus.ACTIVE,
                or_(
                    self.user_role_model.expiry_date.is_(None),
                    self.user_role_model.expiry_date > datetime.utcnow().date()
                )
            )
        )
        result = await self.session.execute(stmt)
        user_roles = result.scalars().all()

        transferred_count = 0
        for user_role in user_roles:
            # 检查目标角色是否已存在
            existing_stmt = select(self.user_role_model).where(
                and_(
                    self.user_role_model.user_id == user_role.user_id,
                    self.user_role_model.role_id == to_role_id,
                    self.user_role_model.status == CommonStatus.ACTIVE
                )
            )
            existing_result = await self.session.execute(existing_stmt)
            if not existing_result.scalar_one_or_none():
                # 创建新的角色关联
                new_user_role = self.user_role_model(
                    tenant_id=user_role.tenant_id,
                    user_id=user_role.user_id,
                    role_id=to_role_id,
                    assignment_type=user_role.assignment_type,
                    effective_date=user_role.effective_date,
                    expiry_date=user_role.expiry_date,
                    status=CommonStatus.ACTIVE,
                    assigned_at=datetime.utcnow()
                )
                self.session.add(new_user_role)
                transferred_count += 1

            # 删除原角色关联
            user_role.status = CommonStatus.INACTIVE
            user_role.updated_at = datetime.utcnow()

        return transferred_count

    async def _remove_users_from_role(self, role_id: str) -> int:
        """从角色移除所有用户"""
        update_stmt = (
            update(self.user_role_model)
            .where(
                and_(
                    self.user_role_model.role_id == role_id,
                    self.user_role_model.status == CommonStatus.ACTIVE
                )
            )
            .values(
                status=CommonStatus.INACTIVE,
                updated_at=datetime.utcnow()
            )
        )
        result = await self.session.execute(update_stmt)
        return result.rowcount

    async def _remove_role_permissions(self, role_id: str):
        """移除角色的所有权限"""
        update_stmt = (
            update(self.role_permission_model)
            .where(
                and_(
                    self.role_permission_model.role_id == role_id,
                    self.role_permission_model.status == CommonStatus.ACTIVE
                )
            )
            .values(
                status=CommonStatus.INACTIVE,
                updated_at=datetime.utcnow()
            )
        )
        await self.session.execute(update_stmt)

    async def _get_role_tenant_id(self, role_id: str) -> str:
        """获取角色的租户ID"""
        stmt = select(self.role_model.tenant_id).where(self.role_model.role_id == role_id)
        result = await self.session.execute(stmt)
        tenant_id = result.scalar_one_or_none()
        if not tenant_id:
            raise NotFoundError("角色不存在")
        return tenant_id

    async def _get_user_tenant_id(self, user_id: str) -> str:
        """获取用户的租户ID"""
        stmt = select(self.user_model.tenant_id).where(self.user_model.user_id == user_id)
        result = await self.session.execute(stmt)
        tenant_id = result.scalar_one_or_none()
        if not tenant_id:
            raise NotFoundError("用户不存在")
        return tenant_id
