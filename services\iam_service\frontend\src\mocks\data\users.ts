/**
 * @file 用户Mock数据
 * @description 提供用户相关的模拟数据
 * @status 框架文件 - 完成
 */

import { User } from '@/api/types'

export const mockUsers: User[] = [
  {
    id: 'user_1',
    username: 'admin',
    email: '<EMAIL>',
    displayName: '系统管理员',
    avatar: undefined,
    roles: ['admin', 'user'],
    permissions: ['read', 'write', 'delete', 'admin'],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    lastLoginAt: '2024-01-15T09:30:00.000Z',
    isActive: true,
  },
  {
    id: 'user_2',
    username: 'manager',
    email: '<EMAIL>',
    displayName: '项目经理',
    avatar: undefined,
    roles: ['manager', 'user'],
    permissions: ['read', 'write'],
    createdAt: '2024-01-02T00:00:00.000Z',
    updatedAt: '2024-01-02T00:00:00.000Z',
    lastLoginAt: '2024-01-14T14:20:00.000Z',
    isActive: true,
  },
  {
    id: 'user_3',
    username: 'developer',
    email: '<EMAIL>',
    displayName: '开发工程师',
    avatar: undefined,
    roles: ['developer', 'user'],
    permissions: ['read', 'write'],
    createdAt: '2024-01-03T00:00:00.000Z',
    updatedAt: '2024-01-03T00:00:00.000Z',
    lastLoginAt: '2024-01-14T16:45:00.000Z',
    isActive: true,
  },
  {
    id: 'user_4',
    username: 'guest',
    email: '<EMAIL>',
    displayName: '访客用户',
    avatar: undefined,
    roles: ['guest'],
    permissions: ['read'],
    createdAt: '2024-01-04T00:00:00.000Z',
    updatedAt: '2024-01-04T00:00:00.000Z',
    lastLoginAt: '2024-01-13T10:15:00.000Z',
    isActive: true,
  },
  {
    id: 'user_5',
    username: 'tester',
    email: '<EMAIL>',
    displayName: '测试工程师',
    avatar: undefined,
    roles: ['tester', 'user'],
    permissions: ['read'],
    createdAt: '2024-01-05T00:00:00.000Z',
    updatedAt: '2024-01-05T00:00:00.000Z',
    lastLoginAt: '2024-01-12T13:30:00.000Z',
    isActive: false,
  },
] 