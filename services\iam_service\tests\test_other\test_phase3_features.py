"""
第三阶段功能测试

测试RBAC、审计日志、系统配置和高级安全功能
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from services.rbac_service import RBACService
from services.audit_service import AuditService
from services.system_config_service import SystemConfigService
from services.advanced_security_service import AdvancedSecurityService
from security.security_utils import SecurityUtils


class TestRBACService:
    """RBAC服务测试"""

    @pytest.fixture
    def rbac_service(self):
        """创建RBAC服务实例"""
        session = AsyncMock()
        redis_repo = Mock()
        
        return RBACService(
            session=session,
            redis_repo=redis_repo,
            user_model=Mock(),
            tenant_model=Mock(),
            role_model=Mock(),
            permission_model=Mock(),
            user_role_model=Mock(),
            role_permission_model=Mock(),
            audit_log_model=Mock()
        )

    @pytest.mark.asyncio
    async def test_create_role(self, rbac_service):
        """测试创建角色"""
        # 模拟租户存在
        rbac_service._get_tenant_by_id = AsyncMock(return_value=Mock(status="active"))
        rbac_service._validate_role_code_uniqueness = AsyncMock()
        rbac_service._assign_permissions_to_role = AsyncMock(return_value=3)
        rbac_service._create_audit_log = AsyncMock()
        rbac_service._cache_role_info = AsyncMock()
        
        result = await rbac_service.create_role(
            tenant_id="tenant_123",
            role_name="管理员",
            role_code="ADMIN",
            description="系统管理员",
            level=1,
            permissions=["user:read", "user:write", "role:read"]
        )
        
        assert "role_id" in result
        assert result["role_name"] == "管理员"
        assert result["role_code"] == "ADMIN"
        assert result["permissions_assigned"] == 3

    @pytest.mark.asyncio
    async def test_check_permission(self, rbac_service):
        """测试权限检查"""
        # 模拟用户权限缓存
        rbac_service._get_user_permissions_from_cache = AsyncMock(return_value={
            "user:read": True,
            "user:read_source": "管理员角色"
        })
        
        result = await rbac_service.check_permission(
            tenant_id="tenant_123",
            user_id="user_123",
            permission_code="user:read"
        )
        
        assert result["has_permission"] is True
        assert result["permission_source"] == "管理员角色"
        assert result["cache_hit"] is True


class TestAuditService:
    """审计服务测试"""

    @pytest.fixture
    def audit_service(self):
        """创建审计服务实例"""
        session = AsyncMock()
        redis_repo = Mock()
        
        return AuditService(
            session=session,
            redis_repo=redis_repo,
            user_model=Mock(),
            tenant_model=Mock(),
            audit_log_model=Mock()
        )

    @pytest.mark.asyncio
    async def test_create_audit_log(self, audit_service):
        """测试创建审计日志"""
        audit_service._get_user_by_id = AsyncMock(return_value=Mock(username="testuser"))
        audit_service._send_security_alert = AsyncMock()
        audit_service._clear_stats_cache = AsyncMock()
        
        result = await audit_service.create_audit_log(
            tenant_id="tenant_123",
            user_id="user_123",
            action="LOGIN",
            resource_type="USER",
            description="用户登录",
            status="success",
            risk_level="low"
        )
        
        assert "log_id" in result
        assert "created_at" in result

    @pytest.mark.asyncio
    async def test_query_audit_logs(self, audit_service):
        """测试查询审计日志"""
        # 模拟数据库查询结果
        mock_logs = [
            Mock(
                log_id="log_123",
                tenant_id="tenant_123",
                user_id="user_123",
                username="testuser",
                action="LOGIN",
                resource_type="USER",
                description="用户登录",
                status="success",
                risk_level="low",
                created_at=datetime.utcnow(),
                details={},
                ip_address="***********",
                user_agent="Mozilla/5.0"
            )
        ]
        
        audit_service.session.execute = AsyncMock()
        audit_service.session.execute.return_value.scalar.return_value = 1  # total count
        audit_service.session.execute.return_value.scalars.return_value.all.return_value = mock_logs
        
        result = await audit_service.query_audit_logs(
            tenant_id="tenant_123",
            page=1,
            page_size=20
        )
        
        assert result["total"] == 1
        assert len(result["logs"]) == 1
        assert result["logs"][0]["action"] == "LOGIN"


class TestSystemConfigService:
    """系统配置服务测试"""

    @pytest.fixture
    def config_service(self):
        """创建系统配置服务实例"""
        session = AsyncMock()
        redis_repo = Mock()
        
        return SystemConfigService(
            session=session,
            redis_repo=redis_repo,
            tenant_model=Mock(),
            system_config_model=Mock(),
            audit_log_model=Mock()
        )

    @pytest.mark.asyncio
    async def test_set_config(self, config_service):
        """测试设置配置"""
        config_service._get_tenant_by_id = AsyncMock(return_value=Mock())
        config_service._get_config_record = AsyncMock(return_value=None)
        config_service._create_audit_log = AsyncMock()
        config_service._clear_config_cache = AsyncMock()
        
        result = await config_service.set_config(
            tenant_id="tenant_123",
            config_key="password_policy.min_length",
            config_value=12,
            description="密码最小长度",
            category="security"
        )
        
        assert "config_id" in result
        assert result["config_key"] == "password_policy.min_length"
        assert result["new_value"] == 12

    @pytest.mark.asyncio
    async def test_get_config(self, config_service):
        """测试获取配置"""
        # 模拟缓存未命中，从默认配置获取
        config_service.redis_repo.get = AsyncMock(return_value=None)
        config_service._get_config_record = AsyncMock(return_value=None)
        config_service.redis_repo.set = AsyncMock()
        
        result = await config_service.get_config(
            tenant_id="tenant_123",
            config_key="password_policy.min_length"
        )
        
        assert result["config_key"] == "password_policy.min_length"
        assert result["config_value"] == 8  # 默认值
        assert result["source"] == "default"


class TestAdvancedSecurityService:
    """高级安全服务测试"""

    @pytest.fixture
    def security_service(self):
        """创建高级安全服务实例"""
        session = AsyncMock()
        redis_repo = Mock()
        security_utils = SecurityUtils()
        
        return AdvancedSecurityService(
            session=session,
            redis_repo=redis_repo,
            user_model=Mock(),
            tenant_model=Mock(),
            user_mfa_model=Mock(),
            security_policy_model=Mock(),
            security_event_model=Mock(),
            audit_log_model=Mock(),
            security_utils=security_utils
        )

    @pytest.mark.asyncio
    async def test_setup_mfa(self, security_service):
        """测试设置MFA"""
        security_service._get_user_by_id = AsyncMock(return_value=Mock(
            tenant_id="tenant_123",
            email="<EMAIL>"
        ))
        security_service._create_audit_log = AsyncMock()
        
        result = await security_service.setup_mfa(
            tenant_id="tenant_123",
            user_id="user_123",
            mfa_type="totp"
        )
        
        assert "setup_token" in result
        assert "qr_code_url" in result
        assert "secret_key" in result
        assert "backup_codes" in result
        assert len(result["backup_codes"]) == 10

    @pytest.mark.asyncio
    async def test_verify_mfa_setup(self, security_service):
        """测试验证MFA设置"""
        # 模拟设置信息
        setup_info = {
            "user_id": "user_123",
            "tenant_id": "tenant_123",
            "mfa_type": "totp",
            "mfa_config": {"secret": "JBSWY3DPEHPK3PXP"},
            "backup_codes": ["CODE1", "CODE2"]
        }
        
        security_service.redis_repo.get = AsyncMock(return_value=setup_info)
        security_service.security_utils.verify_totp = Mock(return_value=True)
        security_service._get_user_mfa = AsyncMock(return_value=None)
        security_service._get_user_by_id = AsyncMock(return_value=Mock(username="testuser"))
        security_service._store_backup_codes = AsyncMock()
        security_service._create_audit_log = AsyncMock()
        security_service._create_security_event = AsyncMock()
        security_service.redis_repo.delete = AsyncMock()
        
        result = await security_service.verify_mfa_setup(
            tenant_id="tenant_123",
            user_id="user_123",
            setup_token="setup_token_123",
            verification_code="123456"
        )
        
        assert result["mfa_enabled"] is True
        assert result["mfa_type"] == "totp"


class TestSecurityUtils:
    """安全工具测试"""

    def test_generate_totp_secret(self):
        """测试生成TOTP密钥"""
        secret = SecurityUtils.generate_totp_secret()
        assert len(secret) == 32
        assert secret.isalnum()

    def test_generate_backup_codes(self):
        """测试生成备用恢复码"""
        codes = SecurityUtils.generate_backup_codes(count=5, length=8)
        assert len(codes) == 5
        for code in codes:
            assert len(code) == 8
            assert code.isupper()

    def test_generate_password(self):
        """测试生成密码"""
        password = SecurityUtils.generate_password(length=12, include_symbols=True)
        assert len(password) == 12
        assert any(c.islower() for c in password)
        assert any(c.isupper() for c in password)
        assert any(c.isdigit() for c in password)

    def test_check_password_strength(self):
        """测试密码强度检查"""
        # 强密码
        result = SecurityUtils.check_password_strength("StrongP@ssw0rd123")
        assert result["strength"] == "strong"
        assert result["score"] >= 5
        
        # 弱密码
        result = SecurityUtils.check_password_strength("123456")
        assert result["strength"] == "weak"
        assert len(result["feedback"]) > 0

    def test_generate_api_key(self):
        """测试生成API密钥"""
        api_key = SecurityUtils.generate_api_key("test")
        assert api_key.startswith("test_")
        assert len(api_key) > 10

    def test_is_safe_url(self):
        """测试URL安全检查"""
        allowed_hosts = ["example.com", "api.example.com"]
        
        # 安全URL
        assert SecurityUtils.is_safe_url("/api/v1/users", allowed_hosts) is True
        assert SecurityUtils.is_safe_url("https://api.example.com/test", allowed_hosts) is True
        
        # 不安全URL
        assert SecurityUtils.is_safe_url("https://malicious.com/test", allowed_hosts) is False
        assert SecurityUtils.is_safe_url("javascript:alert('xss')", allowed_hosts) is False


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
