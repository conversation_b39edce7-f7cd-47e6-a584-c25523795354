/**
 * @file 认证相关API接口
 * @description 提供登录、登出、令牌刷新等认证功能的API接口
 * @status 开发中
 */

import { apiClient } from './client'
import {
  BaseRequest,
  RequestMeta,
  SuccessResponse,
  LoginRequest,
  LoginResponse,
  LogoutRequest,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ChangePasswordRequest,
  RegisterRequest,
  RegisterResponse,
  SendEmailCodeRequest,
  SendEmailCodeResponse,
  ActivateUserRequest,
  ActivateUserResponse,
  ListTenantsRequest,
  ListTenantsResponse,
  TenantListItem
} from './types'

/**
 * 用户登录
 */
export const login = async (loginData: LoginRequest): Promise<LoginResponse> => {
  const request: BaseRequest<LoginRequest> = {
    meta: createRequestMeta(),
    data: loginData
  }

  const response = await apiClient.post<LoginResponse>('/v1/auth/login', request)

  if (response.status === 'success' && response.data) {
    // 保存令牌到本地存储
    localStorage.setItem('access_token', response.data.access_token)
    localStorage.setItem('refresh_token', response.data.refresh_token)
    localStorage.setItem('session_id', response.data.session_id)
    localStorage.setItem('tenant_id', response.data.tenant_info.tenant_id)
    localStorage.setItem('user_info', JSON.stringify(response.data.user_info))

    return response.data
  }

  throw new Error(response.message || '登录失败')
}

/**
 * 用户登出
 */
export const logout = async (logoutData: LogoutRequest): Promise<void> => {
  const request: BaseRequest<LogoutRequest> = {
    meta: createRequestMeta(),
    data: logoutData
  }

  try {
    await apiClient.post('/v1/auth/logout', request)
  } finally {
    // 无论请求是否成功，都清除本地存储的认证信息
    clearAuthData()
  }
}

/**
 * 刷新访问令牌
 */
export const refreshToken = async (refreshData: RefreshTokenRequest): Promise<RefreshTokenResponse> => {
  const request: BaseRequest<RefreshTokenRequest> = {
    meta: createRequestMeta(),
    data: refreshData
  }

  const response = await apiClient.post<RefreshTokenResponse>('/v1/auth/refresh', request)

  if (response.status === 'success' && response.data) {
    // 更新本地存储的令牌
    localStorage.setItem('access_token', response.data.access_token)
    localStorage.setItem('refresh_token', response.data.refresh_token)

    return response.data
  }

  throw new Error(response.message || '令牌刷新失败')
}

/**
 * 修改密码
 */
export const changePassword = async (passwordData: ChangePasswordRequest): Promise<void> => {
  const request: BaseRequest<ChangePasswordRequest> = {
    meta: createRequestMeta(),
    data: passwordData
  }

  const response = await apiClient.post('/v1/auth/change-password', request)

  if (response.status !== 'success') {
    throw new Error(response.message || '密码修改失败')
  }
}

/**
 * 验证令牌有效性
 */
export const verifyToken = async (): Promise<boolean> => {
  try {
    const request: BaseRequest<{}> = {
      meta: createRequestMeta(),
      data: {}
    }

    const response = await apiClient.post('/v1/auth/verify-token', request)

    return response.status === 'success'
  } catch (error) {
    return false
  }
}

/**
 * 获取当前用户信息
 */
export const getCurrentUser = async (): Promise<LoginResponse['user_info']> => {
  // 首先尝试从本地存储获取
  const userInfoStr = localStorage.getItem('user_info')
  if (userInfoStr) {
    try {
      return JSON.parse(userInfoStr)
    } catch (error) {
      console.warn('Failed to parse user info from localStorage:', error)
    }
  }
  
  // 如果本地存储没有或解析失败，从服务器获取
  const response = await apiClient.get('/v1/auth/me')
  
  if (response.status === 'success' && response.data) {
    localStorage.setItem('user_info', JSON.stringify(response.data))
    return response.data
  }
  
  throw new Error('获取用户信息失败')
}

/**
 * 清除认证数据
 */
export const clearAuthData = (): void => {
  localStorage.removeItem('access_token')
  localStorage.removeItem('refresh_token')
  localStorage.removeItem('session_id')
  localStorage.removeItem('tenant_id')
  localStorage.removeItem('user_info')
}

/**
 * 检查是否已登录
 */
export const isAuthenticated = (): boolean => {
  const accessToken = localStorage.getItem('access_token')
  const userInfo = localStorage.getItem('user_info')
  return !!(accessToken && userInfo)
}

/**
 * 获取访问令牌
 */
export const getAccessToken = (): string | null => {
  return localStorage.getItem('access_token')
}

/**
 * 获取刷新令牌
 */
export const getRefreshToken = (): string | null => {
  return localStorage.getItem('refresh_token')
}

/**
 * 获取租户ID
 */
export const getTenantId = (): string | null => {
  return localStorage.getItem('tenant_id')
}

/**
 * 用户注册
 */
export const register = async (registerData: RegisterRequest): Promise<RegisterResponse> => {
  const request: BaseRequest<RegisterRequest> = {
    meta: createRequestMeta(),
    data: registerData
  }

  const response = await apiClient.post<RegisterResponse>('/v1/users/register', request)

  if (response.status === 'success' && response.data) {
    return response.data
  }

  throw new Error(response.message || '注册失败')
}

/**
 * 发送邮箱验证码
 */
export const sendEmailCode = async (emailData: SendEmailCodeRequest): Promise<SendEmailCodeResponse> => {
  const request: BaseRequest<SendEmailCodeRequest> = {
    meta: createRequestMeta(),
    data: emailData
  }

  const response = await apiClient.post<SendEmailCodeResponse>('/v1/users/send_email_code', request)

  if (response.status === 'success' && response.data) {
    return response.data
  }

  throw new Error(response.message || '发送验证码失败')
}

/**
 * 激活用户账户
 */
export const activateUser = async (activateData: ActivateUserRequest): Promise<ActivateUserResponse> => {
  const request: BaseRequest<ActivateUserRequest> = {
    meta: createRequestMeta(),
    data: activateData
  }

  const response = await apiClient.post<ActivateUserResponse>('/v1/users/activate', request)

  if (response.status === 'success' && response.data) {
    return response.data
  }

  throw new Error(response.message || '账户激活失败')
}

/**
 * 根据租户编码获取租户信息
 */
export const getTenantByCode = async (tenantCode: string): Promise<TenantListItem> => {
  const request: BaseRequest<ListTenantsRequest> = {
    meta: createRequestMeta(),
    data: {
      tenant_id: tenantCode,
    }
  }

  const response = await apiClient.post<ListTenantsResponse>('/v1/tenants/detail', request)

  if (response.status === 'success' && response.data) {
    // 查找精确匹配的租户编码
    const tenant = response.data;
    if (tenant) {
      return tenant
    }
    throw new Error('租户不存在或已停用')
  }

  throw new Error(response.message || '获取租户信息失败')
}

/**
 * 创建请求元信息
 */
const createRequestMeta = (): RequestMeta => {
  return {
    request_id: generateRequestId(),
    timestamp: new Date().toISOString(),
    version: 'v1'
  }
}

/**
 * 生成请求ID
 */
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
