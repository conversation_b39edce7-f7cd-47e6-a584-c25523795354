#!/usr/bin/env python3
"""未使用引用清理脚本，集成 autoflake 和 pyflakes"""

import subprocess
import sys
from pathlib import Path
from typing import List, <PERSON><PERSON>


def run_autoflake(paths: List[str]) -> Tuple[int, str]:
    """运行 autoflake 移除未使用的引用

    Args:
        paths: 要处理的路径列表

    Returns:
        (返回码, 输出信息)
    """
    try:
        result = subprocess.run(
            [
                "autoflake",
                "--remove-all-unused-imports",  # 移除所有未使用的导入
                "--remove-unused-variables",  # 移除未使用的变量
                "--in-place",  # 原地修改文件
                "--recursive",  # 递归处理目录
                *paths,
            ],
            capture_output=True,
            text=True,
            check=False,
        )
        return result.returncode, result.stdout + result.stderr
    except FileNotFoundError:
        return 1, "Error: autoflake not found. Install with: pip install autoflake"


def run_pyflakes(paths: List[str]) -> <PERSON><PERSON>[int, str]:
    """运行 pyflakes 进行静态检查

    Args:
        paths: 要检查的路径列表

    Returns:
        (返回码, 输出信息)
    """
    try:
        result = subprocess.run(
            ["pyflakes", *paths], capture_output=True, text=True, check=False
        )
        return result.returncode, result.stdout + result.stderr
    except FileNotFoundError:
        return 1, "Error: pyflakes not found. Install with: pip install pyflakes"


def get_default_paths() -> List[str]:
    """获取默认处理路径"""
    project_root = (
        Path(__file__).resolve().parent.parent
    )  # 假设脚本在项目根目录的scripts文件夹
    default_path = project_root / "src"
    return [str(default_path)] if default_path.exists() else [str(project_root)]


def main():
    """主函数"""
    # 获取路径参数或使用默认值
    paths = sys.argv[1:] or get_default_paths()
    print(paths)
    # 路径验证
    invalid_paths = [p for p in paths if not Path(p).exists()]
    if invalid_paths:
        print(f"Error: Invalid path(s): {', '.join(invalid_paths)}")
        sys.exit(1)

    # 执行清理
    print("=" * 40)
    print("Running autoflake...")
    exit_code, output = run_autoflake(paths)
    if output.strip():
        print(output)

    # 执行静态检查
    print("\n" + "=" * 40)
    print("Running pyflakes...")
    flakes_code, flakes_output = run_pyflakes(paths)
    if flakes_output.strip():
        print(flakes_output)

    # 合并返回码
    final_code = exit_code or flakes_code
    print("\n" + "=" * 40)
    print("✅ Cleanup completed!" if final_code == 0 else "❌ Issues found!")
    sys.exit(final_code)


if __name__ == "__main__":
    main()
