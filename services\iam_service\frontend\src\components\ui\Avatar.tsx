/**
 * @file Avatar组件
 * @description 用户头像组件，支持图片、文字和图标显示
 */

import React from 'react'
import clsx from 'clsx'

export interface AvatarProps {
  src?: string
  alt?: string
  name?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  shape?: 'circle' | 'square'
  className?: string
  fallbackIcon?: React.ReactNode
  onClick?: () => void
}

const sizeClasses = {
  xs: 'w-6 h-6 text-xs',
  sm: 'w-8 h-8 text-sm',
  md: 'w-10 h-10 text-base',
  lg: 'w-12 h-12 text-lg',
  xl: 'w-16 h-16 text-xl'
}

const DefaultUserIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
    />
  </svg>
)

// 从姓名生成首字母
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

// 根据姓名生成背景颜色
const getBackgroundColor = (name: string): string => {
  const colors = [
    'bg-red-500',
    'bg-orange-500',
    'bg-amber-500',
    'bg-yellow-500',
    'bg-lime-500',
    'bg-green-500',
    'bg-emerald-500',
    'bg-teal-500',
    'bg-cyan-500',
    'bg-sky-500',
    'bg-blue-500',
    'bg-indigo-500',
    'bg-violet-500',
    'bg-purple-500',
    'bg-fuchsia-500',
    'bg-pink-500',
    'bg-rose-500'
  ]
  
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  return colors[Math.abs(hash) % colors.length]
}

export const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  name,
  size = 'md',
  shape = 'circle',
  className,
  fallbackIcon,
  onClick
}) => {
  const [imageError, setImageError] = React.useState(false)
  const [imageLoaded, setImageLoaded] = React.useState(false)

  const handleImageError = () => {
    setImageError(true)
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
    setImageError(false)
  }

  // 重置图片状态当src改变时
  React.useEffect(() => {
    if (src) {
      setImageError(false)
      setImageLoaded(false)
    }
  }, [src])

  const shouldShowImage = src && !imageError
  const shouldShowInitials = name && (!src || imageError)
  const shouldShowIcon = !src && !name

  const avatarClasses = clsx(
    'relative inline-flex items-center justify-center overflow-hidden font-medium text-white',
    sizeClasses[size],
    {
      'rounded-full': shape === 'circle',
      'rounded-lg': shape === 'square',
      'cursor-pointer': onClick,
      'transition-transform hover:scale-105': onClick
    },
    {
      // 如果显示首字母，使用基于姓名的背景色
      [getBackgroundColor(name || '')]: shouldShowInitials,
      // 如果显示图标，使用灰色背景
      'bg-gray-300 text-gray-600': shouldShowIcon
    },
    className
  )

  return (
    <div className={avatarClasses} onClick={onClick}>
      {/* 图片显示 */}
      {shouldShowImage && (
        <>
          <img
            src={src}
            alt={alt || name || 'Avatar'}
            className={clsx(
              'w-full h-full object-cover',
              {
                'opacity-0': !imageLoaded,
                'opacity-100': imageLoaded
              }
            )}
            onError={handleImageError}
            onLoad={handleImageLoad}
          />
          {/* 加载时的占位符 */}
          {!imageLoaded && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-200 animate-pulse">
              <DefaultUserIcon className="w-1/2 h-1/2 text-gray-400" />
            </div>
          )}
        </>
      )}

      {/* 首字母显示 */}
      {shouldShowInitials && (
        <span className="font-semibold">
          {getInitials(name!)}
        </span>
      )}

      {/* 图标显示 */}
      {shouldShowIcon && (
        <div className="w-2/3 h-2/3">
          {fallbackIcon || <DefaultUserIcon className="w-full h-full" />}
        </div>
      )}
    </div>
  )
}
