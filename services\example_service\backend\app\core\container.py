from commonlib.storages.persistence.redis.repository import RedisRepository
from dependency_injector import containers, providers


class ServiceContainer(containers.DeclarativeContainer):
    """Application containers."""

    config = providers.DependenciesContainer()
    infra = providers.DependenciesContainer()

    # redis的仓储
    redis_repo = providers.Factory(
        RedisRepository,
        key_prefix=config.app_name.provider,
        redis_connector=infra.redis_clint,
    )
