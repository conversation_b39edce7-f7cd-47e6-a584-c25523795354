"""
文档管理路由

提供文档的创建、查询、更新、删除等接口
"""

from fastapi import APIRouter, Depends
from dependency_injector.wiring import inject, Provide
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from container import ServiceContainer
from services.document_service import DocumentService

router = APIRouter()


# ===== 请求模型 =====
class CreateDocumentRequest(BaseModel):
    """创建文档请求"""
    doc_name: str = Field(..., description="文档名称", min_length=1, max_length=200)
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    content: Optional[str] = Field(None, description="文档内容")
    file_path: Optional[str] = Field(None, description="文件路径")
    file_type: Optional[str] = Field(None, description="文件类型")
    metadata: Optional[Dict[str, Any]] = Field({}, description="文档元数据")


class ListDocumentsRequest(BaseModel):
    """查询文档列表请求"""
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    user_id: Optional[str] = Field(None, description="用户ID(用于权限检查)")
    cursor: Optional[str] = Field(None, description="分页游标")
    limit: int = Field(20, description="每页数量", ge=1, le=100)
    search: Optional[str] = Field(None, description="搜索关键词")
    status: Optional[str] = Field(None, description="文档状态筛选")
    file_type: Optional[str] = Field(None, description="文件类型筛选")


class GetDocumentRequest(BaseModel):
    """获取文档详情请求"""
    doc_id: str = Field(..., description="文档ID")
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    user_id: Optional[str] = Field(None, description="用户ID(用于权限检查)")


class UpdateDocumentRequest(BaseModel):
    """更新文档请求"""
    doc_id: str = Field(..., description="文档ID")
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    doc_name: Optional[str] = Field(None, description="文档名称")
    content: Optional[str] = Field(None, description="文档内容")
    metadata: Optional[Dict[str, Any]] = Field(None, description="文档元数据")


class DeleteDocumentRequest(BaseModel):
    """删除文档请求"""
    doc_id: str = Field(..., description="文档ID")
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    force: bool = Field(False, description="是否强制删除")


class UploadDocumentRequest(BaseModel):
    """上传文档请求"""
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    file_name: str = Field(..., description="文件名")
    file_content: str = Field(..., description="文件内容(base64编码)")
    file_type: str = Field(..., description="文件类型")
    metadata: Optional[Dict[str, Any]] = Field({}, description="文档元数据")


class ProcessDocumentRequest(BaseModel):
    """处理文档请求"""
    doc_id: str = Field(..., description="文档ID")
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    chunk_size: Optional[int] = Field(None, description="分块大小")
    chunk_overlap: Optional[int] = Field(None, description="分块重叠")
    force_reprocess: bool = Field(False, description="是否强制重新处理")


class SearchDocumentsRequest(BaseModel):
    """搜索文档请求"""
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    user_id: Optional[str] = Field(None, description="用户ID(用于权限检查)")
    query: str = Field(..., description="搜索查询", min_length=1)
    limit: int = Field(10, description="返回结果数量", ge=1, le=50)
    similarity_threshold: float = Field(0.7, description="相似度阈值", ge=0.0, le=1.0)
    search_type: str = Field("semantic", description="搜索类型: semantic, keyword, hybrid")


# ===== 响应数据模型 =====
class DocumentResponse(BaseModel):
    """文档响应数据"""
    doc_id: str = Field(..., description="文档ID")
    doc_name: str = Field(..., description="文档名称")
    kb_id: str = Field(..., description="知识库ID")
    kb_name: str = Field(..., description="知识库名称")
    tenant_id: str = Field(..., description="租户ID")
    file_type: Optional[str] = Field(None, description="文件类型")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    status: str = Field(..., description="文档状态")
    processing_status: str = Field(..., description="处理状态")
    chunk_count: int = Field(0, description="分块数量")
    vector_count: int = Field(0, description="向量数量")
    metadata: Dict[str, Any] = Field({}, description="文档元数据")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class DocumentListResponse(BaseModel):
    """文档列表响应数据"""
    documents: List[DocumentResponse] = Field(..., description="文档列表")
    total: int = Field(..., description="总数量")
    next_cursor: Optional[str] = Field(None, description="下一页游标")
    has_more: bool = Field(..., description="是否有更多数据")


class DocumentOperationResponse(BaseModel):
    """文档操作响应数据"""
    success: bool = Field(True, description="操作是否成功")
    doc_id: Optional[str] = Field(None, description="文档ID")
    task_id: Optional[str] = Field(None, description="任务ID")


class SearchResult(BaseModel):
    """搜索结果项"""
    doc_id: str = Field(..., description="文档ID")
    doc_name: str = Field(..., description="文档名称")
    chunk_id: str = Field(..., description="分块ID")
    content: str = Field(..., description="匹配内容")
    similarity: float = Field(..., description="相似度分数")
    metadata: Dict[str, Any] = Field({}, description="元数据")


class DocumentSearchResponse(BaseModel):
    """文档搜索响应数据"""
    results: List[SearchResult] = Field(..., description="搜索结果")
    total: int = Field(..., description="总结果数")
    query: str = Field(..., description="搜索查询")
    search_time: float = Field(..., description="搜索耗时(秒)")


# ===== 响应模型 =====
class DocumentResponseModel(SuccessResponse[DocumentResponse]):
    """文档响应模型"""
    data: DocumentResponse


class DocumentListResponseModel(SuccessResponse[DocumentListResponse]):
    """文档列表响应模型"""
    data: DocumentListResponse


class DocumentOperationResponseModel(SuccessResponse[DocumentOperationResponse]):
    """文档操作响应模型"""
    data: DocumentOperationResponse


class DocumentSearchResponseModel(SuccessResponse[DocumentSearchResponse]):
    """文档搜索响应模型"""
    data: DocumentSearchResponse


# ===== 路由端点 =====
@router.post(
    "/create",
    summary="创建文档",
    description="在指定知识库中创建新文档",
    response_model=DocumentResponseModel,
)
@inject
async def create_document(
    request: BaseRequest[CreateDocumentRequest],
    doc_service: DocumentService = Depends(Provide[ServiceContainer.document_service])
):
    """创建文档"""
    create_doc_params = request.data
    result = await doc_service.create_document(
        doc_name=create_doc_params.doc_name,
        kb_id=create_doc_params.kb_id,
        tenant_id=create_doc_params.tenant_id,
        content=create_doc_params.content,
        file_path=create_doc_params.file_path,
        file_type=create_doc_params.file_type,
        metadata=create_doc_params.metadata
    )
    return success_response(result, message="文档创建成功，正在处理中")


@router.post(
    "/list",
    summary="查询文档列表",
    description="分页查询知识库中的文档列表，支持搜索和筛选",
    response_model=DocumentListResponseModel,
)
@inject
async def list_documents(
    request: BaseRequest[ListDocumentsRequest],
    doc_service: DocumentService = Depends(Provide[ServiceContainer.document_service])
):
    """获取文档列表"""
    list_docs_params = request.data
    result = await doc_service.list_documents(
        kb_id=list_docs_params.kb_id,
        tenant_id=list_docs_params.tenant_id,
        user_id=list_docs_params.user_id,
        cursor=list_docs_params.cursor,
        limit=list_docs_params.limit,
        search=list_docs_params.search,
        status=list_docs_params.status,
        file_type=list_docs_params.file_type
    )
    return success_response(result, message="查询成功")


@router.post(
    "/detail",
    summary="获取文档详情",
    description="获取指定文档的详细信息，包括处理状态和统计数据",
    response_model=DocumentResponseModel,
)
@inject
async def get_document_detail(
    request: BaseRequest[GetDocumentRequest],
    doc_service: DocumentService = Depends(Provide[ServiceContainer.document_service])
):
    """获取文档详情"""
    get_doc_params = request.data
    result = await doc_service.get_document_detail(
        doc_id=get_doc_params.doc_id,
        kb_id=get_doc_params.kb_id,
        tenant_id=get_doc_params.tenant_id,
        user_id=get_doc_params.user_id
    )
    return success_response(result, message="查询成功")


@router.post(
    "/update",
    summary="更新文档",
    description="更新文档的基本信息和内容",
    response_model=DocumentResponseModel,
)
@inject
async def update_document(
    request: BaseRequest[UpdateDocumentRequest],
    doc_service: DocumentService = Depends(Provide[ServiceContainer.document_service])
):
    """更新文档"""
    update_doc_params = request.data
    result = await doc_service.update_document(
        doc_id=update_doc_params.doc_id,
        kb_id=update_doc_params.kb_id,
        tenant_id=update_doc_params.tenant_id,
        doc_name=update_doc_params.doc_name,
        content=update_doc_params.content,
        metadata=update_doc_params.metadata
    )
    return success_response(result, message="文档更新成功，正在重新处理")


@router.post(
    "/delete",
    summary="删除文档",
    description="删除指定文档及其相关的向量数据",
    response_model=DocumentOperationResponseModel,
)
@inject
async def delete_document(
    request: BaseRequest[DeleteDocumentRequest],
    doc_service: DocumentService = Depends(Provide[ServiceContainer.document_service])
):
    """删除文档"""
    delete_doc_params = request.data
    result = await doc_service.delete_document(
        doc_id=delete_doc_params.doc_id,
        kb_id=delete_doc_params.kb_id,
        tenant_id=delete_doc_params.tenant_id,
        force=delete_doc_params.force
    )
    return success_response(result, message="文档删除成功")


@router.post(
    "/upload",
    summary="上传文档",
    description="上传文档文件到指定知识库",
    response_model=DocumentOperationResponseModel,
)
@inject
async def upload_document(
    request: BaseRequest[UploadDocumentRequest],
    doc_service: DocumentService = Depends(Provide[ServiceContainer.document_service])
):
    """上传文档"""
    upload_doc_params = request.data
    result = await doc_service.upload_document(
        kb_id=upload_doc_params.kb_id,
        tenant_id=upload_doc_params.tenant_id,
        file_name=upload_doc_params.file_name,
        file_content=upload_doc_params.file_content,
        file_type=upload_doc_params.file_type,
        metadata=upload_doc_params.metadata
    )
    return success_response(result, message="文档上传成功")


@router.post(
    "/process",
    summary="处理文档",
    description="对文档进行分块和向量化处理",
    response_model=DocumentOperationResponseModel,
)
@inject
async def process_document(
    request: BaseRequest[ProcessDocumentRequest],
    doc_service: DocumentService = Depends(Provide[ServiceContainer.document_service])
):
    """处理文档（分块、向量化）"""
    process_doc_params = request.data
    result = await doc_service.process_document(
        doc_id=process_doc_params.doc_id,
        kb_id=process_doc_params.kb_id,
        tenant_id=process_doc_params.tenant_id,
        chunk_size=process_doc_params.chunk_size,
        chunk_overlap=process_doc_params.chunk_overlap,
        force_reprocess=process_doc_params.force_reprocess
    )
    return success_response(result, message="文档处理任务已启动")


@router.post(
    "/search",
    summary="搜索文档",
    description="在知识库中进行语义搜索或关键词搜索",
    response_model=DocumentSearchResponseModel,
)
@inject
async def search_documents(
    request: BaseRequest[SearchDocumentsRequest],
    doc_service: DocumentService = Depends(Provide[ServiceContainer.document_service])
):
    """搜索文档"""
    search_docs_params = request.data
    result = await doc_service.search_documents(
        kb_id=search_docs_params.kb_id,
        tenant_id=search_docs_params.tenant_id,
        user_id=search_docs_params.user_id,
        query=search_docs_params.query,
        limit=search_docs_params.limit,
        similarity_threshold=search_docs_params.similarity_threshold,
        search_type=search_docs_params.search_type
    )
    return success_response(result, message="搜索完成")
