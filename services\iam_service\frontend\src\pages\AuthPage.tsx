/**
 * @file 统一认证页面
 * @description 集成所有认证功能的主页面，支持登录、注册、密码重置等
 */

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { AuthLayout } from '@/components/layout'
import { ThemeProvider, ThemeToggle } from '@/components/theme'
import { TenantSelector } from '@/components/auth'
import { 
  LoginForm, 
  RegisterForm, 
  ForgotPasswordForm, 
  ResetPasswordForm 
} from '@/components/forms'
import { TenantInfo } from '@/types'
import { useResponsive } from '@/hooks'

type AuthMode = 'login' | 'register' | 'forgot-password' | 'reset-password'

export interface AuthPageProps {
  initialMode?: AuthMode
  tenant?: TenantInfo
  resetToken?: string
  resetEmail?: string
  onAuthSuccess?: (data: any) => void
  onError?: (error: string) => void
}

export const AuthPage: React.FC<AuthPageProps> = ({
  initialMode = 'login',
  tenant: initialTenant,
  resetToken,
  resetEmail,
  onAuthSuccess,
  onError
}) => {
  const [mode, setMode] = React.useState<AuthMode>(initialMode)
  const [selectedTenant, setSelectedTenant] = React.useState<TenantInfo | undefined>(initialTenant)
  const [error, setError] = React.useState<string>('')
  const [success, setSuccess] = React.useState<string>('')

  const { isMobile } = useResponsive()

  // 处理错误信息
  const handleError = (errorMessage: string) => {
    setError(errorMessage)
    setSuccess('')
    onError?.(errorMessage)
    
    // 3秒后自动清除错误
    setTimeout(() => setError(''), 3000)
  }

  // 处理成功信息
  const handleSuccess = (data?: any, successMessage?: string) => {
    setError('')
    if (successMessage) {
      setSuccess(successMessage)
      setTimeout(() => setSuccess(''), 3000)
    }
    onAuthSuccess?.(data)
  }

  // 切换认证模式
  const switchMode = (newMode: AuthMode) => {
    setMode(newMode)
    setError('')
    setSuccess('')
  }

  // 获取页面标题
  const getPageTitle = () => {
    switch (mode) {
      case 'login':
        return '登录'
      case 'register':
        return '注册'
      case 'forgot-password':
        return '忘记密码'
      case 'reset-password':
        return '重置密码'
      default:
        return '认证'
    }
  }

  // 获取页面描述
  const getPageDescription = () => {
    switch (mode) {
      case 'login':
        return '欢迎回来，请登录您的账户'
      case 'register':
        return '创建新账户，开始您的旅程'
      case 'forgot-password':
        return '重置您的密码'
      case 'reset-password':
        return '设置新密码'
      default:
        return ''
    }
  }

  // 渲染表单内容
  const renderFormContent = () => {
    const formProps = {
      tenant: selectedTenant,
      onSuccess: handleSuccess,
      onError: handleError
    }

    switch (mode) {
      case 'login':
        return (
          <LoginForm
            {...formProps}
            onSwitchToRegister={() => switchMode('register')}
            onForgotPassword={() => switchMode('forgot-password')}
          />
        )
      
      case 'register':
        return (
          <RegisterForm
            {...formProps}
            onSwitchToLogin={() => switchMode('login')}
          />
        )
      
      case 'forgot-password':
        return (
          <ForgotPasswordForm
            {...formProps}
            onBackToLogin={() => switchMode('login')}
            onSuccess={(email) => handleSuccess(null, `重置邮件已发送到 ${email}`)}
          />
        )
      
      case 'reset-password':
        return (
          <ResetPasswordForm
            {...formProps}
            email={resetEmail}
            token={resetToken}
            onBackToLogin={() => switchMode('login')}
            onSuccess={() => {
              handleSuccess(null, '密码重置成功')
              setTimeout(() => switchMode('login'), 2000)
            }}
          />
        )
      
      default:
        return null
    }
  }

  return (
    <ThemeProvider tenant={selectedTenant}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
        {/* 主题切换按钮 */}
        <div className="absolute top-4 right-4 z-10">
          <ThemeToggle />
        </div>

        <AuthLayout
          mode={isMobile ? 'centered' : 'split'}
          tenant={selectedTenant}
        >
          <div className="w-full max-w-md mx-auto">
            {/* 页面标题 */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center mb-8"
            >
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {getPageTitle()}
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                {getPageDescription()}
              </p>
            </motion.div>

            {/* 租户选择器 */}
            {!selectedTenant && mode !== 'reset-password' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="mb-6"
              >
                <TenantSelector
                  selectedTenant={selectedTenant}
                  onTenantSelect={setSelectedTenant}
                  onError={handleError}
                  showHistory={true}
                  autoDetect={true}
                />
              </motion.div>
            )}

            {/* 全局成功信息 */}
            <AnimatePresence>
              {success && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4"
                >
                  <div className="flex items-center">
                    <svg className="h-5 w-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* 全局错误信息 */}
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"
                >
                  <div className="flex items-center">
                    <svg className="h-5 w-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* 表单内容 */}
            <motion.div
              key={mode}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8"
            >
              {renderFormContent()}
            </motion.div>

            {/* 页脚信息 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="mt-8 text-center text-sm text-gray-500 dark:text-gray-400"
            >
              <p>
                © 2024 {selectedTenant?.display_name || 'IAM System'}. 
                保留所有权利。
              </p>
              <div className="mt-2 space-x-4">
                <a 
                  href="/terms" 
                  className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                >
                  服务条款
                </a>
                <a 
                  href="/privacy" 
                  className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                >
                  隐私政策
                </a>
                <a 
                  href="/help" 
                  className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                >
                  帮助中心
                </a>
              </div>
            </motion.div>
          </div>
        </AuthLayout>
      </div>
    </ThemeProvider>
  )
}
