/**
 * @file Logo组件
 * @description 统一的Logo展示组件，支持多种尺寸和变体
 */

import React from 'react'
import clsx from 'clsx'

export interface LogoProps {
  src?: string
  alt?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'white' | 'dark'
  className?: string
  fallback?: React.ReactNode
  onClick?: () => void
}

const sizeClasses = {
  xs: 'w-6 h-6',
  sm: 'w-8 h-8',
  md: 'w-12 h-12',
  lg: 'w-16 h-16',
  xl: 'w-24 h-24'
}

const DefaultLogoIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M12 2L2 7l10 5 10-5-10-5z" />
    <path d="M2 17l10 5 10-5" />
    <path d="M2 12l10 5 10-5" />
  </svg>
)

export const Logo: React.FC<LogoProps> = ({
  src,
  alt = 'Logo',
  size = 'md',
  variant = 'default',
  className,
  fallback,
  onClick
}) => {
  const [imageError, setImageError] = React.useState(false)
  const [imageLoaded, setImageLoaded] = React.useState(false)

  const handleImageError = () => {
    setImageError(true)
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
    setImageError(false)
  }

  // 如果没有提供src或者图片加载失败，显示默认图标或fallback
  const shouldShowFallback = !src || imageError

  const logoClasses = clsx(
    'flex items-center justify-center',
    sizeClasses[size],
    {
      'text-gray-600': variant === 'default',
      'text-white': variant === 'white',
      'text-gray-900': variant === 'dark',
      'cursor-pointer': onClick,
      'transition-opacity hover:opacity-80': onClick
    },
    className
  )

  if (shouldShowFallback) {
    return (
      <div className={logoClasses} onClick={onClick}>
        {fallback || <DefaultLogoIcon className="w-full h-full" />}
      </div>
    )
  }

  return (
    <div className={logoClasses} onClick={onClick}>
      <img
        src={src}
        alt={alt}
        className={clsx(
          'w-full h-full object-contain',
          {
            'opacity-0': !imageLoaded,
            'opacity-100': imageLoaded
          }
        )}
        onError={handleImageError}
        onLoad={handleImageLoad}
      />
      {/* 加载时的占位符 */}
      {!imageLoaded && !imageError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded animate-pulse">
          <DefaultLogoIcon className="w-1/2 h-1/2 text-gray-400" />
        </div>
      )}
    </div>
  )
}
