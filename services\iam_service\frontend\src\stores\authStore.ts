/**
 * @file 认证状态管理
 * @description 使用Zustand管理用户认证状态、登录信息和权限
 * @status 开发中
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { UserInfo, TenantInfo, LoginRequest } from '@/api/types'
import * as authApi from '@/api/auth'

export interface AuthState {
  // 状态
  isAuthenticated: boolean
  isLoading: boolean
  user: UserInfo | null
  tenant: TenantInfo | null
  permissions: string[]
  sessionId: string | null
  
  // 错误状态
  error: string | null
  
  // Actions
  login: (loginData: LoginRequest) => Promise<void>
  logout: (logoutAllDevices?: boolean) => Promise<void>
  refreshToken: () => Promise<void>
  clearError: () => void
  checkAuthStatus: () => Promise<void>
  hasPermission: (permission: string) => boolean
  hasPermissions: (permissions: string[]) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  
  // 内部方法
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setAuthData: (data: {
    user: UserInfo
    tenant: TenantInfo
    permissions: string[]
    sessionId: string
  }) => void
  clearAuthData: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isAuthenticated: false,
      isLoading: false,
      user: null,
      tenant: null,
      permissions: [],
      sessionId: null,
      error: null,

      // 登录
      login: async (loginData: LoginRequest) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authApi.login(loginData)
          
          set({
            isAuthenticated: true,
            isLoading: false,
            user: response.user_info,
            tenant: response.tenant_info,
            permissions: response.permissions,
            sessionId: response.session_id,
            error: null
          })
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '登录失败，请重试'
          })
          throw error
        }
      },

      // 登出
      logout: async (logoutAllDevices = false) => {
        const { tenant, sessionId } = get()
        
        if (tenant?.tenant_id) {
          try {
            await authApi.logout({
              tenant_id: tenant.tenant_id,
              session_id: sessionId || undefined,
              logout_all_devices: logoutAllDevices
            })
          } catch (error) {
            console.error('Logout API call failed:', error)
            // 即使API调用失败，也要清除本地状态
          }
        }
        
        // 清除状态和本地存储
        authApi.clearAuthData()
        set({
          isAuthenticated: false,
          isLoading: false,
          user: null,
          tenant: null,
          permissions: [],
          sessionId: null,
          error: null
        })
      },

      // 刷新令牌
      refreshToken: async () => {
        const { tenant } = get()
        const refreshToken = authApi.getRefreshToken()
        
        if (!tenant?.tenant_id || !refreshToken) {
          throw new Error('缺少刷新令牌或租户信息')
        }
        
        try {
          await authApi.refreshToken({
            tenant_id: tenant.tenant_id,
            refresh_token: refreshToken
          })
        } catch (error) {
          // 刷新失败，清除认证状态
          get().logout()
          throw error
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null })
      },

      // 检查认证状态
      checkAuthStatus: async () => {
        const isAuth = authApi.isAuthenticated()
        
        if (!isAuth) {
          set({
            isAuthenticated: false,
            user: null,
            tenant: null,
            permissions: [],
            sessionId: null
          })
          return
        }
        
        try {
          // 验证令牌有效性
          const isValid = await authApi.verifyToken()
          
          if (!isValid) {
            // 尝试刷新令牌
            await get().refreshToken()
          }
          
          // 获取用户信息
          const userInfo = await authApi.getCurrentUser()
          const tenantId = authApi.getTenantId()
          const sessionId = localStorage.getItem('session_id')
          
          set({
            isAuthenticated: true,
            user: userInfo,
            tenant: tenantId ? { 
              tenant_id: tenantId,
              tenant_name: '',
              tenant_code: '',
              status: 'active',
              created_at: ''
            } : null,
            sessionId
          })
        } catch (error) {
          console.error('Auth status check failed:', error)
          get().logout()
        }
      },

      // 权限检查
      hasPermission: (permission: string) => {
        const { permissions } = get()
        return permissions.includes(permission)
      },

      hasPermissions: (permissions: string[]) => {
        const { permissions: userPermissions } = get()
        return permissions.every(permission => userPermissions.includes(permission))
      },

      hasAnyPermission: (permissions: string[]) => {
        const { permissions: userPermissions } = get()
        return permissions.some(permission => userPermissions.includes(permission))
      },

      // 内部方法
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setError: (error: string | null) => {
        set({ error })
      },

      setAuthData: (data) => {
        set({
          isAuthenticated: true,
          user: data.user,
          tenant: data.tenant,
          permissions: data.permissions,
          sessionId: data.sessionId
        })
      },

      clearAuthData: () => {
        authApi.clearAuthData()
        set({
          isAuthenticated: false,
          user: null,
          tenant: null,
          permissions: [],
          sessionId: null,
          error: null
        })
      }
    }),
    {
      name: 'auth-storage',
      // 只持久化部分状态，敏感信息通过localStorage单独管理
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        tenant: state.tenant,
        permissions: state.permissions,
        sessionId: state.sessionId
      })
    }
  )
)

// 导出便捷的hooks
export const useAuth = () => {
  const store = useAuthStore()
  return {
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    user: store.user,
    tenant: store.tenant,
    permissions: store.permissions,
    error: store.error,
    login: store.login,
    logout: store.logout,
    clearError: store.clearError,
    checkAuthStatus: store.checkAuthStatus,
    hasPermission: store.hasPermission,
    hasPermissions: store.hasPermissions,
    hasAnyPermission: store.hasAnyPermission
  }
}

export const useUser = () => {
  const user = useAuthStore(state => state.user)
  const tenant = useAuthStore(state => state.tenant)
  return { user, tenant }
}

export const usePermissions = () => {
  const permissions = useAuthStore(state => state.permissions)
  const hasPermission = useAuthStore(state => state.hasPermission)
  const hasPermissions = useAuthStore(state => state.hasPermissions)
  const hasAnyPermission = useAuthStore(state => state.hasAnyPermission)
  
  return {
    permissions,
    hasPermission,
    hasPermissions,
    hasAnyPermission
  }
}
