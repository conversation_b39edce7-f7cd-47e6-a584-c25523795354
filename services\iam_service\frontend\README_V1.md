# IAM 前端系统 - 第一版本 (邮箱登录)

## 版本信息
- **版本**: v1.0.0
- **发布日期**: 2024-07-25
- **功能范围**: 邮箱登录基础功能

## 已实现功能

### 🔐 认证功能
- [x] 邮箱登录
- [x] JWT令牌管理
- [x] 自动令牌刷新
- [x] 登录状态持久化
- [x] 安全登出

### 🎨 用户界面
- [x] 响应式登录页面
- [x] 表单验证和错误提示
- [x] 加载状态指示
- [x] 友好的错误信息展示

### 🏗️ 系统架构
- [x] 主布局框架 (Header + Sidebar + Content)
- [x] 路由权限控制
- [x] 状态管理 (Zustand)
- [x] API客户端封装
- [x] 错误边界处理

### 🛡️ 安全特性
- [x] 输入验证和清理
- [x] 令牌安全存储
- [x] 自动登出机制
- [x] 权限守卫组件

## 技术栈

### 前端框架
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **React Router v6** - 路由管理

### 状态管理
- **Zustand** - 轻量级状态管理
- **React Query** (准备中) - 服务器状态管理

### 样式系统
- **Tailwind CSS** - 原子化CSS框架
- **Headless UI** (准备中) - 无样式组件库

### 开发工具
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **Vitest** - 单元测试
- **Testing Library** - 组件测试

## 项目结构

```
src/
├── api/                    # API接口层
│   ├── client.ts          # HTTP客户端
│   ├── auth.ts            # 认证API
│   └── types.ts           # API类型定义
├── components/            # 通用组件
│   ├── ui/                # 基础UI组件
│   ├── common/            # 通用业务组件
│   └── layout/            # 布局组件
├── modules/               # 功能模块
│   ├── auth/              # 认证模块
│   └── dashboard/         # 仪表板模块
├── stores/                # 状态管理
│   └── authStore.ts       # 认证状态
├── utils/                 # 工具函数
│   ├── validation.ts      # 表单验证
│   └── common.ts          # 通用工具
└── App.tsx               # 应用入口
```

## 使用说明

### 启动开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm run test

# 构建生产版本
npm run build
```

### 登录测试

1. 访问 `http://localhost:5173/login`
2. 输入测试账户信息：
   - 租户ID: `test-tenant`
   - 邮箱: `<EMAIL>`
   - 密码: `password123`
3. 点击登录按钮

### API配置

在 `src/api/client.ts` 中配置后端API地址：

```typescript
const apiClient = new ApiClient('/api') // 开发环境
// const apiClient = new ApiClient('https://api.example.com') // 生产环境
```

## 开发规范

### 组件开发
- 使用函数式组件和Hooks
- 遵循TypeScript严格模式
- 组件文件包含完整的JSDoc注释
- 每个组件都有对应的测试文件

### 状态管理
- 使用Zustand进行状态管理
- 按功能模块划分store
- 状态更新使用不可变模式

### API调用
- 统一使用封装的API客户端
- 错误处理统一在客户端层
- 支持请求重试和超时控制

### 样式规范
- 使用Tailwind CSS原子类
- 组件样式通过props传递
- 响应式设计优先

## 测试策略

### 单元测试
- 组件渲染测试
- 用户交互测试
- 状态管理测试
- 工具函数测试

### 集成测试
- 登录流程测试
- 路由导航测试
- API集成测试

### 测试覆盖率目标
- 组件测试: > 80%
- 工具函数: > 90%
- 状态管理: > 85%

## 已知限制

### 当前版本限制
- 仅支持邮箱登录，不支持用户名/手机号登录
- 没有密码重置功能
- 没有注册功能
- 没有多因子认证(MFA)
- 没有会话管理功能

### 浏览器支持
- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 下一版本计划

### v1.1.0 - 密码管理
- [ ] 忘记密码功能
- [ ] 密码重置流程
- [ ] 密码强度检查
- [ ] 密码历史记录

### v1.2.0 - 用户注册
- [ ] 用户注册页面
- [ ] 邮箱验证
- [ ] 注册审核流程

### v1.3.0 - 多因子认证
- [ ] TOTP认证
- [ ] 短信验证
- [ ] 邮箱验证码

### v2.0.0 - 用户管理
- [ ] 用户列表和搜索
- [ ] 用户信息编辑
- [ ] 角色权限管理
- [ ] 批量操作

## 部署说明

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm run build
npm run preview
```

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["npm", "run", "serve"]
```

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: IAM开发团队
- 邮箱: <EMAIL>
- 文档: [项目文档](docs/)
- 问题反馈: [GitHub Issues](https://github.com/example/iam-frontend/issues)
