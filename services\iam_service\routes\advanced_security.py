"""
高级安全功能路由

提供高级安全功能的API接口，包括MFA、安全策略、威胁检测等
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel, Field
from dependency_injector.wiring import inject, Provide

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from commonlib.exceptions.exceptions import ValidationError, NotFoundError
from container import ServiceContainer
from services.advanced_security_service import AdvancedSecurityService


router = APIRouter(prefix="/security", tags=["高级安全"])


# ===== 请求数据模型 =====

class SetupMFARequestData(BaseModel):
    """
    设置MFA请求数据模型

    为用户设置多因子认证
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    mfa_type: str = Field(
        ...,
        description="MFA类型：totp(TOTP应用)、sms(短信)、email(邮箱)",
        examples=["totp"]
    )
    phone: Optional[str] = Field(
        None,
        description="手机号（SMS MFA需要）",
        examples=["13800138000"]
    )
    email: Optional[str] = Field(
        None,
        description="邮箱（Email MFA需要）",
        examples=["<EMAIL>"]
    )


class VerifyMFASetupRequestData(BaseModel):
    """
    验证MFA设置请求数据模型

    验证MFA设置并激活
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    setup_token: str = Field(
        ...,
        description="设置令牌",
        examples=["setup_token_123456"]
    )
    verification_code: str = Field(
        ...,
        description="验证码",
        examples=["123456"]
    )


class DisableMFARequestData(BaseModel):
    """
    禁用MFA请求数据模型

    禁用用户的多因子认证
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    verification_code: str = Field(
        ...,
        description="当前MFA验证码",
        examples=["123456"]
    )
    reason: Optional[str] = Field(
        None,
        description="禁用原因",
        examples=["用户请求禁用"]
    )


class SetSecurityPolicyRequestData(BaseModel):
    """
    设置安全策略请求数据模型

    设置租户或全局安全策略
    """
    tenant_id: Optional[str] = Field(
        None,
        description="租户ID（为空时设置全局策略）",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    policy_name: str = Field(
        ...,
        description="策略名称",
        examples=["password_policy"]
    )
    policy_config: Dict[str, Any] = Field(
        ...,
        description="策略配置",
        examples=[{
            "min_length": 8,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_numbers": True,
            "require_symbols": False,
            "max_age_days": 90
        }]
    )
    enabled: bool = Field(
        True,
        description="是否启用策略",
        examples=[True]
    )


class QuerySecurityEventsRequestData(BaseModel):
    """
    查询安全事件请求数据模型

    查询安全事件和威胁检测结果
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    event_type: Optional[str] = Field(
        None,
        description="事件类型：login_anomaly(登录异常)、brute_force(暴力破解)、privilege_escalation(权限提升)",
        examples=["login_anomaly"]
    )
    severity: Optional[str] = Field(
        None,
        description="严重程度：low(低)、medium(中)、high(高)、critical(严重)",
        examples=["high"]
    )
    start_time: Optional[str] = Field(
        None,
        description="开始时间（ISO格式）",
        examples=["2025-01-01T00:00:00.000000"]
    )
    end_time: Optional[str] = Field(
        None,
        description="结束时间（ISO格式）",
        examples=["2025-01-31T23:59:59.999999"]
    )
    user_id: Optional[str] = Field(
        None,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    ip_address: Optional[str] = Field(
        None,
        description="IP地址",
        examples=["*************"]
    )
    status: Optional[str] = Field(
        None,
        description="处理状态：pending(待处理)、investigating(调查中)、resolved(已解决)、false_positive(误报)",
        examples=["pending"]
    )
    page: int = Field(
        1,
        description="页码",
        ge=1,
        examples=[1]
    )
    page_size: int = Field(
        20,
        description="每页数量",
        ge=1,
        le=100,
        examples=[20]
    )


class UpdateSecurityEventRequestData(BaseModel):
    """
    更新安全事件请求数据模型

    更新安全事件的处理状态
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    event_id: str = Field(
        ...,
        description="事件ID",
        examples=["event_550e8400-e29b-41d4-a716-446655440000"]
    )
    status: str = Field(
        ...,
        description="处理状态",
        examples=["resolved"]
    )
    notes: Optional[str] = Field(
        None,
        description="处理备注",
        examples=["已确认为正常登录行为"]
    )
    assigned_to: Optional[str] = Field(
        None,
        description="分配给（用户ID）",
        examples=["admin_550e8400-e29b-41d4-a716-446655440000"]
    )


class GenerateBackupCodesRequestData(BaseModel):
    """
    生成备用恢复码请求数据模型

    为用户生成MFA备用恢复码
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    count: int = Field(
        10,
        description="生成数量",
        ge=5,
        le=20,
        examples=[10]
    )


# ===== 响应数据模型 =====

class MFASetupResponse(BaseModel):
    """MFA设置响应数据"""
    setup_token: str = Field(..., description="设置令牌")
    qr_code_url: Optional[str] = Field(None, description="二维码URL（TOTP类型）")
    secret_key: Optional[str] = Field(None, description="密钥（TOTP类型）")
    backup_codes: List[str] = Field(..., description="备用恢复码")
    expires_at: str = Field(..., description="设置令牌过期时间")


class MFAVerifyResponse(BaseModel):
    """MFA验证响应数据"""
    user_id: str = Field(..., description="用户ID")
    mfa_enabled: bool = Field(..., description="MFA是否已启用")
    mfa_type: str = Field(..., description="MFA类型")
    enabled_at: str = Field(..., description="启用时间")


class SecurityPolicyResponse(BaseModel):
    """安全策略响应数据"""
    policy_id: str = Field(..., description="策略ID")
    policy_name: str = Field(..., description="策略名称")
    policy_config: Dict[str, Any] = Field(..., description="策略配置")
    enabled: bool = Field(..., description="是否启用")
    updated_at: str = Field(..., description="更新时间")


class SecurityEventInfo(BaseModel):
    """安全事件信息模型"""
    event_id: str = Field(..., description="事件ID")
    tenant_id: str = Field(..., description="租户ID")
    event_type: str = Field(..., description="事件类型")
    severity: str = Field(..., description="严重程度")
    title: str = Field(..., description="事件标题")
    description: str = Field(..., description="事件描述")
    user_id: Optional[str] = Field(None, description="相关用户ID")
    username: Optional[str] = Field(None, description="用户名")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    details: Dict[str, Any] = Field(..., description="事件详情")
    status: str = Field(..., description="处理状态")
    assigned_to: Optional[str] = Field(None, description="分配给")
    notes: Optional[str] = Field(None, description="处理备注")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class QuerySecurityEventsResponse(BaseModel):
    """查询安全事件响应数据"""
    events: List[SecurityEventInfo] = Field(..., description="安全事件列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    has_next: bool = Field(..., description="是否有下一页")


class BackupCodesResponse(BaseModel):
    """备用恢复码响应数据"""
    user_id: str = Field(..., description="用户ID")
    backup_codes: List[str] = Field(..., description="备用恢复码")
    generated_at: str = Field(..., description="生成时间")
    expires_at: str = Field(..., description="过期时间")


# ===== 响应模型包装类 =====

class MFASetupResponseModel(SuccessResponse[MFASetupResponse]):
    """MFA设置响应模型"""
    data: MFASetupResponse = Field(..., description="MFA设置结果")


class MFAVerifyResponseModel(SuccessResponse[MFAVerifyResponse]):
    """MFA验证响应模型"""
    data: MFAVerifyResponse = Field(..., description="MFA验证结果")


class SecurityPolicyResponseModel(SuccessResponse[SecurityPolicyResponse]):
    """安全策略响应模型"""
    data: SecurityPolicyResponse = Field(..., description="安全策略信息")


class QuerySecurityEventsResponseModel(SuccessResponse[QuerySecurityEventsResponse]):
    """查询安全事件响应模型"""
    data: QuerySecurityEventsResponse = Field(..., description="安全事件查询结果")


class BackupCodesResponseModel(SuccessResponse[BackupCodesResponse]):
    """备用恢复码响应模型"""
    data: BackupCodesResponse = Field(..., description="备用恢复码信息")


# ===== 路由端点 =====

@router.post(
    "/mfa/setup",
    summary="设置MFA",
    description="""
    设置多因子认证

    **功能说明：**
    - 为用户设置多因子认证
    - 支持TOTP、SMS、Email等类型
    - 生成备用恢复码
    - 返回设置令牌和二维码

    **MFA类型：**
    - TOTP：基于时间的一次性密码（推荐）
    - SMS：短信验证码
    - Email：邮箱验证码

    **返回数据：**
    - 设置令牌
    - 二维码URL（TOTP）
    - 备用恢复码
    """,
    response_model=MFASetupResponseModel,
    responses={
        200: {"description": "MFA设置成功"},
        400: {"description": "请求参数错误"},
        404: {"description": "用户不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["多因子认证"]
)
@inject
async def setup_mfa(
    request: BaseRequest[SetupMFARequestData],
    security_service: AdvancedSecurityService = Depends(Provide[ServiceContainer.advanced_security_service])
):
    """
    设置MFA

    为用户设置多因子认证
    """
    mfa_params = request.data
    result = await security_service.setup_mfa(
        tenant_id=mfa_params.tenant_id,
        user_id=mfa_params.user_id,
        mfa_type=mfa_params.mfa_type,
        phone=mfa_params.phone,
        email=mfa_params.email
    )
    return success_response(result, message="MFA设置成功")


@router.post(
    "/mfa/verify-setup",
    summary="验证MFA设置",
    description="""
    验证MFA设置

    **功能说明：**
    - 验证MFA设置并激活
    - 确认用户能正确使用MFA
    - 激活多因子认证
    - 记录安全事件

    **验证流程：**
    1. 用户输入验证码
    2. 系统验证码正确性
    3. 激活MFA功能
    4. 返回激活结果

    **返回数据：**
    - 用户ID
    - MFA启用状态
    - 启用时间
    """,
    response_model=MFAVerifyResponseModel,
    responses={
        200: {"description": "MFA验证成功"},
        400: {"description": "验证码错误"},
        404: {"description": "设置令牌不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["多因子认证"]
)
@inject
async def verify_mfa_setup(
    request: BaseRequest[VerifyMFASetupRequestData],
    security_service: AdvancedSecurityService = Depends(Provide[ServiceContainer.advanced_security_service])
):
    """
    验证MFA设置

    验证MFA设置并激活
    """
    verify_params = request.data
    result = await security_service.verify_mfa_setup(
        tenant_id=verify_params.tenant_id,
        user_id=verify_params.user_id,
        setup_token=verify_params.setup_token,
        verification_code=verify_params.verification_code
    )
    return success_response(result, message="MFA验证成功")


@router.post(
    "/mfa/disable",
    summary="禁用MFA",
    description="""
    禁用多因子认证

    **功能说明：**
    - 禁用用户的多因子认证
    - 需要当前MFA验证码确认
    - 清除MFA相关数据
    - 记录安全事件

    **安全措施：**
    - 需要提供当前有效的MFA验证码
    - 记录禁用原因和操作者
    - 发送安全通知

    **返回数据：**
    - 禁用确认
    - 禁用时间
    """,
    response_model=SuccessResponse[Dict[str, Any]],
    responses={
        200: {"description": "MFA禁用成功"},
        400: {"description": "验证码错误"},
        404: {"description": "用户不存在或MFA未启用"},
        500: {"description": "服务器内部错误"}
    },
    tags=["多因子认证"]
)
@inject
async def disable_mfa(
    request: BaseRequest[DisableMFARequestData],
    security_service: AdvancedSecurityService = Depends(Provide[ServiceContainer.advanced_security_service])
):
    """
    禁用MFA

    禁用用户的多因子认证
    """
    disable_params = request.data
    result = await security_service.disable_mfa(
        tenant_id=disable_params.tenant_id,
        user_id=disable_params.user_id,
        verification_code=disable_params.verification_code,
        reason=disable_params.reason
    )
    return success_response(result, message="MFA禁用成功")


@router.post(
    "/mfa/backup-codes",
    summary="生成备用恢复码",
    description="""
    生成备用恢复码

    **功能说明：**
    - 为用户生成MFA备用恢复码
    - 用于MFA设备丢失时的恢复
    - 每个恢复码只能使用一次
    - 支持重新生成

    **使用场景：**
    - MFA设备丢失或损坏
    - 无法接收SMS或邮箱验证码
    - 紧急访问需求

    **返回数据：**
    - 备用恢复码列表
    - 生成时间
    - 过期时间
    """,
    response_model=BackupCodesResponseModel,
    responses={
        200: {"description": "备用恢复码生成成功"},
        400: {"description": "请求参数错误"},
        404: {"description": "用户不存在或MFA未启用"},
        500: {"description": "服务器内部错误"}
    },
    tags=["多因子认证"]
)
@inject
async def generate_backup_codes(
    request: BaseRequest[GenerateBackupCodesRequestData],
    security_service: AdvancedSecurityService = Depends(Provide[ServiceContainer.advanced_security_service])
):
    """
    生成备用恢复码

    为用户生成MFA备用恢复码
    """
    backup_params = request.data
    result = await security_service.generate_backup_codes(
        tenant_id=backup_params.tenant_id,
        user_id=backup_params.user_id,
        count=backup_params.count
    )
    return success_response(result, message="备用恢复码生成成功")


@router.post(
    "/policy/set",
    summary="设置安全策略",
    description="""
    设置安全策略

    **功能说明：**
    - 设置租户或全局安全策略
    - 支持多种策略类型
    - 实时生效
    - 支持策略继承

    **策略类型：**
    - password_policy：密码策略
    - session_policy：会话策略
    - login_policy：登录策略
    - access_policy：访问策略

    **返回数据：**
    - 策略ID
    - 策略配置
    - 更新时间
    """,
    response_model=SecurityPolicyResponseModel,
    responses={
        200: {"description": "安全策略设置成功"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    },
    tags=["安全策略"]
)
@inject
async def set_security_policy(
    request: BaseRequest[SetSecurityPolicyRequestData],
    security_service: AdvancedSecurityService = Depends(Provide[ServiceContainer.advanced_security_service])
):
    """
    设置安全策略

    设置租户或全局安全策略
    """
    policy_params = request.data
    result = await security_service.set_security_policy(
        tenant_id=policy_params.tenant_id,
        policy_name=policy_params.policy_name,
        policy_config=policy_params.policy_config,
        enabled=policy_params.enabled
    )
    return success_response(result, message="安全策略设置成功")


@router.post(
    "/events/query",
    summary="查询安全事件",
    description="""
    查询安全事件

    **功能说明：**
    - 查询安全事件和威胁检测结果
    - 支持多条件过滤
    - 支持分页查询
    - 支持状态管理

    **事件类型：**
    - login_anomaly：登录异常
    - brute_force：暴力破解
    - privilege_escalation：权限提升
    - data_access：数据访问异常

    **返回数据：**
    - 安全事件列表
    - 事件详情
    - 处理状态
    """,
    response_model=QuerySecurityEventsResponseModel,
    responses={
        200: {"description": "查询成功"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    },
    tags=["威胁检测"]
)
@inject
async def query_security_events(
    request: BaseRequest[QuerySecurityEventsRequestData],
    security_service: AdvancedSecurityService = Depends(Provide[ServiceContainer.advanced_security_service])
):
    """
    查询安全事件

    查询安全事件和威胁检测结果
    """
    query_params = request.data
    result = await security_service.query_security_events(
        tenant_id=query_params.tenant_id,
        event_type=query_params.event_type,
        severity=query_params.severity,
        start_time=query_params.start_time,
        end_time=query_params.end_time,
        user_id=query_params.user_id,
        ip_address=query_params.ip_address,
        status=query_params.status,
        page=query_params.page,
        page_size=query_params.page_size
    )
    return success_response(result, message="查询成功")


@router.post(
    "/events/update",
    summary="更新安全事件",
    description="""
    更新安全事件

    **功能说明：**
    - 更新安全事件的处理状态
    - 添加处理备注
    - 分配处理人员
    - 记录处理历史

    **处理状态：**
    - pending：待处理
    - investigating：调查中
    - resolved：已解决
    - false_positive：误报

    **返回数据：**
    - 更新结果
    - 更新时间
    """,
    response_model=SuccessResponse[Dict[str, Any]],
    responses={
        200: {"description": "更新成功"},
        400: {"description": "请求参数错误"},
        404: {"description": "安全事件不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["威胁检测"]
)
@inject
async def update_security_event(
    request: BaseRequest[UpdateSecurityEventRequestData],
    security_service: AdvancedSecurityService = Depends(Provide[ServiceContainer.advanced_security_service])
):
    """
    更新安全事件

    更新安全事件的处理状态
    """
    update_params = request.data
    result = await security_service.update_security_event(
        tenant_id=update_params.tenant_id,
        event_id=update_params.event_id,
        status=update_params.status,
        notes=update_params.notes,
        assigned_to=update_params.assigned_to
    )
    return success_response(result, message="安全事件更新成功")
