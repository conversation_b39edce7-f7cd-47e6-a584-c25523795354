"""
Dependency Injection测试

测试依赖注入机制的各种场景
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from dependency_injector.wiring import inject, Provide
from dependency_injector import providers
from commonlib.core.containers.infra_container import InfraContainer
from container import ServiceContainer


class TestDependencyInjection:
    """Dependency Injection测试类"""

    @pytest.mark.unit
    def test_inject_decorator_basic(self):
        """测试基本的@inject装饰器"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.decorator_redis_repo.override(mock_redis_repo)
        
        # 定义使用依赖注入的函数
        @inject
        def test_function(redis_repo = Provide[InfraContainer.decorator_redis_repo]):
            return redis_repo
        
        # Mock容器的wire状态
        with patch.object(container, 'wire'):
            container.wire(modules=[__name__])
            
            # 测试依赖注入
            result = test_function()
            assert result is mock_redis_repo

    @pytest.mark.unit
    def test_inject_with_multiple_dependencies(self):
        """测试多个依赖的注入"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock多个依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_jwt_manager = MagicMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        container.jwt_manager.override(mock_jwt_manager)
        
        # 定义使用多个依赖注入的函数
        @inject
        def test_function(
            session = Provide[ServiceContainer.session],
            redis_repo = Provide[ServiceContainer.redis_repo],
            jwt_manager = Provide[ServiceContainer.jwt_manager]
        ):
            return session, redis_repo, jwt_manager
        
        # Mock容器的wire状态
        with patch.object(container, 'wire'):
            container.wire(modules=[__name__])
            
            # 测试多个依赖注入
            session, redis_repo, jwt_manager = test_function()
            assert session is mock_session
            assert redis_repo is mock_redis_repo
            assert jwt_manager is mock_jwt_manager

    @pytest.mark.unit
    def test_inject_with_async_function(self):
        """测试异步函数的依赖注入"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖
        mock_session = AsyncMock()
        container.session.override(mock_session)
        
        # 定义异步函数使用依赖注入
        @inject
        async def async_test_function(session = Provide[ServiceContainer.session]):
            return session
        
        # Mock容器的wire状态
        with patch.object(container, 'wire'):
            container.wire(modules=[__name__])
            
            # 测试异步依赖注入
            import asyncio
            result = asyncio.run(async_test_function())
            assert result is mock_session

    @pytest.mark.unit
    def test_inject_with_class_method(self):
        """测试类方法的依赖注入"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.decorator_redis_repo.override(mock_redis_repo)
        
        class TestService:
            @inject
            def __init__(self, redis_repo = Provide[InfraContainer.decorator_redis_repo]):
                self.redis_repo = redis_repo
            
            @inject
            def get_data(self, redis_repo = Provide[InfraContainer.decorator_redis_repo]):
                return redis_repo
        
        # Mock容器的wire状态
        with patch.object(container, 'wire'):
            container.wire(modules=[__name__])
            
            # 测试类的依赖注入
            service = TestService()
            assert service.redis_repo is mock_redis_repo
            
            data = service.get_data()
            assert data is mock_redis_repo

    @pytest.mark.unit
    def test_provide_with_different_provider_types(self):
        """测试不同provider类型的Provide"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证不同类型的provider可以被Provide使用
        factory_provide = Provide[ServiceContainer.tenant_service]
        singleton_provide = Provide[ServiceContainer.sms_service]
        callable_provide = Provide[ServiceContainer.session]
        
        # 验证Provide对象被正确创建
        assert factory_provide is not None
        assert singleton_provide is not None
        assert callable_provide is not None

    @pytest.mark.unit
    def test_inject_with_default_values(self):
        """测试带默认值的依赖注入"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.decorator_redis_repo.override(mock_redis_repo)
        
        # 定义带默认值的函数
        @inject
        def test_function(
            redis_repo = Provide[InfraContainer.decorator_redis_repo],
            default_param = "default_value"
        ):
            return redis_repo, default_param
        
        # Mock容器的wire状态
        with patch.object(container, 'wire'):
            container.wire(modules=[__name__])
            
            # 测试依赖注入和默认值
            redis_repo, default_param = test_function()
            assert redis_repo is mock_redis_repo
            assert default_param == "default_value"
            
            # 测试覆盖默认值
            redis_repo, custom_param = test_function(default_param="custom_value")
            assert redis_repo is mock_redis_repo
            assert custom_param == "custom_value"

    @pytest.mark.unit
    def test_inject_error_handling(self):
        """测试依赖注入错误处理"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 不override任何依赖，让其保持未配置状态
        
        # 定义使用依赖注入的函数
        @inject
        def test_function(redis_repo = Provide[InfraContainer.decorator_redis_repo]):
            return redis_repo
        
        # 在没有wire的情况下测试
        with pytest.raises(Exception):
            test_function()

    @pytest.mark.unit
    def test_inject_with_nested_dependencies(self):
        """测试嵌套依赖的注入"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock嵌套依赖
        mock_redis_client = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_script_manager = MagicMock()
        
        container.redis_client.override(mock_redis_client)
        container.decorator_redis_repo.override(mock_redis_repo)
        container.redis_script_manager.override(mock_script_manager)
        
        # 定义使用嵌套依赖的函数
        @inject
        def test_function(
            redis_client = Provide[InfraContainer.redis_client],
            redis_repo = Provide[InfraContainer.decorator_redis_repo],
            script_manager = Provide[InfraContainer.redis_script_manager]
        ):
            return redis_client, redis_repo, script_manager
        
        # Mock容器的wire状态
        with patch.object(container, 'wire'):
            container.wire(modules=[__name__])
            
            # 测试嵌套依赖注入
            client, repo, manager = test_function()
            assert client is mock_redis_client
            assert repo is mock_redis_repo
            assert manager is mock_script_manager

    @pytest.mark.unit
    def test_inject_with_override_at_runtime(self):
        """测试运行时override依赖"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 初始Mock
        initial_redis_repo = AsyncMock()
        container.decorator_redis_repo.override(initial_redis_repo)
        
        @inject
        def test_function(redis_repo = Provide[InfraContainer.decorator_redis_repo]):
            return redis_repo
        
        # Mock容器的wire状态
        with patch.object(container, 'wire'):
            container.wire(modules=[__name__])
            
            # 测试初始依赖
            result1 = test_function()
            assert result1 is initial_redis_repo
            
            # 运行时override
            new_redis_repo = AsyncMock()
            container.decorator_redis_repo.override(new_redis_repo)
            
            # 测试新的依赖
            result2 = test_function()
            assert result2 is new_redis_repo

    @pytest.mark.unit
    def test_inject_with_partial_application(self):
        """测试部分应用的依赖注入"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖
        mock_session = AsyncMock()
        container.session.override(mock_session)
        
        # 定义带多个参数的函数
        @inject
        def test_function(
            user_id: str,
            session = Provide[ServiceContainer.session],
            action: str = "default_action"
        ):
            return user_id, session, action
        
        # Mock容器的wire状态
        with patch.object(container, 'wire'):
            container.wire(modules=[__name__])
            
            # 测试部分应用
            user_id, session, action = test_function("user123")
            assert user_id == "user123"
            assert session is mock_session
            assert action == "default_action"
            
            # 测试覆盖所有参数
            user_id, session, action = test_function("user456", action="custom_action")
            assert user_id == "user456"
            assert session is mock_session
            assert action == "custom_action"

    @pytest.mark.unit
    def test_inject_with_container_reset(self):
        """测试容器重置后的依赖注入"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 初始设置
        mock_redis_repo = AsyncMock()
        container.decorator_redis_repo.override(mock_redis_repo)
        
        @inject
        def test_function(redis_repo = Provide[InfraContainer.decorator_redis_repo]):
            return redis_repo
        
        # Mock容器的wire状态
        with patch.object(container, 'wire'):
            container.wire(modules=[__name__])
            
            # 测试初始状态
            result1 = test_function()
            assert result1 is mock_redis_repo
            
            # 重置容器
            container.decorator_redis_repo.reset_override()
            
            # 测试重置后的行为（应该使用原始provider）
            with pytest.raises(Exception):
                test_function()
