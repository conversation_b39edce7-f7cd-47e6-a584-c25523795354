from app.core.container import ServiceContainer
from app.tasks.scheduler_mgm import dispatch_scheduler
from commonlib.core.containers.config_container import (ConfigContainer,
                                                        set_up_config_di)
from commonlib.core.containers.infra_container import InfraContainer
from domain_common.app_builder.default_app_factory import AppInitializer

config: ConfigContainer = set_up_config_di()
infra = InfraContainer(config=config)
services = ServiceContainer(config=config, infra=infra)
scheduler_modules = dispatch_scheduler()
app = AppInitializer(
    config, infra, services, wire_modules=scheduler_modules
).create_app()
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8089, log_config=None)
