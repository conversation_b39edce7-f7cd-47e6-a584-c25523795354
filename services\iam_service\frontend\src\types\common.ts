/**
 * @file 通用类型定义
 * @description 项目中通用的TypeScript类型定义
 */

// 基础响应接口
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: ApiError
  meta?: ResponseMeta
}

// API错误接口
export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
  field?: string
  timestamp?: string
}

// 响应元数据接口
export interface ResponseMeta {
  total?: number
  page?: number
  per_page?: number
  has_more?: boolean
  request_id?: string
  timestamp?: string
}

// 分页请求接口
export interface PaginationRequest {
  page?: number
  per_page?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 分页响应接口
export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  per_page: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}

// 搜索请求接口
export interface SearchRequest extends PaginationRequest {
  query?: string
  filters?: Record<string, any>
}

// 选项接口
export interface Option<T = string> {
  label: string
  value: T
  disabled?: boolean
  description?: string
  icon?: string
}

// 选择器选项接口
export interface SelectOption extends Option {
  group?: string
  selected?: boolean
}

// 菜单项接口
export interface MenuItem {
  id: string
  label: string
  icon?: string
  url?: string
  action?: () => void
  children?: MenuItem[]
  disabled?: boolean
  hidden?: boolean
  badge?: string | number
  shortcut?: string
}

// 导航项接口
export interface NavItem extends MenuItem {
  active?: boolean
  exact?: boolean
  permission?: string
}

// 表单字段配置接口
export interface FieldConfig {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'checkbox' | 'radio' | 'textarea'
  placeholder?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  options?: Option[]
  validation?: ValidationRule[]
  description?: string
  defaultValue?: any
}

// 验证规则接口
export interface ValidationRule {
  type: 'required' | 'email' | 'min' | 'max' | 'pattern' | 'custom'
  value?: any
  message: string
}

// 表单配置接口
export interface FormConfig {
  fields: FieldConfig[]
  layout?: 'vertical' | 'horizontal' | 'inline'
  submitText?: string
  resetText?: string
  showReset?: boolean
  autoFocus?: boolean
}

// 加载状态接口
export interface LoadingState {
  isLoading: boolean
  loadingText?: string
  progress?: number
}

// 错误状态接口
export interface ErrorState {
  hasError: boolean
  error?: Error | string
  errorCode?: string
  retryable?: boolean
}

// 异步状态接口
export interface AsyncState<T = any> extends LoadingState, ErrorState {
  data?: T
  lastUpdated?: string
}

// 模态框配置接口
export interface ModalConfig {
  title?: string
  content?: React.ReactNode
  footer?: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
  maskClosable?: boolean
  keyboard?: boolean
  centered?: boolean
  destroyOnClose?: boolean
}

// 通知配置接口
export interface NotificationConfig {
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  closable?: boolean
  action?: {
    label: string
    onClick: () => void
  }
}

// 确认对话框配置接口
export interface ConfirmConfig {
  title?: string
  content: string
  okText?: string
  cancelText?: string
  type?: 'info' | 'warning' | 'error'
  onOk?: () => void | Promise<void>
  onCancel?: () => void
}

// 文件上传配置接口
export interface UploadConfig {
  accept?: string
  multiple?: boolean
  maxSize?: number
  maxCount?: number
  directory?: boolean
  beforeUpload?: (file: File) => boolean | Promise<boolean>
  onProgress?: (percent: number) => void
  onSuccess?: (response: any, file: File) => void
  onError?: (error: Error, file: File) => void
}

// 文件信息接口
export interface FileInfo {
  uid: string
  name: string
  size: number
  type: string
  url?: string
  status: 'uploading' | 'done' | 'error' | 'removed'
  percent?: number
  response?: any
  error?: Error
}

// 设备信息接口
export interface DeviceInfo {
  type: 'desktop' | 'tablet' | 'mobile'
  os: string
  browser: string
  version: string
  userAgent: string
  screen: {
    width: number
    height: number
  }
  viewport: {
    width: number
    height: number
  }
}

// 位置信息接口
export interface LocationInfo {
  country?: string
  region?: string
  city?: string
  timezone?: string
  ip?: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}

// 键值对接口
export interface KeyValuePair<T = string> {
  key: string
  value: T
}

// 时间范围接口
export interface TimeRange {
  start: string | Date
  end: string | Date
}

// 颜色配置接口
export interface ColorConfig {
  primary: string
  secondary?: string
  background?: string
  text?: string
  border?: string
}

// 尺寸配置接口
export interface SizeConfig {
  width?: number | string
  height?: number | string
  minWidth?: number | string
  minHeight?: number | string
  maxWidth?: number | string
  maxHeight?: number | string
}

// 间距配置接口
export interface SpacingConfig {
  margin?: string | number
  padding?: string | number
  gap?: string | number
}

// 组件基础属性接口
export interface BaseComponentProps {
  className?: string
  style?: React.CSSProperties
  id?: string
  testId?: string
  children?: React.ReactNode
}

// 可点击组件属性接口
export interface ClickableProps {
  onClick?: (event: React.MouseEvent) => void
  onDoubleClick?: (event: React.MouseEvent) => void
  disabled?: boolean
  loading?: boolean
}

// 可聚焦组件属性接口
export interface FocusableProps {
  onFocus?: (event: React.FocusEvent) => void
  onBlur?: (event: React.FocusEvent) => void
  autoFocus?: boolean
  tabIndex?: number
}

// 表单控件属性接口
export interface FormControlProps extends FocusableProps {
  name?: string
  value?: any
  defaultValue?: any
  onChange?: (value: any, event?: React.ChangeEvent) => void
  placeholder?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  error?: string
  helperText?: string
}

// 环境变量接口
export interface EnvConfig {
  NODE_ENV: 'development' | 'production' | 'test'
  API_BASE_URL: string
  APP_NAME: string
  APP_VERSION: string
  ENABLE_MOCK: boolean
  LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error'
}
