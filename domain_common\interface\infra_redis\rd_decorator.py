import time
from functools import wraps
from inspect import iscoroutinefunction

from commonlib.core.containers.infra_container import InfraContainer
from commonlib.core.logging.tsif_logging import app_logger
from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.utils.func_toolkit import fullname
from dependency_injector.wiring import Provide, inject
from domain_common.interface.infra_redis.decorators.cache import \
    RedisCacheDecorator


def redis_cache_decorator(
    ttl: int = None,
    key_prefix: str = None,
    ignore_errors: bool = None,
    need_cache: bool = True,
    need_hash: bool = True,
    config: dict = None,
):
    """Redis cache decorator for caching function results.

    This decorator can be used in two ways:
    1. Directly with parameters: @redis_cache_decorator(ttl=300, key_prefix="my_prefix")
    2. Through the InfraContainer helper: @InfraContainer.with_cache(ttl_seconds=300)

    Args:
        ttl: Time-to-live in seconds for cached items
        key_prefix: Optional prefix for cache keys
        ignore_errors: Whether to ignore Redis errors
        need_cache: Whether to enable caching
        need_hash: Whether to hash the cache key
        config: Configuration dictionary (overrides individual parameters if provided)

    Returns:
        A decorator that caches function results in Redis
    """
    # If configs is provided, use it instead of individual parameters
    config_dict = config or {
        "ttl": ttl,
        "key_prefix": key_prefix,
        "ignore_errors": ignore_errors,
        "need_cache": need_cache,
        "need_hash": need_hash,
    }

    def decorator_wrapper(func):
        @wraps(func)
        @inject  # 注意，使用inject的使用，需要di使用wire显示的覆盖范围
        async def wrapped(
            *args,
            decorator_instance: RedisCacheDecorator = Provide[
                InfraContainer.redis_cache_decorator
            ],
            **kwargs,
        ):
            decorated_func = decorator_instance(config_dict)(func)
            return await decorated_func(*args, **kwargs)

        return wrapped

    return decorator_wrapper


def redis_distributed_task_preemption_decorator(
    name: str = None, px: int = 3000, nx: bool = True
):
    """Redis distributed task preemption decorator.

    This decorator ensures that a function is only executed once across multiple instances
    of the application at the same time. It's useful for scheduled tasks that should not
    run concurrently.

    This decorator can be used in two ways:
    1. Directly with parameters: @redis_distributed_task_preemption_decorator(name="my_task", px=5000)
    2. Through the InfraContainer helper: @InfraContainer.with_distributed_lock(name="my_task", lock_ttl_ms=5000)

    Args:
        name: Optional name for the lock (defaults to the function's fully qualified name)
        px: Time-to-live for the lock in milliseconds
        nx: Whether to use NX mode (only set if the key doesn't exist)

    Returns:
        A decorator that ensures distributed execution of a function
    """

    def decorator_wrapper(func):
        @wraps(func)
        @inject
        async def wrapped(
            *args,
            redis_repo: RedisRepository = Provide[InfraContainer.decorator_redis_repo],
            **kwargs,
        ):
            # Use provided name or generate one from the function
            task_name = name or fullname(func)
            task_key = f"nx_schedule_job:{task_name}"
            task_value = time.perf_counter()

            # Try to acquire the lock
            acquired = await redis_repo.set(task_key, task_value, px=px, nx=nx)

            if acquired:
                try:
                    # Execute the function based on whether it's async or not
                    if iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    app_logger.error(
                        f"[DistributedTask:{task_name}] execution error: {e}",
                        exception=True,
                    )
                    raise  # Re-raise the exception to allow proper error handling
                finally:
                    elapsed = time.perf_counter() - task_value
                    app_logger.info(
                        f"[DistributedTask:{task_name}] executed in {elapsed:.3f}s"
                    )
            else:
                app_logger.info(
                    f"[DistributedTask:{task_name}] skipped (already running elsewhere)"
                )
                # Return None when the task is skipped
                return None

        return wrapped

    return decorator_wrapper
