# IAM 服务 Makefile

.PHONY: help install dev test lint format clean build run stop logs

# 默认目标
help:
	@echo "IAM 服务管理命令："
	@echo "  install     - 安装依赖"
	@echo "  dev         - 开发模式启动"
	@echo "  test        - 运行测试"
	@echo "  lint        - 代码检查"
	@echo "  format      - 代码格式化"
	@echo "  clean       - 清理临时文件"
	@echo "  build       - 构建 Docker 镜像"
	@echo "  run         - 启动服务（Docker）"
	@echo "  stop        - 停止服务"
	@echo "  logs        - 查看日志"

# 安装依赖
install:
	pip install -r requirements.txt

# 开发模式启动
dev:
	python main.py

# 运行测试
test:
	pytest -v --cov=services --cov=routes --cov=tasks

# 代码检查
lint:
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

# 代码格式化
format:
	black .
	isort .

# 清理临时文件
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".coverage" -delete
	find . -type d -name "htmlcov" -exec rm -rf {} +

# 构建 Docker 镜像
build:
	docker build -t iam-service:latest .

# 启动服务（Docker Compose）
run:
	docker-compose up -d

# 启动服务和工具（包含管理界面）
run-with-tools:
	docker-compose --profile tools up -d

# 停止服务
stop:
	docker-compose down

# 查看日志
logs:
	docker-compose logs -f iam-service

# 进入容器
shell:
	docker-compose exec iam-service bash

# 数据库迁移
migrate:
	# TODO: 添加数据库迁移命令
	@echo "数据库迁移功能待实现"

# 备份数据库
backup:
	docker-compose exec postgres pg_dump -U postgres iam_db > backup_$(shell date +%Y%m%d_%H%M%S).sql

# 恢复数据库
restore:
	@echo "请指定备份文件: make restore-from FILE=backup_file.sql"

restore-from:
	docker-compose exec -T postgres psql -U postgres iam_db < $(FILE)

# 重启服务
restart: stop run

# 查看服务状态
status:
	docker-compose ps

# 更新服务
update: stop build run

# 生产部署
deploy:
	@echo "生产部署流程："
	@echo "1. 备份数据库"
	@echo "2. 构建新镜像"
	@echo "3. 停止旧服务"
	@echo "4. 启动新服务"
	@echo "5. 验证服务状态"
	make backup
	make build
	make stop
	make run
	sleep 10
	make status
