"""
容器测试Fixtures

提供容器测试所需的各种fixtures
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path
import tempfile
import os

from dependency_injector import containers, providers
from commonlib.core.containers.config_container import ConfigContainer, set_up_config_di
from commonlib.core.containers.infra_container import InfraContainer
from container import ServiceContainer
from commonlib.configs.base_setting import AppSettings


@pytest.fixture
def test_app_settings():
    """测试用的应用设置"""
    return AppSettings(
        app_name="test_iam_service",
        debug=True,
    )


@pytest.fixture
def test_config_file():
    """创建临时测试配置文件"""
    config_data = {
        "application": {
            "project_name": "test_iam_service",
            "debug": True,
            "title": "Test IAM Service",
            "description": "Test IAM Service API",
            "docs_url": "/docs",
            "openapi_url": "/openapi.json",
            "redoc_url": "/redoc"
        },
        "persistence": {
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 15,
                "password": None,
                "ssl": False,
                "connection_pool_size": 10
            },
            "mysql": {
                "host": "localhost",
                "port": 3306,
                "database": "test_iam",
                "username": "test",
                "password": "test"
            },
            "postgres": {
                "host": "localhost",
                "port": 5432,
                "database": "test_iam",
                "username": "test",
                "password": "test"
            }
        },
        "connection_priority": {
            "redis": 1,
            "postgres": 2,
            "mysql": 3
        },
        "log_dir": "/tmp/test_logs"
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        import json
        json.dump(config_data, f)
        temp_path = f.name
    
    yield temp_path
    
    # 清理临时文件
    os.unlink(temp_path)


@pytest.fixture
def mock_config_container(test_app_settings, test_config_file):
    """Mock的配置容器"""
    with patch('commonlib.core.containers.config_container.load_dotenv'):
        container = ConfigContainer()
        
        # Mock配置加载器
        mock_loader = MagicMock()
        mock_config = MagicMock()
        mock_config.application.project_name = "test_iam_service"
        mock_config.debug = True
        mock_config.log_dir = "/tmp/test_logs"
        mock_config.persistence = MagicMock()
        mock_config.connection_priority = MagicMock()
        
        mock_loader.get_config.return_value = mock_config
        container.config_loader.override(mock_loader)
        
        # Mock日志初始化
        with patch('commonlib.core.logging.tsif_logging.app_logger.initialize'):
            container.init_resources()
        
        return container


@pytest.fixture
def mock_infra_container(mock_config_container):
    """Mock的基础设施容器"""
    container = InfraContainer(config=mock_config_container)
    
    # Mock连接管理器
    mock_connection_manager = MagicMock()
    mock_redis_client = AsyncMock()
    mock_postgres_client = AsyncMock()
    mock_mysql_client = AsyncMock()
    
    mock_connection_manager.get_connector.side_effect = lambda name: {
        'redis': mock_redis_client,
        'postgres': mock_postgres_client,
        'mysql': mock_mysql_client
    }.get(name)
    
    container.connection_manager.override(mock_connection_manager)
    
    return container


@pytest.fixture
def mock_service_container(mock_config_container, mock_infra_container):
    """Mock的服务容器"""
    container = ServiceContainer(
        config=mock_config_container,
        infra=mock_infra_container
    )
    
    # Mock数据库会话
    mock_session = AsyncMock()
    container.session.override(mock_session)
    
    # Mock Redis仓库
    mock_redis_repo = AsyncMock()
    container.redis_repo.override(mock_redis_repo)
    
    return container


@pytest.fixture
def isolated_config_container():
    """隔离的配置容器（不依赖外部资源）"""
    container = ConfigContainer()
    
    # 完全Mock所有外部依赖
    with patch('commonlib.core.containers.config_container.load_dotenv'), \
         patch('commonlib.core.logging.tsif_logging.app_logger.initialize'):
        
        # Mock配置加载器
        mock_loader = MagicMock()
        mock_config = MagicMock()
        mock_config.application.project_name = "isolated_test"
        mock_config.debug = True
        mock_config.log_dir = "/tmp/isolated_logs"
        
        mock_loader.get_config.return_value = mock_config
        container.config_loader.override(mock_loader)
        
        yield container


@pytest.fixture
def container_test_environment():
    """容器测试环境配置"""
    return {
        "test_mode": True,
        "mock_external_services": True,
        "use_in_memory_db": True,
        "disable_logging": True,
        "fast_startup": True
    }


@pytest.fixture
def mock_security_components():
    """Mock安全组件"""
    return {
        "jwt_manager": MagicMock(),
        "session_manager": AsyncMock(),
        "cache_manager": AsyncMock(),
        "security_utils": MagicMock()
    }


@pytest.fixture
def mock_external_services():
    """Mock外部服务"""
    return {
        "email_service": AsyncMock(),
        "sms_service": AsyncMock(),
        "verification_service": AsyncMock()
    }
