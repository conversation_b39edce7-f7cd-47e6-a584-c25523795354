import uuid
from typing import Callable

from commonlib.core.context import (RecyclableContextVar,
                                    recyclable_app_correlation_id)
from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class RequestIDMiddleware(BaseHTTPMiddleware):
    """从请求头、参数、体中提取 request_id 并注入上下文"""

    HEADER_KEY = "X-Request-ID"
    PARAM_KEY = "request_id"

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        request_id = await self._extract_request_id(request)
        # 注入上下文
        RecyclableContextVar.increment_thread_recycles()
        recyclable_app_correlation_id.set(request_id)

        # 响应头写入
        response = await call_next(request)
        response.headers[self.HEADER_KEY] = request_id
        return response

    async def _extract_request_id(self, request: Request) -> str:
        # 1. 请求头
        request_id = request.headers.get(self.HEADER_KEY)
        if request_id:
            return request_id

        # 2. Query 参数
        if self.PARAM_KEY in request.query_params:
            return request.query_params[self.PARAM_KEY]

        # 3. 表单参数
        content_type = request.headers.get("content-type", "").lower()
        try:
            if "application/x-www-form-urlencoded" in content_type:
                form = await request.form()
                if self.PARAM_KEY in form:
                    return form[self.PARAM_KEY]
            # 4. JSON 参数
            elif "application/json" in content_type:
                body = await request.json()
                if isinstance(body, dict) and self.PARAM_KEY in body:
                    return str(body[self.PARAM_KEY])
        except Exception:
            pass  # 忽略解析失败

        # 5. 自动生成
        return uuid.uuid4().hex


def setup_request_id_middleware(app: FastAPI, *args, **kwargs) -> None:
    """修正后的配置方法"""

    app.add_middleware(
        RequestIDMiddleware,
    )
