# API接口封装修复总结

## 🔧 问题修复

### 原始问题
前端API请求格式与后端期望格式不匹配：

**前端发送格式（错误）：**
```json
{
  "data": { /* 业务数据 */ },
  "trace_id": "string",
  "timestamp": number
}
```

**后端期望格式（正确）：**
```json
{
  "meta": {
    "request_id": "string",
    "timestamp": "string",
    "version": "v1"
  },
  "data": { /* 业务数据 */ }
}
```

### 修复方案

#### 1. 更新API类型定义
- 添加 `RequestMeta` 接口
- 修改 `BaseRequest` 结构
- 统一请求格式

#### 2. 修正认证API
- 所有API调用使用新的请求格式
- 添加 `createRequestMeta()` 函数
- 修正时间戳格式（ISO字符串）

#### 3. 租户信息获取
**问题：** 注册时需要 `tenant_id`，但用户只输入 `tenant_code`

**解决方案：** 使用现有的 `/v1/tenants/list` 接口
```typescript
// 通过租户编码获取租户信息
const getTenantByCode = async (tenantCode: string) => {
  const request = {
    meta: createRequestMeta(),
    data: { 
      limit: 1,
      search: tenantCode,
      status: 'active'
    }
  }
  
  const response = await apiClient.post('/v1/tenants/list', request)
  const tenant = response.data.tenants.find(t => t.tenant_code === tenantCode)
  return tenant
}
```

## 🔄 修正后的注册流程

### 1. 用户注册API调用序列

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant Backend as 后端

    User->>Frontend: 输入租户编码和邮箱
    User->>Frontend: 点击"获取验证码"
    
    Frontend->>Backend: POST /v1/tenants/list<br/>搜索租户编码
    Backend-->>Frontend: 返回租户信息
    
    Frontend->>Backend: POST /v1/users/send_email_code<br/>发送验证码
    Backend-->>Frontend: 返回验证码ID
    
    User->>Frontend: 输入验证码和其他信息
    User->>Frontend: 提交注册
    
    Frontend->>Backend: POST /v1/users/register<br/>用户注册
    Backend-->>Frontend: 返回注册结果
```

### 2. 具体API调用示例

#### 步骤1：获取租户信息
```http
POST /v1/tenants/list
Content-Type: application/json

{
  "meta": {
    "request_id": "req_1753437318624_ikrne6vrb",
    "timestamp": "2024-07-25T18:00:00.000Z",
    "version": "v1"
  },
  "data": {
    "limit": 1,
    "search": "test-tenant",
    "status": "active"
  }
}
```

**响应：**
```json
{
  "status": "success",
  "code": 200,
  "message": "查询成功",
  "data": {
    "tenants": [
      {
        "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
        "tenant_name": "测试租户",
        "tenant_code": "test-tenant",
        "status": "active",
        "max_users": 1000,
        "current_users": 10,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "has_more": false
  }
}
```

#### 步骤2：发送邮箱验证码
```http
POST /v1/users/send_email_code
Content-Type: application/json

{
  "meta": {
    "request_id": "req_1753437318625_jlsof7wrc",
    "timestamp": "2024-07-25T18:00:01.000Z",
    "version": "v1"
  },
  "data": {
    "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
    "email": "<EMAIL>",
    "scene": "register"
  }
}
```

#### 步骤3：用户注册
```http
POST /v1/users/register
Content-Type: application/json

{
  "meta": {
    "request_id": "req_1753437318626_klmno8xyd",
    "timestamp": "2024-07-25T18:00:02.000Z",
    "version": "v1"
  },
  "data": {
    "tenant_code": "test-tenant",
    "username": "testuser123",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "nickname": "测试用户",
    "password": "Password123!",
    "verification_code": "123456",
    "code_id": "code_1753437318625_abcdef",
    "agree_terms": true
  }
}
```

## 📁 修改的文件

### 1. API类型定义 (`src/api/types.ts`)
- 添加 `RequestMeta` 接口
- 修改 `BaseRequest` 结构
- 更新租户相关类型

### 2. 认证API (`src/api/auth.ts`)
- 修正所有API调用格式
- 添加 `createRequestMeta()` 函数
- 实现 `getTenantByCode()` 方法

### 3. API客户端 (`src/api/client.ts`)
- 修正令牌刷新请求格式

### 4. 注册表单 (`src/modules/auth/components/RegisterForm.tsx`)
- 更新发送验证码逻辑
- 改进错误处理

### 5. 测试文件 (`src/modules/auth/components/__tests__/RegisterForm.test.tsx`)
- 更新测试用例以适配新的API调用

## 🚀 验证方法

### 1. 开发环境测试
1. 访问 `http://localhost:3001/register`
2. 填写租户编码: `test-tenant`
3. 填写邮箱: `<EMAIL>`
4. 点击"获取验证码"
5. 查看网络请求，确认格式正确

### 2. 网络请求检查
打开浏览器开发者工具，查看Network标签：
- 请求格式应为 `{meta: {...}, data: {...}}`
- `meta.timestamp` 应为ISO字符串格式
- `meta.version` 应为 "v1"
- `meta.request_id` 应以 "req_" 开头

## 🔍 关键修复点

1. **请求格式统一** - 所有API使用 `{meta, data}` 格式
2. **时间戳格式** - 使用ISO字符串而非数字时间戳
3. **请求ID** - 使用 `request_id` 而非 `trace_id`
4. **版本信息** - 添加API版本号 "v1"
5. **租户查询** - 使用现有的list接口而非不存在的get_by_code接口

## ✅ 修复结果

- ✅ API请求格式完全符合后端期望
- ✅ 注册流程可以正常工作
- ✅ 验证码发送功能正常
- ✅ 错误处理完善
- ✅ 测试用例更新完成

现在前端可以正确与IAM服务后端进行通信！
