/**
 * @file 顶部导航栏组件
 * @description 应用程序的顶部导航栏，包含菜单按钮、标题和用户操作
 * @status 开发中
 */

import React, { useState } from 'react'
import { useAuth, useUser } from '@/stores/authStore'
import { Button } from '@/components/ui/Button'

interface HeaderProps {
  onMenuClick?: () => void
  showMenuButton?: boolean
}

export function Header({ onMenuClick, showMenuButton = true }: HeaderProps) {
  const { logout, isAuthenticated } = useAuth()
  const { user, tenant } = useUser()
  const [userMenuOpen, setUserMenuOpen] = useState(false)

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* 左侧：菜单按钮和标题 */}
          <div className="flex items-center">
            {/* 移动端菜单按钮 */}
            {showMenuButton && onMenuClick && (
              <button
                type="button"
                className="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                onClick={onMenuClick}
              >
                <span className="sr-only">打开侧边栏</span>
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            )}

            {/* 标题 */}
            <h1 className="ml-4 text-xl font-semibold text-gray-900 lg:ml-0">
              IAM 管理系统
            </h1>

            {/* 租户信息 */}
            {tenant && (
              <div className="ml-4 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                {tenant.tenant_name || tenant.tenant_code}
              </div>
            )}
          </div>

          {/* 右侧：用户操作 */}
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                {/* 通知按钮 */}
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
                >
                  <span className="sr-only">查看通知</span>
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 17h5l-5 5-5-5h5zm0 0V7a7.5 7.5 0 00-15 0v10h15z"
                    />
                  </svg>
                </button>

                {/* 用户菜单 */}
                <div className="relative">
                  <button
                    type="button"
                    className="flex items-center space-x-2 p-2 text-sm rounded-full text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={() => setUserMenuOpen(!userMenuOpen)}
                  >
                    <div className="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-white">
                        {user?.display_name?.charAt(0) || user?.username?.charAt(0) || 'U'}
                      </span>
                    </div>
                    <span className="hidden md:block">
                      {user?.display_name || user?.username || '用户'}
                    </span>
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>

                  {/* 用户菜单下拉 */}
                  {userMenuOpen && (
                    <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                      <div className="py-1">
                        <div className="px-4 py-2 border-b border-gray-100">
                          <p className="text-sm font-medium text-gray-900">
                            {user?.display_name || user?.username}
                          </p>
                          <p className="text-sm text-gray-500">{user?.email}</p>
                        </div>
                        <button
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => {
                            setUserMenuOpen(false)
                            // TODO: 实现个人资料功能
                          }}
                        >
                          个人资料
                        </button>
                        <button
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => {
                            setUserMenuOpen(false)
                            // TODO: 实现设置功能
                          }}
                        >
                          设置
                        </button>
                        <button
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => {
                            setUserMenuOpen(false)
                            handleLogout()
                          }}
                        >
                          退出登录
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <Button
                variant="primary"
                size="sm"
                onClick={() => window.location.href = '/login'}
              >
                登录
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 点击外部关闭用户菜单 */}
      {userMenuOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setUserMenuOpen(false)}
        />
      )}
    </header>
  )
}