import asyncio
import json
import os
from typing import List, Union

from commonlib.core.logging.tsif_logging import app_logger
from commonlib.exceptions.error_codes import ErrorLevel
from commonlib.exceptions.exceptions import APIError
from commonlib.schemas.responses import (ErrorDetail, error_response,
                                         fail_response, get_request_id)
from fastapi import Request, status
from fastapi.exceptions import HTTPException, RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError


def _is_development_environment() -> bool:
    """检查是否为开发环境"""
    return os.getenv("ENVIRONMENT", "development").lower() in (
        "development",
        "dev",
        "local",
    )


def _extract_validation_errors(
    exc: Union[RequestValidationError, ValidationError]
) -> List[ErrorDetail]:
    """从验证错误中提取错误详情"""
    errors = []
    for e in exc.errors():
        error_type = e.get("type", "validation_error")
        location = " -> ".join(map(str, e.get("loc", [])))
        message = e.get("msg", "Validation failed")

        # 构建更友好的错误消息
        if location:
            full_message = f"{message} (field: {location})"
        else:
            full_message = message

        errors.append(ErrorDetail(type=error_type, message=full_message))

    return errors


def _should_include_debug_info(error_level: ErrorLevel) -> bool:
    """判断是否应该包含调试信息"""
    # 在开发环境或严重错误时包含调试信息
    return _is_development_environment() or error_level in (
        ErrorLevel.ERROR,
        ErrorLevel.CRITICAL,
    )


async def api_error_handler(request: Request, exc: APIError) -> JSONResponse:
    """
    处理自定义API异常
    :param request: FastAPI请求对象
    :param exc: API异常对象
    :return: 标准化的JSON响应
    """
    error_code = exc.error_code
    error_level = error_code.meta.level
    status_code = exc.status_code

    # 从异常detail中提取消息
    if isinstance(exc.detail, dict) and "error" in exc.detail:
        error_message = exc.detail["error"].get("message", "Unknown error")
        error_details = exc.detail["error"].get("details", {})
    else:
        error_message = str(exc.detail)
        error_details = getattr(exc, "context", {})

    # 记录日志
    log_message = f"API Error [{error_code.type}]: {error_message}"
    log_context = {
        "error_code": error_code.code,
        "error_type": error_code.type,
        "error_level": error_level.value,
        "request_method": request.method,
        "request_path": request.url.path,
        "status_code": status_code,
    }

    # 根据错误级别选择日志级别
    if error_level == ErrorLevel.CRITICAL:
        app_logger.error(
            log_message, **log_context, exception=True
        )  # 使用error代替critical
    elif error_level == ErrorLevel.ERROR:
        app_logger.error(log_message, **log_context, exception=True)
    elif error_level == ErrorLevel.WARNING:
        app_logger.warning(log_message, **log_context)
    else:
        app_logger.info(log_message, **log_context)

    # 构建错误详情列表
    errors = []
    if _should_include_debug_info(error_level):
        # 主要错误信息
        errors.append(ErrorDetail(type=error_code.type, message=error_message))

        # 添加上下文信息（仅在开发环境或严重错误时）
        if error_details:
            for key, value in error_details.items():
                if key not in ["message", "type"]:  # 避免重复信息
                    errors.append(
                        ErrorDetail(type=f"context_{key}", message=f"{key}: {value}")
                    )

    # 根据状态码选择响应类型
    if 400 <= status_code < 500:
        # 客户端错误 - 使用fail_response
        response_data = fail_response(
            message=error_message,
            errors=errors if errors else None,
            code=status_code,
        )
    else:
        # 服务器错误 - 使用error_response
        response_data = error_response(
            message=error_message,
            errors=errors if errors else None,
            code=status_code,
        )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
        headers=getattr(exc, "headers", None),
    )


async def validation_handler(
    request: Request, exc: Union[RequestValidationError, ValidationError]
) -> JSONResponse:
    """
    处理请求验证错误（FastAPI/Pydantic验证失败）
    :param request: FastAPI请求对象
    :param exc: 验证错误异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY

    # 记录验证错误
    app_logger.warning(
        f"Request validation failed for {request.method} {request.url.path}",
        error_type="validation_error",
        request_method=request.method,
        request_path=request.url.path,
        error_count=len(exc.errors()) if hasattr(exc, "errors") else 1,
    )

    # 提取验证错误详情
    validation_errors = _extract_validation_errors(exc)

    # 构建友好的错误消息
    if len(validation_errors) == 1:
        message = "Request validation failed"
    else:
        message = f"Request validation failed ({len(validation_errors)} errors)"

    # 构建错误响应 - 验证错误属于客户端错误，使用fail_response
    response_data = fail_response(
        message=message,
        errors=validation_errors if _is_development_environment() else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def service_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    全局未捕获异常处理器
    :param request: FastAPI请求对象
    :param exc: 未捕获的异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    exception_name = exc.__class__.__name__

    # 记录未捕获异常
    app_logger.error(
        f"Unhandled exception [{exception_name}] for {request.method} {request.url.path}: {str(exc)}",
        error_type="internal_error",
        exception_class=exception_name,
        request_method=request.method,
        request_path=request.url.path,
        exception=True,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        # 开发环境提供详细错误信息
        errors.append(
            ErrorDetail(type="exception_type", message=f"Exception: {exception_name}")
        )
        errors.append(ErrorDetail(type="exception_message", message=str(exc)))

        # 如果有异常链，也包含进去
        if exc.__cause__:
            errors.append(
                ErrorDetail(
                    type="exception_cause",
                    message=f"Caused by: {exc.__cause__.__class__.__name__}: {str(exc.__cause__)}",
                )
            )

    # 构建错误响应 - 未捕获异常属于服务器错误，使用error_response
    response_data = error_response(
        message="Internal server error",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def http_exception_handler(request: Request, exc) -> JSONResponse:
    """
    处理FastAPI的HTTPException
    :param request: FastAPI请求对象
    :param exc: HTTP异常
    :return: 标准化的JSON响应
    """
    status_code = exc.status_code

    # 记录HTTP异常
    app_logger.warning(
        f"HTTP Exception [{status_code}] for {request.method} {request.url.path}: {exc.detail}",
        error_type="http_error",
        status_code=status_code,
        request_method=request.method,
        request_path=request.url.path,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(ErrorDetail(type="http_exception", message=str(exc.detail)))

    # 根据状态码选择响应类型
    if 400 <= status_code < 500:
        response_data = fail_response(
            message=str(exc.detail),
            errors=errors if errors else None,
            code=status_code,
        )
    else:
        response_data = error_response(
            message=str(exc.detail),
            errors=errors if errors else None,
            code=status_code,
        )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
        headers=getattr(exc, "headers", None),
    )


async def value_error_handler(request: Request, exc: ValueError) -> JSONResponse:
    """
    处理ValueError异常
    :param request: FastAPI请求对象
    :param exc: ValueError异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_400_BAD_REQUEST

    # 记录ValueError
    app_logger.warning(
        f"ValueError for {request.method} {request.url.path}: {str(exc)}",
        error_type="value_error",
        request_method=request.method,
        request_path=request.url.path,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(ErrorDetail(type="value_error", message=str(exc)))

    # ValueError通常是客户端错误，使用fail_response
    response_data = fail_response(
        message="Invalid parameter value",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def key_error_handler(request: Request, exc: KeyError) -> JSONResponse:
    """
    处理KeyError异常（通常是缺少必需的键）
    :param request: FastAPI请求对象
    :param exc: KeyError异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_400_BAD_REQUEST

    # 记录KeyError
    app_logger.warning(
        f"KeyError for {request.method} {request.url.path}: {str(exc)}",
        error_type="key_error",
        request_method=request.method,
        request_path=request.url.path,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(
            ErrorDetail(type="key_error", message=f"Missing required key: {str(exc)}")
        )

    # KeyError通常是客户端错误，使用fail_response
    response_data = fail_response(
        message="Missing required parameter",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def type_error_handler(request: Request, exc: TypeError) -> JSONResponse:
    """
    处理TypeError异常（通常是类型不匹配）
    :param request: FastAPI请求对象
    :param exc: TypeError异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_400_BAD_REQUEST

    # 记录TypeError
    app_logger.warning(
        f"TypeError for {request.method} {request.url.path}: {str(exc)}",
        error_type="type_error",
        request_method=request.method,
        request_path=request.url.path,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(ErrorDetail(type="type_error", message=str(exc)))

    # TypeError通常是客户端错误，使用fail_response
    response_data = fail_response(
        message="Invalid parameter type",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def attribute_error_handler(
    request: Request, exc: AttributeError
) -> JSONResponse:
    """
    处理AttributeError异常（通常是对象缺少属性）
    :param request: FastAPI请求对象
    :param exc: AttributeError异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

    # 记录AttributeError
    app_logger.error(
        f"AttributeError for {request.method} {request.url.path}: {str(exc)}",
        error_type="attribute_error",
        request_method=request.method,
        request_path=request.url.path,
        exception=True,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(ErrorDetail(type="attribute_error", message=str(exc)))

    # AttributeError通常是服务器错误，使用error_response
    response_data = error_response(
        message="Internal server error",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def json_decode_error_handler(
    request: Request, exc: json.JSONDecodeError
) -> JSONResponse:
    """
    处理JSON解码错误
    :param request: FastAPI请求对象
    :param exc: JSONDecodeError异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_400_BAD_REQUEST

    # 记录JSON解码错误
    app_logger.warning(
        f"JSON decode error for {request.method} {request.url.path}: {str(exc)}",
        error_type="json_decode_error",
        request_method=request.method,
        request_path=request.url.path,
        line_number=exc.lineno,
        column_number=exc.colno,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(
            ErrorDetail(
                type="json_decode_error",
                message=f"Invalid JSON at line {exc.lineno}, column {exc.colno}: {exc.msg}",
            )
        )

    # JSON解码错误是客户端错误，使用fail_response
    response_data = fail_response(
        message="Invalid JSON format",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def timeout_error_handler(
    request: Request, exc: asyncio.TimeoutError
) -> JSONResponse:
    """
    处理超时错误
    :param request: FastAPI请求对象
    :param exc: TimeoutError异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_504_GATEWAY_TIMEOUT

    # 记录超时错误
    app_logger.error(
        f"Timeout error for {request.method} {request.url.path}: {str(exc)}",
        error_type="timeout_error",
        request_method=request.method,
        request_path=request.url.path,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(
            ErrorDetail(
                type="timeout_error",
                message=str(exc) if str(exc) else "Operation timed out",
            )
        )

    # 超时错误是服务器错误，使用error_response
    response_data = error_response(
        message="Request timeout",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def connection_error_handler(
    request: Request, exc: ConnectionError
) -> JSONResponse:
    """
    处理连接错误
    :param request: FastAPI请求对象
    :param exc: ConnectionError异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_503_SERVICE_UNAVAILABLE

    # 记录连接错误
    app_logger.error(
        f"Connection error for {request.method} {request.url.path}: {str(exc)}",
        error_type="connection_error",
        request_method=request.method,
        request_path=request.url.path,
        exception=True,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(ErrorDetail(type="connection_error", message=str(exc)))

    # 连接错误是服务器错误，使用error_response
    response_data = error_response(
        message="Service temporarily unavailable",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def permission_error_handler(
    request: Request, exc: PermissionError
) -> JSONResponse:
    """
    处理权限错误（文件系统权限等）
    :param request: FastAPI请求对象
    :param exc: PermissionError异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

    # 记录权限错误
    app_logger.error(
        f"Permission error for {request.method} {request.url.path}: {str(exc)}",
        error_type="permission_error",
        request_method=request.method,
        request_path=request.url.path,
        exception=True,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(ErrorDetail(type="permission_error", message=str(exc)))

    # 权限错误是服务器错误，使用error_response
    response_data = error_response(
        message="Internal server error",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


async def file_not_found_error_handler(
    request: Request, exc: FileNotFoundError
) -> JSONResponse:
    """
    处理文件不存在错误
    :param request: FastAPI请求对象
    :param exc: FileNotFoundError异常
    :return: 标准化的JSON响应
    """
    status_code = status.HTTP_404_NOT_FOUND

    # 记录文件不存在错误
    app_logger.warning(
        f"File not found error for {request.method} {request.url.path}: {str(exc)}",
        error_type="file_not_found_error",
        request_method=request.method,
        request_path=request.url.path,
    )

    # 构建错误详情
    errors = []
    if _is_development_environment():
        errors.append(ErrorDetail(type="file_not_found_error", message=str(exc)))

    # 文件不存在错误是客户端错误，使用fail_response
    response_data = fail_response(
        message="Resource not found",
        errors=errors if errors else None,
        code=status_code,
    )

    # 确保request_id存在
    if not response_data.request_id:
        response_data.request_id = get_request_id()

    return JSONResponse(
        status_code=status_code,
        content=response_data.model_dump(exclude_none=True),
    )


# ===== 处理器注册字典 =====
app_exception_handlers = {
    # 自定义API异常（优先级最高）
    APIError: api_error_handler,
    # FastAPI内置异常
    RequestValidationError: validation_handler,
    HTTPException: http_exception_handler,
    # Pydantic验证异常
    ValidationError: validation_handler,
    # JSON相关异常
    json.JSONDecodeError: json_decode_error_handler,
    # 网络和连接异常
    ConnectionError: connection_error_handler,
    asyncio.TimeoutError: timeout_error_handler,
    # 文件系统异常
    FileNotFoundError: file_not_found_error_handler,
    PermissionError: permission_error_handler,
    # 标准Python异常
    KeyError: key_error_handler,
    TypeError: type_error_handler,
    ValueError: value_error_handler,
    AttributeError: attribute_error_handler,
    # 全局异常处理器（必须放在最后）
    Exception: service_exception_handler,
}


def register_exception_handlers(app, custom_handlers: dict = None):
    """
    注册所有异常处理器到FastAPI应用
    :param app: FastAPI应用实例
    :param custom_handlers: 自定义异常处理器字典（可选）
    """
    # 合并默认处理器和自定义处理器
    handlers = app_exception_handlers.copy()
    if custom_handlers:
        handlers.update(custom_handlers)

    # 注册异常处理器
    registered_count = 0
    for exception_type, handler in handlers.items():
        try:
            app.add_exception_handler(exception_type, handler)
            registered_count += 1
        except Exception as e:
            app_logger.warning(
                f"Failed to register handler for {exception_type.__name__}: {str(e)}",
                exception_type=exception_type.__name__,
                handler=handler.__name__,
            )

    app_logger.info(
        f"Successfully registered {registered_count}/{len(handlers)} exception handlers",
        total_handlers=len(handlers),
        registered_handlers=registered_count,
        handler_types=[exc_type.__name__ for exc_type in handlers.keys()],
    )


def get_exception_handler_info():
    """
    获取异常处理器信息
    :return: 异常处理器信息字典
    """
    return {
        "total_handlers": len(app_exception_handlers),
        "handlers": {
            exc_type.__name__: {
                "handler_name": handler.__name__,
                "module": handler.__module__,
                "is_async": asyncio.iscoroutinefunction(handler),
            }
            for exc_type, handler in app_exception_handlers.items()
        },
    }


def add_custom_exception_handler(app, exception_type, handler):
    """
    添加自定义异常处理器
    :param app: FastAPI应用实例
    :param exception_type: 异常类型
    :param handler: 异常处理器函数
    """
    try:
        app.add_exception_handler(exception_type, handler)
        app_logger.info(
            f"Added custom exception handler for {exception_type.__name__}",
            exception_type=exception_type.__name__,
            handler=handler.__name__,
        )
    except Exception as e:
        app_logger.error(
            f"Failed to add custom exception handler for {exception_type.__name__}: {str(e)}",
            exception_type=exception_type.__name__,
            handler=handler.__name__,
            exception=True,
        )
        raise


def create_error_detail(error_type: str, message: str) -> ErrorDetail:
    """
    创建错误详情对象的便捷函数
    :param error_type: 错误类型
    :param message: 错误消息
    :return: ErrorDetail对象
    """
    return ErrorDetail(type=error_type, message=message)


def format_validation_error_message(field_path: List[str], error_msg: str) -> str:
    """
    格式化验证错误消息
    :param field_path: 字段路径
    :param error_msg: 错误消息
    :return: 格式化后的消息
    """
    if field_path:
        field_name = " -> ".join(map(str, field_path))
        return f"{error_msg} (field: {field_name})"
    return error_msg


def get_error_context_from_exception(exc: Exception) -> dict:
    """
    从异常中提取上下文信息
    :param exc: 异常对象
    :return: 上下文信息字典
    """
    context = {
        "exception_type": exc.__class__.__name__,
        "exception_module": exc.__class__.__module__,
    }

    # 添加异常特有的属性
    if hasattr(exc, "errno"):
        context["errno"] = exc.errno
    if hasattr(exc, "filename"):
        context["filename"] = exc.filename
    if hasattr(exc, "lineno"):
        context["lineno"] = exc.lineno
    if hasattr(exc, "colno"):
        context["colno"] = exc.colno
    if hasattr(exc, "msg"):
        context["msg"] = exc.msg

    return context


def should_log_stack_trace(exc: Exception) -> bool:
    """
    判断是否应该记录堆栈跟踪
    :param exc: 异常对象
    :return: 是否记录堆栈跟踪
    """
    # 对于某些异常类型，不需要记录完整的堆栈跟踪
    non_trace_exceptions = (
        ValueError,
        KeyError,
        TypeError,
        json.JSONDecodeError,
        RequestValidationError,
        ValidationError,
    )

    return not isinstance(exc, non_trace_exceptions) or _is_development_environment()


def get_exception_summary() -> dict:
    """
    获取异常处理器系统摘要
    :return: 系统摘要字典
    """
    return {
        "version": "1.0.0",
        "total_handlers": len(app_exception_handlers),
        "environment": "development" if _is_development_environment() else "production",
        "features": [
            "Custom API exceptions",
            "FastAPI built-in exceptions",
            "Standard Python exceptions",
            "JSON decode errors",
            "Network and connection errors",
            "File system errors",
            "Development/Production mode switching",
            "Detailed error logging",
            "Unified response format",
        ],
        "handler_categories": {
            "api_errors": 1,
            "validation_errors": 2,
            "network_errors": 2,
            "file_errors": 2,
            "python_standard": 4,
            "global_handler": 1,
        },
    }
