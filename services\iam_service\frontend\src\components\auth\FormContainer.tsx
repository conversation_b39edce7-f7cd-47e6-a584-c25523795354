/**
 * @file 表单容器组件
 * @description 认证页面右侧的表单容器，提供统一的表单布局和样式
 */

import React from 'react'
import { motion } from 'framer-motion'
import clsx from 'clsx'
import { TenantInfo } from '@/types'
import { Card } from '@/components/ui'

export interface FormContainerProps {
  title?: string
  subtitle?: string
  tenant?: TenantInfo
  children: React.ReactNode
  footer?: React.ReactNode
  className?: string
  showTenantInfo?: boolean
}

export const FormContainer: React.FC<FormContainerProps> = ({
  title,
  subtitle,
  tenant,
  children,
  footer,
  className,
  showTenantInfo = true
}) => {
  return (
    <div className={clsx(
      'flex flex-col items-center justify-center',
      'min-h-screen p-6 bg-gray-50',
      className
    )}>
      {/* 租户信息栏 */}
      {showTenantInfo && tenant && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="w-full max-w-md mb-6"
        >
          <div className="flex items-center justify-center p-3 bg-white rounded-lg shadow-sm border">
            {tenant.logo && (
              <img
                src={tenant.logo}
                alt={tenant.display_name}
                className="w-6 h-6 mr-2 rounded"
              />
            )}
            <span className="text-sm font-medium text-gray-700">
              {tenant.display_name} 工作区
            </span>
          </div>
        </motion.div>
      )}

      {/* 主表单卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="w-full max-w-md"
      >
        <Card className="p-8 shadow-lg border-0">
          {/* 表单头部 */}
          {(title || subtitle) && (
            <div className="text-center mb-8">
              {title && (
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {title}
                </h1>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600">
                  {subtitle}
                </p>
              )}
            </div>
          )}

          {/* 表单内容 */}
          <div className="space-y-6">
            {children}
          </div>

          {/* 表单底部 */}
          {footer && (
            <div className="mt-8 pt-6 border-t border-gray-200">
              {footer}
            </div>
          )}
        </Card>
      </motion.div>

      {/* 底部链接 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-8 text-center"
      >
        <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
          <a href="/privacy" className="hover:text-gray-700 transition-colors">
            隐私政策
          </a>
          <span>·</span>
          <a href="/terms" className="hover:text-gray-700 transition-colors">
            服务条款
          </a>
          <span>·</span>
          <a href="/help" className="hover:text-gray-700 transition-colors">
            帮助中心
          </a>
        </div>
      </motion.div>
    </div>
  )
}
