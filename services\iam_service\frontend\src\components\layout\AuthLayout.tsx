/**
 * @file 认证页面布局组件
 * @description 认证页面的主布局，支持左右分屏和居中卡片两种模式
 */

import React from 'react'
import { motion } from 'framer-motion'
import clsx from 'clsx'
import { AuthLayoutMode, TenantInfo } from '@/types'
import { BrandSection, FormContainer } from '@/components/auth'
import { useResponsive } from '@/hooks'

export interface AuthLayoutProps {
  mode?: AuthLayoutMode
  tenant?: TenantInfo
  title?: string
  subtitle?: string
  children: React.ReactNode
  footer?: React.ReactNode
  showBrandSection?: boolean
  showTenantInfo?: boolean
  className?: string
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({
  mode = 'split',
  tenant,
  title,
  subtitle,
  children,
  footer,
  showBrandSection = true,
  showTenantInfo = true,
  className
}) => {
  const { isMobile, isTablet } = useResponsive()

  // 在移动端和平板端强制使用居中模式
  const effectiveMode = isMobile || isTablet ? 'centered' : mode

  // 分屏布局
  if (effectiveMode === 'split') {
    return (
      <div className={clsx('min-h-screen flex', className)}>
        {/* 左侧品牌展示区 */}
        {showBrandSection && (
          <motion.div
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
            className="hidden lg:flex lg:w-2/5 xl:w-1/2"
          >
            <BrandSection
              tenant={tenant}
              className="w-full"
            />
          </motion.div>
        )}

        {/* 右侧表单区 */}
        <motion.div
          initial={{ opacity: 0, x: 100 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, ease: 'easeOut', delay: 0.1 }}
          className={clsx(
            'flex-1 flex items-center justify-center',
            'bg-gray-50 p-6',
            {
              'lg:w-3/5 xl:w-1/2': showBrandSection,
              'w-full': !showBrandSection
            }
          )}
        >
          <div className="w-full max-w-md">
            {/* 移动端租户信息 */}
            {showTenantInfo && tenant && (isMobile || isTablet) && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="mb-6"
              >
                <div className="flex items-center justify-center p-3 bg-white rounded-lg shadow-sm border">
                  {tenant.logo && (
                    <img
                      src={tenant.logo}
                      alt={tenant.display_name}
                      className="w-6 h-6 mr-2 rounded"
                    />
                  )}
                  <span className="text-sm font-medium text-gray-700">
                    {tenant.display_name} 工作区
                  </span>
                </div>
              </motion.div>
            )}

            {/* 表单卡片 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-white rounded-xl shadow-lg border-0 p-8"
            >
              {/* 表单头部 */}
              {(title || subtitle) && (
                <div className="text-center mb-8">
                  {title && (
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">
                      {title}
                    </h1>
                  )}
                  {subtitle && (
                    <p className="text-sm text-gray-600">
                      {subtitle}
                    </p>
                  )}
                </div>
              )}

              {/* 表单内容 */}
              <div className="space-y-6">
                {children}
              </div>

              {/* 表单底部 */}
              {footer && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                  {footer}
                </div>
              )}
            </motion.div>

            {/* 底部链接 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mt-8 text-center"
            >
              <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
                <a href="/privacy" className="hover:text-gray-700 transition-colors">
                  隐私政策
                </a>
                <span>·</span>
                <a href="/terms" className="hover:text-gray-700 transition-colors">
                  服务条款
                </a>
                <span>·</span>
                <a href="/help" className="hover:text-gray-700 transition-colors">
                  帮助中心
                </a>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    )
  }

  // 居中卡片布局
  return (
    <div className={clsx('min-h-screen bg-gray-50', className)}>
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 opacity-50" />
      
      <div className="relative z-10">
        <FormContainer
          title={title}
          subtitle={subtitle}
          tenant={tenant}
          footer={footer}
          showTenantInfo={showTenantInfo}
          className="min-h-screen"
        >
          {children}
        </FormContainer>
      </div>
    </div>
  )
}
