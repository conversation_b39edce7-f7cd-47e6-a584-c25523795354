[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "thingsmore"
version = "0.1.0"
description = "ThingsMore Application"
readme = "README.md"
requires-python = ">=3.12"
authors = [
  { name = "Your Name", email = "<EMAIL>" }
]
classifiers = [
  "Development Status :: 3 - Alpha",
  "Intended Audience :: Developers",
  "Programming Language :: Python :: 3.12",
  "Typing :: Typed"
]
dependencies = [
  # 留空，推荐使用 requirements/prod.txt + requirements-parser 或 migrate 依赖
]

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
include = ["common_service*","infrastructure*","model_lib*","services*"]
exclude = ["tests*", "docs*"]

[tool.setuptools.package-data]
"api.thingsmore" = ["py.typed"]