import os
from pathlib import Path
from typing import List, Optional, Set, Union

from commonlib.configs.base_setting import AppSettings


class ConfigLoader:
    """独立的配置加载器，确保配置只加载一次"""

    def __init__(self):
        self._config: AppSettings | None = None
        self._loaded = False

    def load(
        self,
        app_setting: AppSettings = None,
        config_path: Optional[Union[str, Path]] = None,
        additional_paths: Optional[List[Union[str, Path]]] = None,
    ) -> AppSettings:
        self._config = app_setting
        """加载配置（仅第一次调用时实际执行）"""
        if not self._loaded:
            search_paths = self._get_search_paths(config_path, additional_paths)
            self._load_config_with_search(search_paths)
            self._loaded = True
        return self._config

    def get_config(self) -> AppSettings:
        return self._config

    @classmethod
    def _get_search_paths(
        cls,
        config_path: Optional[Union[str, Path]] = None,
        additional_paths: Optional[List[Union[str, Path]]] = None,
    ) -> List[Path]:
        """搜索配置文件路径"""
        search_paths: List[Path] = []

        if config_path:
            search_paths.append(
                Path(config_path) if isinstance(config_path, str) else config_path
            )
        if "CONFIG_FILE_PATH" in os.environ:
            search_paths.append(Path(os.environ["CONFIG_FILE_PATH"]))

        if additional_paths:
            search_paths.extend(
                Path(p) if isinstance(p, str) else p for p in additional_paths
            )

        search_paths.extend(
            [
                Path("config.json"),
                Path("configs/config.json"),
                Path("../configs/config.json"),
                Path("../../configs/config.json"),
                Path("../../../configs/config.json"),
            ]
        )

        # 去重并保留顺序
        seen_paths: Set[Path] = set()
        return [
            p
            for p in search_paths
            if not (p.resolve() in seen_paths or seen_paths.add(p.resolve()))
        ]

    def _load_config_with_search(self, search_paths: List[Path]) -> bool:
        """尝试从搜索路径加载配置"""
        for path in search_paths:
            if path.exists():
                print(path)
                try:
                    self._config.from_file(path)
                    return True
                except Exception as e:
                    raise ValueError(f"Config load failed: {e}")
        return False
