#!/usr/bin/env python3
"""
异常系统测试脚本
验证所有异常类型是否正常工作
"""

import sys
import traceback
from typing import <PERSON>ple

from commonlib.exceptions.error_codes import ValidationErrors
from commonlib.exceptions.exceptions import (APIError, BadRequestError,
                                             ConflictException,
                                             ContextTooLargeError,
                                             DatabaseError,
                                             DocumentProcessingError,
                                             EmbeddingError,
                                             ExternalServiceError,
                                             ForbiddenError, GenerationError,
                                             IntegrationError,
                                             KnowledgeBaseError, NotFoundError,
                                             RateLimitException,
                                             RetrievalError, ServerException,
                                             ServiceUnavailableException,
                                             UnauthorizedError,
                                             ValidationError,
                                             VectorSearchError)


def test_exception(exception_class, *args, **kwargs) -> Tuple[bool, str]:
    """测试单个异常类"""
    try:
        exception = exception_class(*args, **kwargs)
        # 验证基本属性
        assert hasattr(exception, "status_code")
        assert hasattr(exception, "detail")
        assert hasattr(exception, "error_code")
        assert hasattr(exception, "headers")

        # 验证错误代码结构
        error_dict = exception.error_code.to_dict()
        assert "code" in error_dict
        assert "type" in error_dict
        assert "http_status" in error_dict
        assert "level" in error_dict

        return True, f"✅ {exception_class.__name__} - 状态码: {exception.status_code}"
    except Exception as e:
        return False, f"❌ {exception_class.__name__} - 错误: {str(e)}"


def run_all_tests() -> None:
    """运行所有异常测试"""
    print("🧪 开始异常系统测试...\n")

    test_cases = [
        # 基础异常
        (APIError, ValidationErrors.INVALID_FORMAT, "测试消息"),
        # 通用异常
        (BadRequestError, "请求参数错误"),
        (ValidationError, "邮箱格式错误", "email", "invalid@", "email"),
        (UnauthorizedError, "令牌过期"),
        (ForbiddenError, "权限不足", "read_data"),
        (NotFoundError, "用户", "12345"),
        (ConflictException, "用户", "email", "existing_123"),
        (RateLimitException, "api", 100, "1m", 60),
        (DatabaseError, "连接超时", "SELECT * FROM users"),
        (ServerException, "内部错误", "ERR-001"),
        (ServiceUnavailableException, "数据库", 300, "维护中"),
        # RAG项目异常
        (DocumentProcessingError, "PDF解析失败", "doc.pdf", "pdf", "格式错误"),
        (EmbeddingError, "嵌入生成失败", "text-ada-002", 1000),
        (VectorSearchError, "搜索失败", "机器学习", "kb_index"),
        (KnowledgeBaseError, "知识库不存在", "kb_123", "AI知识库"),
        (RetrievalError, "检索失败", "深度学习", "semantic"),
        (GenerationError, "生成失败", "gpt-3.5", 2000),
        (ContextTooLargeError, "上下文过大", 8000, 4096),
        # 外部服务异常
        (ExternalServiceError, "服务不可用", "openai", 503),
        (IntegrationError, "API调用失败", "embedding_api", "Rate limited"),
    ]

    passed = 0
    failed = 0
    results = []

    for test_case in test_cases:
        exception_class = test_case[0]
        args = test_case[1:]

        success, message = test_exception(exception_class, *args)
        results.append(message)

        if success:
            passed += 1
        else:
            failed += 1

    # 输出结果
    print("📊 测试结果:")
    print("=" * 50)
    for result in results:
        print(result)

    print("\n" + "=" * 50)
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📈 成功率: {passed/(passed+failed)*100:.1f}%")

    if failed > 0:
        print("\n⚠️  存在失败的测试用例，请检查异常定义")
        sys.exit(1)
    else:
        print("\n🎉 所有测试通过！异常系统工作正常")


def test_error_response_format():
    """测试错误响应格式"""
    print("\n🔍 测试错误响应格式...")

    # 创建一个验证错误
    error = ValidationError(
        "邮箱格式不正确",
        field_name="email",
        field_value="invalid@",
        validation_type="email",
    )

    # 检查响应格式
    detail = error.detail
    assert isinstance(detail, dict)
    assert "error" in detail

    error_info = detail["error"]
    assert "type" in error_info
    assert "message" in error_info
    assert "details" in error_info

    # 检查详细信息
    details = error_info["details"]
    assert "field_name" in details
    assert "field_value" in details
    assert "validation_type" in details

    print("✅ 错误响应格式正确")

    # 检查HTTP头
    headers = error.headers
    assert "X-Error-Code" in headers
    assert "X-Error-Level" in headers

    print("✅ HTTP头信息正确")
    print(f"   错误代码: {headers['X-Error-Code']}")
    print(f"   错误级别: {headers['X-Error-Level']}")


def test_rag_specific_errors():
    """测试RAG项目特有错误"""
    print("\n🤖 测试RAG项目特有错误...")

    # 文档处理错误
    doc_error = DocumentProcessingError(
        "无法解析PDF文件",
        file_name="research_paper.pdf",
        file_type="pdf",
        error_details="Invalid PDF structure",
    )

    assert doc_error.status_code == 400
    assert "research_paper.pdf" in str(doc_error.detail)
    print("✅ 文档处理错误测试通过")

    # 向量搜索错误
    search_error = VectorSearchError(
        "向量数据库查询失败", query="什么是深度学习", index_name="ai_knowledge_base"
    )

    assert search_error.status_code == 500
    assert "ai_knowledge_base" in str(search_error.detail)
    print("✅ 向量搜索错误测试通过")

    # 上下文过大错误
    context_error = ContextTooLargeError(
        "输入文本超出模型限制", current_length=8192, max_length=4096
    )

    assert context_error.status_code == 400
    details = context_error.detail["error"]["details"]
    assert details["current_length"] == 8192
    assert details["max_length"] == 4096
    print("✅ 上下文过大错误测试通过")


if __name__ == "__main__":
    try:
        run_all_tests()
        test_error_response_format()
        test_rag_specific_errors()
        print("\n🎯 所有测试完成！异常系统已准备就绪")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {str(e)}")
        traceback.print_exc()
        sys.exit(1)
