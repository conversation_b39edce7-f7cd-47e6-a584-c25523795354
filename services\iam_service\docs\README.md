# IAM服务 - 身份与访问管理系统

## 项目概述

IAM（Identity and Access Management）服务是一个企业级的身份认证和权限管理系统，采用微服务架构设计，支持多租户模式。本系统为企业提供完整的用户身份管理、认证授权、权限控制和安全审计解决方案。

## 技术架构

### 核心技术栈
- **后端框架**: FastAPI (Python 3.11+)
- **数据库**: PostgreSQL (主数据库) + Redis (缓存)
- **ORM**: SQLAlchemy 2.0 (异步)
- **依赖注入**: dependency-injector
- **认证**: JWT + 会话管理
- **API文档**: OpenAPI 3.0 + Swagger UI

### 架构特点
- **微服务架构**: 独立部署，水平扩展
- **多租户支持**: 完整的租户隔离机制
- **异步处理**: 全异步设计，高并发支持
- **类型安全**: 完整的类型注解和验证
- **安全优先**: 多层安全防护机制

## 功能模块

### 1. 租户管理模块 ✅
**路由前缀**: `/api/v1/tenants`

**核心功能**:
- 租户生命周期管理（创建、查询、更新、删除）
- 租户状态管理（激活、暂停、删除）
- 租户配置管理（功能开关、资源限制）
- 租户统计和监控

**技术特性**:
- 完整的CRUD操作
- 游标分页查询
- 状态转换验证
- 审计日志记录
- 缓存优化

### 2. 用户管理模块 ✅
**路由前缀**: `/api/v1/users`

**核心功能**:
- 用户生命周期管理（创建、注册、激活、删除）
- 用户信息维护（基本信息、偏好设置、状态管理）
- 用户查询和统计（多条件查询、分页、统计分析）
- 用户安全管理（密码策略、账户锁定、安全验证）

**技术特性**:
- 密码bcrypt加密存储
- 租户内用户唯一性验证
- 密码策略验证（长度、复杂度）
- 激活令牌生成和管理
- 用户信息缓存策略

### 3. 认证和安全模块 ✅
**路由前缀**: `/api/v1/auth`

**核心功能**:
- 多种登录方式（用户名、邮箱、手机号）
- JWT令牌管理（访问令牌+刷新令牌）
- 会话管理（创建、查询、终止、并发控制）
- 密码管理（修改、重置、策略验证）
- 验证码管理（短信、邮箱验证码）
- 敏感操作确认

**安全特性**:
- JWT签名验证和令牌黑名单
- 会话超时和并发控制
- 密码强度检查和加密存储
- 登录失败次数限制和账户锁定
- 设备指纹生成和验证
- 验证码频率限制和过期管理

### 4. 角色权限管理模块 (RBAC) ✅
**路由前缀**: `/api/v1/rbac`

**核心功能**:
- 角色管理（创建、查询、更新、删除角色）
- 权限管理（创建权限、分配角色权限）
- 用户角色分配（为用户分配/移除角色）
- 权限检查（检查用户是否具有指定权限）

**技术特性**:
- 支持角色层级和继承
- 权限缓存优化
- 批量权限操作
- 角色权限继承

### 5. 审计日志系统 ✅
**路由前缀**: `/api/v1/audit`

**核心功能**:
- 审计日志记录（自动记录系统操作）
- 日志查询（多条件查询和分页）
- 统计分析（操作统计和趋势分析）
- 日志导出（支持CSV、JSON、Excel格式）

**技术特性**:
- 多维度查询过滤
- 实时统计分析
- 异步导出处理
- 风险级别分类

### 6. 系统配置模块 ✅
**路由前缀**: `/api/v1/system`

**核心功能**:
- 配置管理（设置、获取、删除配置）
- 配置继承（支持全局和租户级配置）
- 敏感配置（加密存储敏感信息）
- 批量操作（批量设置和重置配置）

**技术特性**:
- 配置层级继承
- 敏感配置加密
- 配置分类管理
- 实时配置更新

### 7. 高级安全功能 ✅
**路由前缀**: `/api/v1/security`

**核心功能**:
- 多因子认证(MFA)（TOTP、SMS、Email）
- 安全策略（密码策略、会话策略等）
- 威胁检测（异常登录、暴力破解检测）
- 安全事件（事件记录和处理）

**技术特性**:
- TOTP二维码生成
- 备用恢复码
- 安全策略配置
- 威胁检测和告警

### 8. 知识库管理模块 ✅
**路由前缀**: `/api/v1/knowledge_bases`

**核心功能**:
- 知识库生命周期管理
- 知识库访问权限控制
- 知识库内容管理
- 知识库统计分析

### 9. 文档管理模块 ✅
**路由前缀**: `/api/v1/documents`

**核心功能**:
- 文档上传和存储
- 文档访问权限控制
- 文档版本管理
- 文档搜索和分类

### 10. 外部服务集成 ✅

**邮件服务**:
- 多种邮件模板支持
- 用户激活邮件、密码重置邮件
- 邮箱验证码邮件、MFA设置确认邮件
- 异步邮件发送、SMTP配置

**短信服务**:
- 多短信服务提供商支持
- 阿里云、腾讯云SMS集成
- 短信模板管理、发送频率限制
- 短信发送历史记录

**验证码服务**:
- 统一验证码管理
- 短信验证码、邮箱验证码
- 多种使用场景支持
- 验证码过期管理和频率控制

### 11. 安全基础设施 ✅

**安全工具类**:
- 密码加密和验证（bcrypt）
- 随机密码生成、密码强度检查
- TOTP密钥生成和验证
- 设备指纹生成、数据脱敏工具

**JWT令牌管理**:
- JWT令牌生成和验证
- 访问令牌和刷新令牌
- 令牌过期检查、令牌黑名单管理

**会话管理**:
- 分布式会话管理、设备信息记录
- 会话超时控制、并发会话限制
- 会话统计分析、过期会话清理

**缓存管理**:
- 用户信息缓存、验证码缓存
- 会话信息缓存、权限信息缓存
- TTL自动过期管理

### 12. 后台任务系统 ✅

**清理任务**:
- 过期会话清理、过期验证码清理
- 过期令牌清理、临时文件清理

**通知任务**:
- 异步邮件发送、异步短信发送
- 批量通知处理

**用户任务**:
- 用户数据同步、用户统计更新
- 用户状态检查

## 系统监控

### 健康检查
**路由前缀**: `/api/v1/health`
- 系统健康状态检查
- 数据库连接检查
- Redis连接检查
- 外部服务状态检查

### 系统监控
**路由前缀**: `/api/v1/system`
- 系统性能监控
- 资源使用统计
- 错误日志收集
- 业务指标统计

## 项目特点

### 1. 企业级安全
- 零信任架构设计
- 多层安全防护机制
- 完整的审计追踪
- 威胁检测和响应

### 2. 高性能设计
- 异步处理架构
- 智能缓存策略
- 数据库查询优化
- 分页和批量处理

### 3. 可扩展架构
- 微服务架构设计
- 依赖注入容器
- 模块化组件设计
- 插件化扩展机制

### 4. 开发友好
- 完整的类型注解
- 自动生成API文档
- 统一的错误处理
- 丰富的测试用例

## 快速开始

### 环境要求
- Python 3.11+
- PostgreSQL 12+
- Redis 6+

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置环境
```bash
cp .env.example .env
# 编辑 .env 文件配置数据库和Redis连接
```

### 启动服务
```bash
python main.py
```

### 访问文档
- API文档: http://localhost:8089/docs
- 健康检查: http://localhost:8089/api/v1/health

## 文档目录

- [功能清单](./MODULE_FUNCTIONS.md) - 详细的模块功能说明
- [待办事项](./TODO_LIST.md) - 开发任务和优化计划
- [项目总结](./PROJECT_SUMMARY.md) - 项目整体总结和技术成就
- [第三阶段功能](./PHASE3_FEATURES.md) - 第三阶段新增功能详细说明
- [部署指南](./DEPLOYMENT_GUIDE.md) - 生产环境部署指南

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。
