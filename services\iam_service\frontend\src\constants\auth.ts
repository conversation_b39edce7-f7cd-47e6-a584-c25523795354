/**
 * @file 认证相关常量
 * @description 认证模块的常量定义
 */

// 认证相关的存储键名
export const AUTH_STORAGE_KEYS = {
  ACCESS_TOKEN: 'auth_access_token',
  REFRESH_TOKEN: 'auth_refresh_token',
  USER_INFO: 'auth_user_info',
  TENANT_INFO: 'auth_tenant_info',
  REMEMBER_ME: 'auth_remember_me',
  LAST_LOGIN_EMAIL: 'auth_last_login_email',
  RECENT_TENANTS: 'auth_recent_tenants',
  FAVORITE_TENANTS: 'auth_favorite_tenants'
} as const

// 认证状态常量
export const AUTH_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  AUTHENTICATED: 'authenticated',
  UNAUTHENTICATED: 'unauthenticated',
  ERROR: 'error'
} as const

// 表单状态常量
export const FORM_STATUS = {
  IDLE: 'idle',
  SUBMITTING: 'submitting',
  SUCCESS: 'success',
  ERROR: 'error'
} as const

// 登录方式常量
export const LOGIN_TYPES = {
  EMAIL: 'email',
  PHONE: 'phone',
  USERNAME: 'username'
} as const

// 认证表单模式常量
export const AUTH_FORM_MODES = {
  LOGIN: 'login',
  REGISTER: 'register',
  FORGOT_PASSWORD: 'forgot-password',
  RESET_PASSWORD: 'reset-password'
} as const

// 密码强度等级常量
export const PASSWORD_STRENGTH = {
  WEAK: 'weak',
  FAIR: 'fair',
  GOOD: 'good',
  STRONG: 'strong'
} as const

// 密码强度分数阈值
export const PASSWORD_STRENGTH_THRESHOLDS = {
  WEAK: 0,
  FAIR: 25,
  GOOD: 50,
  STRONG: 75
} as const

// 默认密码策略
export const DEFAULT_PASSWORD_POLICY = {
  min_length: 8,
  max_length: 128,
  require_uppercase: true,
  require_lowercase: true,
  require_numbers: true,
  require_symbols: false,
  forbidden_patterns: ['password', '123456', 'qwerty'],
  max_age_days: 90,
  history_count: 5
} as const

// 密码验证正则表达式
export const PASSWORD_PATTERNS = {
  UPPERCASE: /[A-Z]/,
  LOWERCASE: /[a-z]/,
  NUMBERS: /[0-9]/,
  SYMBOLS: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,
  COMMON_PATTERNS: [
    /password/i,
    /123456/,
    /qwerty/i,
    /admin/i,
    /user/i
  ]
} as const

// 表单验证错误代码
export const VALIDATION_ERROR_CODES = {
  REQUIRED: 'REQUIRED',
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_PHONE: 'INVALID_PHONE',
  PASSWORD_TOO_SHORT: 'PASSWORD_TOO_SHORT',
  PASSWORD_TOO_LONG: 'PASSWORD_TOO_LONG',
  PASSWORD_NO_UPPERCASE: 'PASSWORD_NO_UPPERCASE',
  PASSWORD_NO_LOWERCASE: 'PASSWORD_NO_LOWERCASE',
  PASSWORD_NO_NUMBERS: 'PASSWORD_NO_NUMBERS',
  PASSWORD_NO_SYMBOLS: 'PASSWORD_NO_SYMBOLS',
  PASSWORD_COMMON_PATTERN: 'PASSWORD_COMMON_PATTERN',
  PASSWORD_MISMATCH: 'PASSWORD_MISMATCH',
  TENANT_NOT_FOUND: 'TENANT_NOT_FOUND',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED'
} as const

// 认证错误消息
export const AUTH_ERROR_MESSAGES = {
  [VALIDATION_ERROR_CODES.REQUIRED]: '此字段为必填项',
  [VALIDATION_ERROR_CODES.INVALID_EMAIL]: '请输入有效的邮箱地址',
  [VALIDATION_ERROR_CODES.INVALID_PHONE]: '请输入有效的手机号码',
  [VALIDATION_ERROR_CODES.PASSWORD_TOO_SHORT]: '密码长度至少为 {min} 位',
  [VALIDATION_ERROR_CODES.PASSWORD_TOO_LONG]: '密码长度不能超过 {max} 位',
  [VALIDATION_ERROR_CODES.PASSWORD_NO_UPPERCASE]: '密码必须包含大写字母',
  [VALIDATION_ERROR_CODES.PASSWORD_NO_LOWERCASE]: '密码必须包含小写字母',
  [VALIDATION_ERROR_CODES.PASSWORD_NO_NUMBERS]: '密码必须包含数字',
  [VALIDATION_ERROR_CODES.PASSWORD_NO_SYMBOLS]: '密码必须包含特殊字符',
  [VALIDATION_ERROR_CODES.PASSWORD_COMMON_PATTERN]: '密码不能包含常见模式',
  [VALIDATION_ERROR_CODES.PASSWORD_MISMATCH]: '两次输入的密码不一致',
  [VALIDATION_ERROR_CODES.TENANT_NOT_FOUND]: '租户不存在',
  [VALIDATION_ERROR_CODES.INVALID_CREDENTIALS]: '邮箱或密码错误',
  [VALIDATION_ERROR_CODES.ACCOUNT_LOCKED]: '账户已被锁定，请联系管理员',
  [VALIDATION_ERROR_CODES.ACCOUNT_DISABLED]: '账户已被禁用',
  [VALIDATION_ERROR_CODES.EMAIL_NOT_VERIFIED]: '邮箱尚未验证，请先验证邮箱'
} as const

// 会话超时时间（毫秒）
export const SESSION_TIMEOUTS = {
  DEFAULT: 30 * 60 * 1000, // 30分钟
  REMEMBER_ME: 30 * 24 * 60 * 60 * 1000, // 30天
  ADMIN: 15 * 60 * 1000, // 15分钟
  API_TOKEN: 60 * 60 * 1000 // 1小时
} as const

// 登录尝试限制
export const LOGIN_ATTEMPT_LIMITS = {
  MAX_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15分钟
  RESET_WINDOW: 60 * 60 * 1000 // 1小时
} as const

// 租户检测优先级
export const TENANT_DETECTION_PRIORITY = [
  'url',
  'subdomain',
  'email',
  'default'
] as const

// 租户检测方法
export const TENANT_DETECTION_METHODS = {
  URL_PARAMETER: 'url_parameter',
  SUBDOMAIN: 'subdomain',
  EMAIL_DOMAIN: 'email_domain',
  MANUAL_INPUT: 'manual_input',
  AUTO_DETECTED: 'auto_detected'
} as const

// 邮箱域名到租户的映射示例
export const EMAIL_DOMAIN_TENANT_MAP = {
  'example.com': 'example-corp',
  'test.com': 'test-tenant',
  'demo.org': 'demo-tenant'
} as const

// 社交登录提供商
export const SOCIAL_LOGIN_PROVIDERS = {
  GOOGLE: 'google',
  MICROSOFT: 'microsoft',
  GITHUB: 'github',
  LINKEDIN: 'linkedin',
  FACEBOOK: 'facebook',
  TWITTER: 'twitter'
} as const

// 认证事件类型
export const AUTH_EVENT_TYPES = {
  LOGIN: 'login',
  LOGOUT: 'logout',
  REGISTER: 'register',
  PASSWORD_RESET: 'password_reset',
  SESSION_EXPIRED: 'session_expired',
  ACCOUNT_LOCKED: 'account_locked',
  EMAIL_VERIFIED: 'email_verified',
  TENANT_SWITCHED: 'tenant_switched'
} as const

// API端点路径
export const AUTH_API_ENDPOINTS = {
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REGISTER: '/auth/register',
  REFRESH: '/auth/refresh',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  VERIFY_EMAIL: '/auth/verify-email',
  RESEND_VERIFICATION: '/auth/resend-verification',
  CHECK_EMAIL: '/auth/check-email',
  CHECK_TENANT: '/auth/check-tenant',
  USER_PROFILE: '/auth/profile',
  CHANGE_PASSWORD: '/auth/change-password',
  SESSIONS: '/auth/sessions',
  REVOKE_SESSION: '/auth/sessions/{id}/revoke'
} as const

// HTTP状态码
export const HTTP_STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500
} as const

// 认证相关的CSS类名
export const AUTH_CSS_CLASSES = {
  CONTAINER: 'auth-container',
  FORM: 'auth-form',
  FIELD: 'auth-field',
  ERROR: 'auth-error',
  SUCCESS: 'auth-success',
  LOADING: 'auth-loading',
  BRAND_SECTION: 'auth-brand-section',
  FORM_SECTION: 'auth-form-section'
} as const

// 动画持续时间（毫秒）
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  FORM_SWITCH: 400,
  FADE_IN: 200,
  SLIDE_IN: 300
} as const
