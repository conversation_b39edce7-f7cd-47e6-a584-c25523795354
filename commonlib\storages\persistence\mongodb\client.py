from datetime import timedelta
from typing import Optional

from commonlib.configs.storages.persistence.mongodb_config import MongoDBConfig
from commonlib.core.logging.tsif_logging import app_logger
from commonlib.storages.base import BaseConnector, ConnectorParams
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError


class MongoConnector(BaseConnector[AsyncIOMotorClient, MongoDBConfig]):
    _config_class = MongoDBConfig

    def __init__(self, initial_params: Optional[ConnectorParams] = None):
        super().__init__(
            initial_params or ConnectorParams(heartbeat_interval=timedelta(seconds=20))
        )
        self._connection: Optional[AsyncIOMotorClient] = None
        self._ping_connection: Optional[AsyncIOMotorDatabase] = None

    @property
    def name(self) -> str:
        return "Mongodb"

    async def _connect(self) -> bool:
        if not self.config:
            raise RuntimeError(f"{self.name} Config not loaded")

        try:
            await self._close()  # 清理旧连接

            self._connection = AsyncIOMotorClient(
                str(self.config.DSN),
                minPoolSize=self.config.MONGODB_MIN_POOL_SIZE,
                maxPoolSize=self.config.MONGODB_MAX_POOL_SIZE,
                serverSelectionTimeoutMS=3000,  # 可调
            )
            self._ping_connection = self._connection.get_database(self.config.MONGO_DB)
            return True
        except Exception as e:
            app_logger.error(f"Unexpected {self.name} error: {e}", exception=True)
            return False

    async def _close(self) -> None:
        if self._connection:
            try:
                self._connection.close()
            except Exception as e:
                app_logger.error(f"[{self.name}] Close client failed: {e}")
        self._connection = None
        self._ping_connection = None

    async def _perform_heartbeat_check(self) -> bool:
        if self._ping_connection is None:
            return False
        try:
            await self._ping_connection.command("ping")
            return True
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            app_logger.error(f"{self.name} heartbeat failed: {e}", exception=True)
            return False
        except Exception as e:
            app_logger.error(
                f"{self.name} unexpected heartbeat error: {e}", exception=True
            )
            return False
