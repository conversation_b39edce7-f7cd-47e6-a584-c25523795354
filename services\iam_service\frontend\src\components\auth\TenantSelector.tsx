/**
 * @file 租户选择组件
 * @description 智能租户选择器，支持搜索、历史记录和自动检测
 */

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import clsx from 'clsx'
import { TenantInfo } from '@/types'
import { Logo, Input, Button } from '@/components/ui'
import * as authApi from '@/api/auth'

export interface TenantSelectorProps {
  selectedTenant?: TenantInfo
  onTenantSelect?: (tenant: TenantInfo) => void
  onError?: (error: string) => void
  className?: string
  placeholder?: string
  showHistory?: boolean
  autoDetect?: boolean
}

export const TenantSelector: React.FC<TenantSelectorProps> = ({
  selectedTenant,
  onTenantSelect,
  onError,
  className,
  placeholder = '请输入租户编码或域名',
  showHistory = true,
  autoDetect = true
}) => {
  const [isOpen, setIsOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')
  const [isLoading, setIsLoading] = React.useState(false)
  const [searchResults, setSearchResults] = React.useState<TenantInfo[]>([])
  const [recentTenants, setRecentTenants] = React.useState<TenantInfo[]>([])
  const [detectedTenant, setDetectedTenant] = React.useState<TenantInfo | null>(null)

  const inputRef = React.useRef<HTMLInputElement>(null)
  const dropdownRef = React.useRef<HTMLDivElement>(null)

  // 加载最近使用的租户
  React.useEffect(() => {
    if (showHistory) {
      const recent = localStorage.getItem('recent_tenants')
      if (recent) {
        try {
          setRecentTenants(JSON.parse(recent))
        } catch (error) {
          console.error('Failed to parse recent tenants:', error)
        }
      }
    }
  }, [showHistory])

  // 自动检测租户
  React.useEffect(() => {
    if (autoDetect && !selectedTenant) {
      detectTenant()
    }
  }, [autoDetect, selectedTenant])

  // 点击外部关闭下拉框
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // 自动检测租户
  const detectTenant = async () => {
    try {
      const hostname = window.location.hostname
      const subdomain = hostname.split('.')[0]
      
      if (subdomain && subdomain !== 'www' && subdomain !== 'localhost') {
        const tenant = await authApi.getTenantByDomain(hostname)
        setDetectedTenant(tenant)
        onTenantSelect?.(tenant)
      }
    } catch (error) {
      // 自动检测失败不显示错误，用户可以手动选择
      console.log('Auto-detect tenant failed:', error)
    }
  }

  // 搜索租户
  const searchTenants = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      return
    }

    setIsLoading(true)

    try {
      const results = await authApi.searchTenants(query)
      setSearchResults(results)
    } catch (error: any) {
      onError?.(error.message || '搜索租户失败')
      setSearchResults([])
    } finally {
      setIsLoading(false)
    }
  }

  // 防抖搜索
  const debouncedSearch = React.useCallback(
    React.useMemo(
      () => {
        let timeoutId: NodeJS.Timeout
        return (query: string) => {
          clearTimeout(timeoutId)
          timeoutId = setTimeout(() => searchTenants(query), 300)
        }
      },
      []
    ),
    []
  )

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(value)
    setIsOpen(true)
    debouncedSearch(value)
  }

  // 选择租户
  const handleTenantSelect = (tenant: TenantInfo) => {
    onTenantSelect?.(tenant)
    setSearchValue('')
    setIsOpen(false)

    // 保存到最近使用
    if (showHistory) {
      const recent = recentTenants.filter(t => t.id !== tenant.id)
      const newRecent = [tenant, ...recent].slice(0, 5)
      setRecentTenants(newRecent)
      localStorage.setItem('recent_tenants', JSON.stringify(newRecent))
    }
  }

  // 清除选择
  const handleClear = () => {
    onTenantSelect?.(undefined as any)
    setSearchValue('')
    setIsOpen(false)
  }

  const displayTenants = searchValue ? searchResults : recentTenants

  return (
    <div className={clsx('relative', className)}>
      {/* 已选择的租户显示 */}
      {selectedTenant && !isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <Logo
            src={selectedTenant.logo}
            alt={selectedTenant.display_name}
            size="sm"
            className="mr-3"
          />
          <div className="flex-1">
            <p className="font-medium text-gray-900">{selectedTenant.display_name}</p>
            <p className="text-sm text-gray-600">{selectedTenant.code}</p>
          </div>
          <button
            onClick={handleClear}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </motion.div>
      )}

      {/* 自动检测提示 */}
      {detectedTenant && !selectedTenant && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-3 p-3 bg-green-50 border border-green-200 rounded-lg"
        >
          <div className="flex items-center">
            <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-green-700">
              检测到租户：{detectedTenant.display_name}
            </span>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleTenantSelect(detectedTenant)}
              className="ml-auto text-green-600 hover:text-green-700"
            >
              使用
            </Button>
          </div>
        </motion.div>
      )}

      {/* 搜索输入框 */}
      {(!selectedTenant || isOpen) && (
        <div className="relative">
          <Input
            ref={inputRef}
            value={searchValue}
            onChange={handleInputChange}
            onFocus={() => setIsOpen(true)}
            placeholder={placeholder}
            className="pr-10"
          />
          
          {/* 搜索图标 */}
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            {isLoading ? (
              <svg className="w-4 h-4 text-gray-400 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
            ) : (
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            )}
          </div>
        </div>
      )}

      {/* 下拉选项 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={dropdownRef}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto"
          >
            {displayTenants.length > 0 ? (
              <div className="py-1">
                {!searchValue && showHistory && recentTenants.length > 0 && (
                  <div className="px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                    最近使用
                  </div>
                )}
                
                {displayTenants.map((tenant, index) => (
                  <motion.button
                    key={tenant.id}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                    onClick={() => handleTenantSelect(tenant)}
                    className="w-full flex items-center px-3 py-2 text-left hover:bg-gray-50 transition-colors"
                  >
                    <Logo
                      src={tenant.logo}
                      alt={tenant.display_name}
                      size="sm"
                      className="mr-3"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 truncate">
                        {tenant.display_name}
                      </p>
                      <p className="text-sm text-gray-600 truncate">
                        {tenant.code}
                      </p>
                    </div>
                  </motion.button>
                ))}
              </div>
            ) : searchValue && !isLoading ? (
              <div className="px-3 py-8 text-center text-gray-500">
                <svg className="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <p className="text-sm">未找到匹配的租户</p>
                <p className="text-xs text-gray-400 mt-1">请检查输入的租户编码或域名</p>
              </div>
            ) : !searchValue && recentTenants.length === 0 ? (
              <div className="px-3 py-8 text-center text-gray-500">
                <svg className="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v9M7 7h.01M7 10h.01" />
                </svg>
                <p className="text-sm">开始输入以搜索租户</p>
                <p className="text-xs text-gray-400 mt-1">支持租户编码、名称或域名搜索</p>
              </div>
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
