# IAM服务第三阶段功能说明

## 概述

第三阶段主要实现了高级权限管理、审计日志、系统配置和高级安全功能，为IAM系统提供了企业级的安全和管理能力。

## 新增功能模块

### 1. 角色权限管理模块 (RBAC)

**路由前缀**: `/api/v1/rbac`

**主要功能**:
- 角色管理：创建、查询、更新、删除角色
- 权限管理：创建权限、分配角色权限
- 用户角色分配：为用户分配/移除角色
- 权限检查：检查用户是否具有指定权限

**核心API**:
- `POST /rbac/roles/create` - 创建角色
- `POST /rbac/roles/query` - 查询角色列表
- `POST /rbac/roles/update` - 更新角色
- `POST /rbac/roles/delete` - 删除角色
- `POST /rbac/permissions/create` - 创建权限
- `POST /rbac/roles/permissions/assign` - 分配角色权限
- `POST /rbac/users/roles/assign` - 分配用户角色
- `POST /rbac/permissions/check` - 权限检查

**特性**:
- 支持角色层级和继承
- 权限缓存优化
- 批量权限操作
- 角色权限继承

### 2. 审计日志系统

**路由前缀**: `/api/v1/audit`

**主要功能**:
- 审计日志记录：自动记录系统操作
- 日志查询：多条件查询和分页
- 统计分析：操作统计和趋势分析
- 日志导出：支持CSV、JSON、Excel格式

**核心API**:
- `POST /audit/logs/query` - 查询审计日志
- `POST /audit/logs/create` - 创建审计日志
- `POST /audit/statistics` - 获取审计统计
- `POST /audit/logs/export` - 导出审计日志

**特性**:
- 多维度查询过滤
- 实时统计分析
- 异步导出处理
- 风险级别分类

### 3. 系统配置模块

**路由前缀**: `/api/v1/system`

**主要功能**:
- 配置管理：设置、获取、删除配置
- 配置继承：支持全局和租户级配置
- 敏感配置：加密存储敏感信息
- 批量操作：批量设置和重置配置

**核心API**:
- `POST /system/config/set` - 设置配置
- `POST /system/config/get` - 获取配置
- `POST /system/config/query` - 查询配置列表
- `POST /system/config/delete` - 删除配置
- `POST /system/config/batch-set` - 批量设置配置
- `POST /system/config/reset` - 重置配置

**特性**:
- 配置层级继承
- 敏感配置加密
- 配置分类管理
- 实时配置更新

### 4. 高级安全功能

**路由前缀**: `/api/v1/security`

**主要功能**:
- 多因子认证(MFA)：TOTP、SMS、Email
- 安全策略：密码策略、会话策略等
- 威胁检测：异常登录、暴力破解检测
- 安全事件：事件记录和处理

**核心API**:
- `POST /security/mfa/setup` - 设置MFA
- `POST /security/mfa/verify-setup` - 验证MFA设置
- `POST /security/mfa/disable` - 禁用MFA
- `POST /security/mfa/backup-codes` - 生成备用恢复码
- `POST /security/policy/set` - 设置安全策略
- `POST /security/events/query` - 查询安全事件
- `POST /security/events/update` - 更新安全事件

**特性**:
- TOTP二维码生成
- 备用恢复码
- 安全策略配置
- 威胁检测和告警

## 技术架构

### 服务层架构

```
routes/
├── rbac.py              # RBAC路由
├── audit.py             # 审计日志路由
├── system_config.py     # 系统配置路由
└── advanced_security.py # 高级安全路由

services/
├── rbac_service.py              # RBAC服务
├── audit_service.py             # 审计服务
├── system_config_service.py     # 系统配置服务
└── advanced_security_service.py # 高级安全服务
```

### 数据模型

```python
# 新增模型
class SecurityPolicy(Base):
    """安全策略模型"""
    policy_id: str
    tenant_id: Optional[str]
    policy_name: str
    policy_config: JSONType
    enabled: bool

class SecurityEvent(Base):
    """安全事件模型"""
    event_id: str
    tenant_id: str
    event_type: str
    severity: str
    title: str
    description: str
    details: JSONType
    status: str
```

### 安全工具扩展

扩展了`SecurityUtils`类，新增：
- TOTP生成和验证
- 备用恢复码生成
- 密码强度检查
- API密钥生成
- URL安全检查

## 配置说明

### 环境变量

```bash
# 加密密钥（用于敏感配置加密）
ENCRYPTION_KEY=your-encryption-key

# MFA配置
MFA_ISSUER_NAME="Your Company IAM"
MFA_QR_CODE_SIZE=200

# 审计配置
AUDIT_LOG_RETENTION_DAYS=365
AUDIT_EXPORT_MAX_RECORDS=10000
```

### 默认配置

系统提供了一套默认的安全配置：

```python
default_configs = {
    # 密码策略
    "password_policy.min_length": 8,
    "password_policy.require_uppercase": True,
    "password_policy.require_lowercase": True,
    "password_policy.require_numbers": True,
    "password_policy.require_symbols": False,
    "password_policy.max_age_days": 90,
    
    # 会话策略
    "session.timeout_hours": 8,
    "session.max_concurrent": 10,
    
    # 登录策略
    "login.max_attempts": 5,
    "login.lockout_minutes": 30,
    
    # MFA策略
    "mfa.enabled": False,
    "mfa.required_for_admin": True,
}
```

## 使用示例

### 1. 设置角色权限

```python
# 创建角色
POST /api/v1/rbac/roles/create
{
    "data": {
        "tenant_id": "tenant_123",
        "role_name": "管理员",
        "role_code": "ADMIN",
        "description": "系统管理员角色",
        "level": 1,
        "permissions": ["user:read", "user:write", "role:read"]
    }
}

# 分配用户角色
POST /api/v1/rbac/users/roles/assign
{
    "data": {
        "tenant_id": "tenant_123",
        "user_id": "user_123",
        "role_ids": ["role_123"],
        "operation": "assign"
    }
}
```

### 2. 设置MFA

```python
# 设置TOTP MFA
POST /api/v1/security/mfa/setup
{
    "data": {
        "tenant_id": "tenant_123",
        "user_id": "user_123",
        "mfa_type": "totp"
    }
}

# 验证MFA设置
POST /api/v1/security/mfa/verify-setup
{
    "data": {
        "tenant_id": "tenant_123",
        "user_id": "user_123",
        "setup_token": "setup_token_123",
        "verification_code": "123456"
    }
}
```

### 3. 查询审计日志

```python
POST /api/v1/audit/logs/query
{
    "data": {
        "tenant_id": "tenant_123",
        "start_time": "2025-01-01T00:00:00.000000",
        "end_time": "2025-01-31T23:59:59.999999",
        "action": "LOGIN",
        "page": 1,
        "page_size": 20
    }
}
```

### 4. 配置系统策略

```python
# 设置密码策略
POST /api/v1/system/config/set
{
    "data": {
        "tenant_id": "tenant_123",
        "config_key": "password_policy.min_length",
        "config_value": 12,
        "description": "密码最小长度要求",
        "category": "security"
    }
}
```

## 安全考虑

1. **权限控制**: 所有API都需要适当的权限验证
2. **数据加密**: 敏感配置和MFA密钥加密存储
3. **审计追踪**: 所有操作都有完整的审计日志
4. **缓存安全**: 权限信息缓存有适当的TTL
5. **输入验证**: 所有输入都经过严格验证和清理

## 性能优化

1. **权限缓存**: 用户权限信息缓存30分钟
2. **统计缓存**: 审计统计数据缓存1小时
3. **批量操作**: 支持批量权限分配和配置设置
4. **异步处理**: 大文件导出采用异步处理
5. **索引优化**: 数据库查询都有相应的索引支持

## 监控和告警

1. **安全事件**: 高风险操作自动创建安全事件
2. **异常检测**: 登录异常、权限提升等自动检测
3. **性能监控**: API响应时间和错误率监控
4. **资源监控**: 缓存使用率和数据库连接监控
