from datetime import timedelta
from typing import Optional

from commonlib.configs.storages.persistence.redis_config import RedisConfig
from commonlib.core.logging.tsif_logging import app_logger
from commonlib.storages.base import BaseConnector, ConnectorParams
from redis.asyncio import ConnectionPool, Redis
from redis.exceptions import ConnectionError as RedisConnectionError


class RedisConnector(BaseConnector[Redis, RedisConfig]):
    """支持热更新的Redis异步连接器"""

    _config_class = RedisConfig

    def __init__(self, initial_params: Optional[ConnectorParams] = None):
        super().__init__(
            initial_params
            or ConnectorParams(
                heartbeat_interval=timedelta(seconds=15)  # Redis默认心跳间隔
            ),
        )
        self._connection: Optional[Redis] = None
        self._pool: Optional[ConnectionPool] = None

    @property
    def name(self) -> str:
        return "Redis"

    async def _connect(self) -> bool:
        if not self.config:
            raise RuntimeError(f"{self.name} Config not loaded")

        try:
            # 关闭旧连接（如果存在）
            await self._close()

            # 创建新连接池
            self._pool = ConnectionPool.from_url(
                str(self.config.DSN), **self.config.REDIS_POOL_CONFIG
            )
            self._connection = Redis(connection_pool=self._pool)
            return True
        except Exception as e:
            app_logger.error(f"Unexpected {self.name} error: {e}", exception=True)
            return False

    async def _close(self) -> None:
        """安全关闭连接池（内部方法）"""
        if self._connection:
            try:
                await self._connection.aclose()
            except Exception as e:
                app_logger.error(f"{self.name} client close error: {e}", exception=True)
        if self._pool:
            try:
                await self._pool.disconnect()
            except Exception as e:
                app_logger.error(f"{self.name} pool close error: {e}", exception=True)
        self._connection = None
        self._pool = None

    async def _perform_heartbeat_check(self) -> bool:
        try:
            if not self._connection or (
                self._connection.connection and self._connection.connection.is_connected
            ):
                return False
            # 使用PING命令检测连接状态
            return await self._connection.ping()
        except RedisConnectionError as e:
            app_logger.error(f"{self.name} heartbeat failed: {e}", exception=True)
            return False
        except Exception as e:
            app_logger.error(
                f"{self.name} Unexpected heartbeat error: {e}", exception=True
            )
            return False
