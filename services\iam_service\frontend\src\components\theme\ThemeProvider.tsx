/**
 * @file 主题提供者组件
 * @description 全局主题管理，支持亮色/暗色模式切换和租户自定义主题
 */

import React from 'react'
import { ThemeMode, ThemeVariant, TenantInfo, ThemeConfig } from '@/types'
import { 
  THEME_MODES, 
  THEME_VARIANTS, 
  THEME_STORAGE_KEYS, 
  DEFAULT_THEME_CONFIG,
  LIGHT_THEME_COLORS,
  DARK_THEME_COLORS,
  CSS_VARIABLE_PREFIX
} from '@/constants'

interface ThemeContextValue {
  mode: ThemeMode
  variant: ThemeVariant
  config: ThemeConfig
  isDark: boolean
  setMode: (mode: ThemeMode) => void
  setVariant: (variant: ThemeVariant) => void
  setTenantTheme: (tenant: TenantInfo) => void
  resetTheme: () => void
}

const ThemeContext = React.createContext<ThemeContextValue | undefined>(undefined)

export interface ThemeProviderProps {
  children: React.ReactNode
  defaultMode?: ThemeMode
  defaultVariant?: ThemeVariant
  tenant?: TenantInfo
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultMode = THEME_MODES.LIGHT,
  defaultVariant = THEME_VARIANTS.DEFAULT,
  tenant
}) => {
  const [mode, setModeState] = React.useState<ThemeMode>(() => {
    if (typeof window === 'undefined') return defaultMode
    
    const stored = localStorage.getItem(THEME_STORAGE_KEYS.MODE)
    if (stored && Object.values(THEME_MODES).includes(stored as ThemeMode)) {
      return stored as ThemeMode
    }
    return defaultMode
  })

  const [variant, setVariantState] = React.useState<ThemeVariant>(() => {
    if (typeof window === 'undefined') return defaultVariant
    
    const stored = localStorage.getItem(THEME_STORAGE_KEYS.VARIANT)
    if (stored && Object.values(THEME_VARIANTS).includes(stored as ThemeVariant)) {
      return stored as ThemeVariant
    }
    return defaultVariant
  })

  const [config, setConfig] = React.useState<ThemeConfig>(() => {
    if (typeof window === 'undefined') return DEFAULT_THEME_CONFIG
    
    const stored = localStorage.getItem(THEME_STORAGE_KEYS.TENANT_CONFIG)
    if (stored) {
      try {
        return { ...DEFAULT_THEME_CONFIG, ...JSON.parse(stored) }
      } catch (error) {
        console.error('Failed to parse stored theme config:', error)
      }
    }
    return DEFAULT_THEME_CONFIG
  })

  // 检测系统主题偏好
  const [systemPrefersDark, setSystemPrefersDark] = React.useState(false)

  React.useEffect(() => {
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    setSystemPrefersDark(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemPrefersDark(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // 计算实际的暗色模式状态
  const isDark = React.useMemo(() => {
    if (mode === THEME_MODES.DARK) return true
    if (mode === THEME_MODES.LIGHT) return false
    return systemPrefersDark // auto mode
  }, [mode, systemPrefersDark])

  // 设置主题模式
  const setMode = React.useCallback((newMode: ThemeMode) => {
    setModeState(newMode)
    localStorage.setItem(THEME_STORAGE_KEYS.MODE, newMode)
  }, [])

  // 设置主题变体
  const setVariant = React.useCallback((newVariant: ThemeVariant) => {
    setVariantState(newVariant)
    localStorage.setItem(THEME_STORAGE_KEYS.VARIANT, newVariant)
  }, [])

  // 设置租户主题
  const setTenantTheme = React.useCallback((tenantInfo: TenantInfo) => {
    if (tenantInfo.theme) {
      const tenantConfig: Partial<ThemeConfig> = {
        variant: THEME_VARIANTS.TENANT,
        colors: {
          ...DEFAULT_THEME_CONFIG.colors,
          primary: tenantInfo.theme.primary_color ? {
            ...DEFAULT_THEME_CONFIG.colors.primary,
            500: tenantInfo.theme.primary_color,
            600: tenantInfo.theme.primary_color
          } : DEFAULT_THEME_CONFIG.colors.primary,
          secondary: tenantInfo.theme.secondary_color ? {
            ...DEFAULT_THEME_CONFIG.colors.secondary,
            500: tenantInfo.theme.secondary_color,
            600: tenantInfo.theme.secondary_color
          } : DEFAULT_THEME_CONFIG.colors.secondary
        }
      }

      setConfig(prev => ({ ...prev, ...tenantConfig }))
      setVariant(THEME_VARIANTS.TENANT)
      localStorage.setItem(THEME_STORAGE_KEYS.TENANT_CONFIG, JSON.stringify(tenantConfig))
    }
  }, [setVariant])

  // 重置主题
  const resetTheme = React.useCallback(() => {
    setModeState(THEME_MODES.LIGHT)
    setVariantState(THEME_VARIANTS.DEFAULT)
    setConfig(DEFAULT_THEME_CONFIG)
    localStorage.removeItem(THEME_STORAGE_KEYS.MODE)
    localStorage.removeItem(THEME_STORAGE_KEYS.VARIANT)
    localStorage.removeItem(THEME_STORAGE_KEYS.TENANT_CONFIG)
  }, [])

  // 应用CSS变量
  React.useEffect(() => {
    if (typeof window === 'undefined') return

    const root = document.documentElement
    const colors = isDark ? DARK_THEME_COLORS : LIGHT_THEME_COLORS

    // 应用基础颜色变量
    Object.entries(colors).forEach(([category, categoryColors]) => {
      Object.entries(categoryColors).forEach(([key, value]) => {
        root.style.setProperty(`${CSS_VARIABLE_PREFIX}-${category}-${key}`, value)
      })
    })

    // 应用主题配置中的颜色
    if (config.colors) {
      Object.entries(config.colors).forEach(([colorName, colorShades]) => {
        if (typeof colorShades === 'object') {
          Object.entries(colorShades).forEach(([shade, value]) => {
            root.style.setProperty(`${CSS_VARIABLE_PREFIX}-${colorName}-${shade}`, value)
          })
        }
      })
    }

    // 设置主题类名
    root.classList.toggle('dark', isDark)
    root.classList.toggle('light', !isDark)
    
    // 设置变体类名
    root.classList.remove('theme-default', 'theme-tenant', 'theme-custom')
    root.classList.add(`theme-${variant}`)

  }, [isDark, variant, config])

  // 自动应用租户主题
  React.useEffect(() => {
    if (tenant && variant !== THEME_VARIANTS.TENANT) {
      setTenantTheme(tenant)
    }
  }, [tenant, variant, setTenantTheme])

  const contextValue: ThemeContextValue = {
    mode,
    variant,
    config,
    isDark,
    setMode,
    setVariant,
    setTenantTheme,
    resetTheme
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  )
}

// Hook for using theme context
export const useTheme = (): ThemeContextValue => {
  const context = React.useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Theme toggle component
export interface ThemeToggleProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className,
  size = 'md'
}) => {
  const { mode, isDark, setMode } = useTheme()

  const toggleTheme = () => {
    if (mode === THEME_MODES.LIGHT) {
      setMode(THEME_MODES.DARK)
    } else if (mode === THEME_MODES.DARK) {
      setMode(THEME_MODES.AUTO)
    } else {
      setMode(THEME_MODES.LIGHT)
    }
  }

  const getIcon = () => {
    if (mode === THEME_MODES.LIGHT) {
      return (
        <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      )
    } else if (mode === THEME_MODES.DARK) {
      return (
        <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      )
    } else {
      return (
        <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      )
    }
  }

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  return (
    <button
      onClick={toggleTheme}
      className={`
        p-2 rounded-lg transition-colors duration-200
        hover:bg-gray-100 dark:hover:bg-gray-800
        focus:outline-none focus:ring-2 focus:ring-blue-500
        ${className}
      `}
      title={`当前: ${mode === THEME_MODES.LIGHT ? '亮色' : mode === THEME_MODES.DARK ? '暗色' : '自动'}`}
    >
      <div className={sizeClasses[size]}>
        {getIcon()}
      </div>
    </button>
  )
}
