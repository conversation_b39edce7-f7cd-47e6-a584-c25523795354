"""
RBAC服务使用示例

演示如何使用RBAC服务进行角色和权限管理
"""

import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from services.iam_service.services.rbac_service import RBACService
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission, AuditLog
)


async def create_rbac_service():
    """创建RBAC服务实例"""
    # 创建数据库引擎和会话
    engine = create_async_engine("sqlite+aiosqlite:///example.db")
    async_session = sessionmaker(engine, class_=AsyncSession)
    session = async_session()
    
    # 创建Redis仓库
    redis_repo = RedisRepository(host="localhost", port=6379, db=0)
    
    # 创建RBAC服务
    rbac_service = RBACService(
        session=session,
        redis_repo=redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        audit_log_model=AuditLog
    )
    
    return rbac_service, session


async def demo_role_management():
    """演示角色管理功能"""
    rbac_service, session = await create_rbac_service()
    
    try:
        print("=== 角色管理演示 ===")
        
        # 1. 创建角色
        print("\n1. 创建角色")
        role_result = await rbac_service.create_role(
            tenant_id="tenant_demo",
            role_name="系统管理员",
            role_code="SYSTEM_ADMIN",
            description="系统管理员角色，拥有所有权限",
            level=1,
            is_system=True,
            permissions=["user:read", "user:write", "role:read", "role:write"]
        )
        print(f"创建角色成功: {role_result}")
        
        # 2. 查询角色
        print("\n2. 查询角色")
        query_result = await rbac_service.query_roles(
            tenant_id="tenant_demo",
            include_permissions=True,
            include_users=True,
            page=1,
            page_size=10
        )
        print(f"查询到 {query_result['total']} 个角色")
        for role in query_result['roles']:
            print(f"  - {role['role_name']} ({role['role_code']})")
        
        # 3. 更新角色
        print("\n3. 更新角色")
        update_result = await rbac_service.update_role(
            tenant_id="tenant_demo",
            role_id=role_result['role_id'],
            description="更新后的系统管理员角色描述"
        )
        print(f"更新角色成功: {update_result}")
        
    except Exception as e:
        print(f"角色管理演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_permission_management():
    """演示权限管理功能"""
    rbac_service, session = await create_rbac_service()
    
    try:
        print("\n=== 权限管理演示 ===")
        
        # 1. 创建权限
        print("\n1. 创建权限")
        permissions_to_create = [
            {
                "permission_code": "user:read",
                "permission_name": "查看用户",
                "description": "查看用户基本信息的权限",
                "resource_type": "user",
                "action": "read",
                "category": "business"
            },
            {
                "permission_code": "user:write",
                "permission_name": "编辑用户",
                "description": "编辑用户信息的权限",
                "resource_type": "user",
                "action": "write",
                "category": "business"
            },
            {
                "permission_code": "role:manage",
                "permission_name": "管理角色",
                "description": "管理角色的权限",
                "resource_type": "role",
                "action": "manage",
                "category": "system"
            }
        ]
        
        created_permissions = []
        for perm_data in permissions_to_create:
            perm_result = await rbac_service.create_permission(
                tenant_id="tenant_demo",
                **perm_data
            )
            created_permissions.append(perm_result)
            print(f"创建权限: {perm_result['permission_code']}")
        
        # 2. 分配角色权限
        print("\n2. 分配角色权限")
        # 假设我们有一个角色ID
        role_id = "role_demo_123"
        permission_codes = [perm['permission_code'] for perm in created_permissions]
        
        assign_result = await rbac_service.assign_role_permissions(
            tenant_id="tenant_demo",
            role_id=role_id,
            permission_codes=permission_codes,
            operation="assign"
        )
        print(f"分配权限成功: 影响 {assign_result['affected_permissions']} 个权限")
        
    except Exception as e:
        print(f"权限管理演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_user_role_assignment():
    """演示用户角色分配功能"""
    rbac_service, session = await create_rbac_service()
    
    try:
        print("\n=== 用户角色分配演示 ===")
        
        # 1. 分配用户角色
        print("\n1. 分配用户角色")
        user_id = "user_demo_123"
        role_ids = ["role_demo_123", "role_demo_456"]
        
        assign_result = await rbac_service.assign_user_roles(
            tenant_id="tenant_demo",
            user_id=user_id,
            role_ids=role_ids,
            operation="assign",
            effective_time=datetime.now().isoformat(),
            expire_time=None  # 永久有效
        )
        print(f"分配角色成功: 影响 {assign_result['affected_roles']} 个角色")
        print(f"当前角色: {assign_result['current_roles']}")
        
        # 2. 权限检查
        print("\n2. 权限检查")
        permission_checks = ["user:read", "user:write", "admin:delete"]
        
        for permission_code in permission_checks:
            check_result = await rbac_service.check_permission(
                tenant_id="tenant_demo",
                user_id=user_id,
                permission_code=permission_code
            )
            status = "✓" if check_result['has_permission'] else "✗"
            source = f" (来源: {check_result['permission_source']})" if check_result['permission_source'] else ""
            print(f"  {status} {permission_code}{source}")
        
    except Exception as e:
        print(f"用户角色分配演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_advanced_features():
    """演示高级功能"""
    rbac_service, session = await create_rbac_service()
    
    try:
        print("\n=== 高级功能演示 ===")
        
        # 1. 角色层级管理
        print("\n1. 角色层级管理")
        
        # 创建父角色
        parent_role = await rbac_service.create_role(
            tenant_id="tenant_demo",
            role_name="部门经理",
            role_code="DEPT_MANAGER",
            level=1,
            permissions=["user:read", "report:read"]
        )
        
        # 创建子角色
        child_role = await rbac_service.create_role(
            tenant_id="tenant_demo",
            role_name="项目经理",
            role_code="PROJECT_MANAGER",
            parent_role_id=parent_role['role_id'],
            level=2,
            permissions=["project:manage"]
        )
        
        print(f"创建父角色: {parent_role['role_name']}")
        print(f"创建子角色: {child_role['role_name']}")
        
        # 2. 批量权限操作
        print("\n2. 批量权限操作")
        
        # 替换角色权限
        new_permissions = ["user:read", "user:write", "project:read", "project:write"]
        replace_result = await rbac_service.assign_role_permissions(
            tenant_id="tenant_demo",
            role_id=child_role['role_id'],
            permission_codes=new_permissions,
            operation="replace"
        )
        print(f"替换权限成功: {replace_result['current_permissions']}")
        
        # 3. 角色删除和用户转移
        print("\n3. 角色删除和用户转移")
        
        # 创建一个临时角色用于演示删除
        temp_role = await rbac_service.create_role(
            tenant_id="tenant_demo",
            role_name="临时角色",
            role_code="TEMP_ROLE",
            level=3
        )
        
        # 删除角色（假设有用户关联，转移到其他角色）
        delete_result = await rbac_service.delete_role(
            tenant_id="tenant_demo",
            role_id=temp_role['role_id'],
            force_delete=False,
            transfer_to_role_id=parent_role['role_id']
        )
        print(f"删除角色成功: 影响 {delete_result['affected_users']} 个用户")
        
    except Exception as e:
        print(f"高级功能演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def main():
    """主函数"""
    print("RBAC服务使用示例")
    print("=" * 50)
    
    # 运行各个演示
    await demo_role_management()
    await demo_permission_management()
    await demo_user_role_assignment()
    await demo_advanced_features()
    
    print("\n演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
