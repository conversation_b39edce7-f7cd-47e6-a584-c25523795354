"""
领域模型基类模块。

此模块提供所有领域模型的基础类定义，
包含基本的ID和时间戳字段。
"""

from datetime import datetime, timezone

from sqlalchemy import DateTime, MetaData, String, func, text
from sqlalchemy.orm import DeclarativeBase, Mapped, declared_attr, mapped_column

# ================================
# 数据库命名约定
# ================================

POSTGRES_INDEXES_NAMING_CONVENTION = {
    "ix": "%(column_0_label)s_idx",
    "uq": "%(table_name)s_%(column_0_name)s_key",
    "ck": "%(table_name)s_%(constraint_name)s_check",
    "fk": "%(table_name)s_%(column_0_name)s_fkey",
    "pk": "%(table_name)s_pkey",
}


class Base(DeclarativeBase):
    """现代化实体基类 (SQLAlchemy 2.0+ 风格)。

    提供基础的ID和时间戳字段，适用于所有实体。
    只包含最基本的字段，其他字段由具体模型类或混入类定义。
    
    属性:
        id: 主键ID，使用UUID字符串格式
        created_at: 记录创建时间
        updated_at: 最后更新时间
    """

    __abstract__ = True
    metadata = MetaData(naming_convention=POSTGRES_INDEXES_NAMING_CONVENTION)
