/**
 * @file 注册表单组件 V2.0
 * @description 重构后的注册表单，使用React Hook Form + Zod验证，支持新的UI设计
 */

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import clsx from 'clsx'
import { Button, Input } from '@/components/ui'
import { SocialLogin } from '@/components/auth'
import { RegisterRequest, TenantInfo } from '@/types'
import * as authApi from '@/api/auth'

// Zod验证模式
const registerSchema = z.object({
  tenant_code: z.string()
    .min(2, '租户编码至少需要2个字符')
    .max(50, '租户编码最多50个字符')
    .regex(/^[a-zA-Z0-9_-]+$/, '租户编码只能包含字母、数字、下划线和连字符'),
  username: z.string()
    .min(3, '用户名至少需要3个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_-]+$/, '用户名只能包含字母、数字、下划线和连字符'),
  email: z.string().email('请输入有效的邮箱地址'),
  phone: z.string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码')
    .optional()
    .or(z.literal('')),
  nickname: z.string()
    .max(50, '昵称最多50个字符')
    .optional(),
  password: z.string()
    .min(8, '密码至少需要8个字符')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      '密码必须包含大小写字母、数字和特殊字符'),
  confirmPassword: z.string(),
  verification_code: z.string()
    .min(4, '验证码至少4位')
    .max(8, '验证码最多8位')
    .regex(/^[0-9]+$/, '验证码只能包含数字'),
  agree_terms: z.boolean().refine(val => val === true, '必须同意服务条款')
}).refine(data => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword']
})

type RegisterFormData = z.infer<typeof registerSchema>

export interface RegisterFormProps {
  tenant?: TenantInfo
  onSuccess?: (data: any) => void
  onError?: (error: string) => void
  onSwitchToLogin?: () => void
  className?: string
  showSocialLogin?: boolean
}

export const RegisterForm: React.FC<RegisterFormProps> = ({
  tenant,
  onSuccess,
  onError,
  onSwitchToLogin,
  className,
  showSocialLogin = true
}) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const [isSendingCode, setIsSendingCode] = React.useState(false)
  const [codeCountdown, setCodeCountdown] = React.useState(0)
  const [codeSent, setCodeSent] = React.useState(false)
  const [codeId, setCodeId] = React.useState('')

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    getValues,
    trigger
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      tenant_code: tenant?.code || '',
      username: '',
      email: '',
      phone: '',
      nickname: '',
      password: '',
      confirmPassword: '',
      verification_code: '',
      agree_terms: false
    }
  })

  // 如果有租户信息，自动填充租户编码
  React.useEffect(() => {
    if (tenant?.code) {
      setValue('tenant_code', tenant.code)
    }
  }, [tenant, setValue])

  // 发送验证码
  const handleSendCode = async () => {
    // 先验证邮箱和租户编码
    const isEmailValid = await trigger('email')
    const isTenantCodeValid = await trigger('tenant_code')
    
    if (!isEmailValid || !isTenantCodeValid) {
      return
    }

    const { email, tenant_code } = getValues()

    setIsSendingCode(true)

    try {
      // 先获取租户信息
      const tenantInfo = await authApi.getTenantByCode(tenant_code)

      const sendCodeData = {
        tenant_id: tenantInfo.tenant_id,
        email,
        scene: 'register' as const
      }

      const response = await authApi.sendEmailCode(sendCodeData)
      setCodeId(response.code_id)
      setCodeSent(true)
      setCodeCountdown(60)

      // 开始倒计时
      const timer = setInterval(() => {
        setCodeCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)

    } catch (err: any) {
      onError?.(err.message || '发送验证码失败')
    } finally {
      setIsSendingCode(false)
    }
  }

  const onSubmit = async (data: RegisterFormData) => {
    if (!codeId) {
      onError?.('请先获取邮箱验证码')
      return
    }

    setIsLoading(true)

    try {
      const registerRequest: RegisterRequest = {
        tenant_code: data.tenant_code,
        username: data.username,
        email: data.email,
        phone: data.phone || undefined,
        nickname: data.nickname || undefined,
        password: data.password,
        verification_code: data.verification_code,
        code_id: codeId,
        agree_terms: data.agree_terms
      }

      const response = await authApi.register(registerRequest)
      onSuccess?.(response)
    } catch (err: any) {
      const errorMessage = err.message || '注册失败，请重试'
      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSocialLogin = (providerId: string) => {
    // TODO: 实现社交登录逻辑
    console.log('Social login with:', providerId)
  }

  return (
    <div className={clsx('space-y-6', className)}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* 租户编码 */}
        {!tenant && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Input
              label="租户编码"
              placeholder="请输入租户编码"
              error={errors.tenant_code?.message}
              disabled={isLoading || isSubmitting}
              {...register('tenant_code')}
            />
          </motion.div>
        )}

        {/* 用户名 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Input
            label="用户名"
            placeholder="请输入用户名"
            error={errors.username?.message}
            disabled={isLoading || isSubmitting}
            {...register('username')}
          />
        </motion.div>

        {/* 邮箱 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Input
            label="邮箱"
            type="email"
            placeholder="请输入邮箱地址"
            error={errors.email?.message}
            disabled={isLoading || isSubmitting}
            {...register('email')}
          />
        </motion.div>

        {/* 手机号（可选） */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Input
            label="手机号（可选）"
            type="tel"
            placeholder="请输入手机号"
            error={errors.phone?.message}
            disabled={isLoading || isSubmitting}
            {...register('phone')}
          />
        </motion.div>

        {/* 昵称（可选） */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Input
            label="昵称（可选）"
            placeholder="请输入昵称"
            error={errors.nickname?.message}
            disabled={isLoading || isSubmitting}
            {...register('nickname')}
          />
        </motion.div>

        {/* 密码 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <Input
            label="密码"
            type="password"
            placeholder="请输入密码"
            error={errors.password?.message}
            disabled={isLoading || isSubmitting}
            {...register('password')}
          />
        </motion.div>

        {/* 确认密码 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
        >
          <Input
            label="确认密码"
            type="password"
            placeholder="请再次输入密码"
            error={errors.confirmPassword?.message}
            disabled={isLoading || isSubmitting}
            {...register('confirmPassword')}
          />
        </motion.div>

        {/* 验证码 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.7 }}
        >
          <div className="flex space-x-2">
            <div className="flex-1">
              <Input
                label="邮箱验证码"
                placeholder="请输入验证码"
                error={errors.verification_code?.message}
                disabled={isLoading || isSubmitting}
                {...register('verification_code')}
              />
            </div>
            <div className="flex items-end">
              <Button
                type="button"
                variant="outline"
                size="md"
                onClick={handleSendCode}
                disabled={isSendingCode || codeCountdown > 0 || isLoading || isSubmitting}
                loading={isSendingCode}
                className="whitespace-nowrap"
              >
                {codeCountdown > 0 ? `${codeCountdown}s` : codeSent ? '重新发送' : '获取验证码'}
              </Button>
            </div>
          </div>
        </motion.div>

        {/* 同意条款 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.8 }}
        >
          <label className="flex items-start">
            <input
              type="checkbox"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
              disabled={isLoading || isSubmitting}
              {...register('agree_terms')}
            />
            <span className="ml-2 text-sm text-gray-600">
              我已阅读并同意
              <a href="/terms" className="text-blue-600 hover:text-blue-500 mx-1">服务条款</a>
              和
              <a href="/privacy" className="text-blue-600 hover:text-blue-500 mx-1">隐私政策</a>
            </span>
          </label>
          {errors.agree_terms && (
            <p className="mt-1 text-sm text-red-600">{errors.agree_terms.message}</p>
          )}
        </motion.div>

        {/* 注册按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.9 }}
        >
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isLoading || isSubmitting}
            disabled={isLoading || isSubmitting}
            className="w-full"
          >
            {isLoading || isSubmitting ? '注册中...' : '注册'}
          </Button>
        </motion.div>
      </form>

      {/* 社交登录 */}
      {showSocialLogin && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.0 }}
        >
          <SocialLogin
            onProviderClick={handleSocialLogin}
            disabled={isLoading || isSubmitting}
            loading={isLoading || isSubmitting}
          />
        </motion.div>
      )}

      {/* 登录链接 */}
      {onSwitchToLogin && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 1.1 }}
          className="text-center"
        >
          <p className="text-sm text-gray-600">
            已有账户？
            <button
              type="button"
              onClick={onSwitchToLogin}
              className="ml-1 text-blue-600 hover:text-blue-500 font-medium transition-colors"
              disabled={isLoading || isSubmitting}
            >
              立即登录
            </button>
          </p>
        </motion.div>
      )}
    </div>
  )
}
