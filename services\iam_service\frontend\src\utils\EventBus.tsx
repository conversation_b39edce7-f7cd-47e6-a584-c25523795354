/**
 * @file Event Bus 事件总线
 * @description 提供跨模块通信的事件总线系统，支持类型安全的事件发布和订阅
 * @status 框架文件 - 完成
 */

import React, { createContext, useContext, useCallback, useRef, ReactNode } from 'react'

// 事件类型定义
export interface EventMap {
  // 全局错误事件
  'global:error': { message: string; code?: string; stack?: string }
  // 全局成功消息
  'global:success': { message: string }
  // 全局警告消息
  'global:warning': { message: string }
  // 用户登录状态变化
  'auth:login': { user: any }
  'auth:logout': void
  // 导航事件
  'navigation:change': { path: string; title?: string }
  // 数据刷新事件
  'data:refresh': { module: string; type?: string }
  // 模态框事件
  'modal:open': { id: string; props?: Record<string, any> }
  'modal:close': { id: string }
  // 通知事件
  'notification:show': { 
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message?: string
    duration?: number
  }
}

export type EventName = keyof EventMap
export type EventHandler<T extends EventName> = (data: EventMap[T]) => void
export type EventHandlerMap = {
  [K in EventName]?: EventHandler<K>[]
}

interface EventBusContextType {
  emit: <T extends EventName>(event: T, data: EventMap[T]) => void
  on: <T extends EventName>(event: T, handler: EventHandler<T>) => () => void
  off: <T extends EventName>(event: T, handler: EventHandler<T>) => void
  once: <T extends EventName>(event: T, handler: EventHandler<T>) => void
}

const EventBusContext = createContext<EventBusContextType | null>(null)

interface EventBusProviderProps {
  children: ReactNode
}

export function EventBusProvider({ children }: EventBusProviderProps) {
  const handlersRef = useRef<EventHandlerMap>({})

  const emit = useCallback(<T extends EventName>(event: T, data: EventMap[T]) => {
    const handlers = handlersRef.current[event]
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data as any)
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error)
        }
      })
    }
  }, [])

  const on = useCallback(<T extends EventName>(event: T, handler: EventHandler<T>) => {
    if (!handlersRef.current[event]) {
      handlersRef.current[event] = []
    }
    handlersRef.current[event]!.push(handler as any)

    // 返回取消订阅函数
    return () => {
      off(event, handler)
    }
  }, [])

  const off = useCallback(<T extends EventName>(event: T, handler: EventHandler<T>) => {
    const handlers = handlersRef.current[event]
    if (handlers) {
      const index = handlers.indexOf(handler as any)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }, [])

  const once = useCallback(<T extends EventName>(event: T, handler: EventHandler<T>) => {
    const onceHandler = (data: EventMap[T]) => {
      handler(data)
      off(event, onceHandler as EventHandler<T>)
    }
    on(event, onceHandler as EventHandler<T>)
  }, [on, off])

  const value: EventBusContextType = {
    emit,
    on,
    off,
    once,
  }

  return (
    <EventBusContext.Provider value={value}>
      {children}
    </EventBusContext.Provider>
  )
}

export function useEventBus() {
  const context = useContext(EventBusContext)
  if (!context) {
    throw new Error('useEventBus must be used within an EventBusProvider')
  }
  return context
}

// 便捷hooks
export function useEventListener<T extends EventName>(
  event: T,
  handler: EventHandler<T>,
  deps: React.DependencyList = []
) {
  const { on } = useEventBus()

  React.useEffect(() => {
    const unsubscribe = on(event, handler)
    return unsubscribe
  }, deps)
}

export function useEventEmitter() {
  const { emit } = useEventBus()
  return emit
} 