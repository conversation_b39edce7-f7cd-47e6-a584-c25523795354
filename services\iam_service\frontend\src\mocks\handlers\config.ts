/**
 * @file 系统配置API Mock处理器
 * @description 模拟系统配置相关的API响应
 * @status 框架文件 - 完成
 */

import { http, HttpResponse } from 'msw'
import { mockConfigs } from '../data/configs'
import { ApiResponse, SystemConfig, PaginatedResponse } from '@/api/types'

export const configHandlers = [
  // 获取配置列表
  http.get('/api/configs', async ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
    const category = url.searchParams.get('category') || ''
    const search = url.searchParams.get('search') || ''

    // 过滤配置
    let filteredConfigs = mockConfigs
    if (category) {
      filteredConfigs = filteredConfigs.filter(config => config.category === category)
    }
    if (search) {
      filteredConfigs = filteredConfigs.filter(config =>
        config.key.toLowerCase().includes(search.toLowerCase()) ||
        config.description?.toLowerCase().includes(search.toLowerCase())
      )
    }

    // 分页
    const total = filteredConfigs.length
    const totalPages = Math.ceil(total / pageSize)
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const items = filteredConfigs.slice(startIndex, endIndex)

    const response: ApiResponse<PaginatedResponse<SystemConfig>> = {
      success: true,
      data: {
        items,
        total,
        page,
        pageSize,
        totalPages,
      },
      message: '获取配置列表成功',
    }

    return HttpResponse.json(response)
  }),

  // 获取单个配置
  http.get('/api/configs/:id', async ({ params }) => {
    const { id } = params
    const config = mockConfigs.find(c => c.id === id)

    if (!config) {
      return HttpResponse.json({
        success: false,
        message: '配置不存在',
        code: 'CONFIG_NOT_FOUND',
      }, { status: 404 })
    }

    const response: ApiResponse<SystemConfig> = {
      success: true,
      data: config,
      message: '获取配置成功',
    }

    return HttpResponse.json(response)
  }),

  // 创建配置
  http.post('/api/configs', async ({ request }) => {
    const configData = await request.json() as any

    // 检查key是否已存在
    const existingConfig = mockConfigs.find(c => c.key === configData.key)
    if (existingConfig) {
      return HttpResponse.json({
        success: false,
        message: '配置键名已存在',
        code: 'CONFIG_KEY_EXISTS',
      }, { status: 400 })
    }

    // 创建新配置
    const newConfig: SystemConfig = {
      id: `config_${Date.now()}`,
      key: configData.key,
      value: configData.value,
      description: configData.description,
      category: configData.category,
      isPublic: configData.isPublic || false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    mockConfigs.push(newConfig)

    const response: ApiResponse<SystemConfig> = {
      success: true,
      data: newConfig,
      message: '配置创建成功',
    }

    return HttpResponse.json(response, { status: 201 })
  }),

  // 更新配置
  http.put('/api/configs/:id', async ({ params, request }) => {
    const { id } = params
    const updateData = await request.json() as any

    const configIndex = mockConfigs.findIndex(c => c.id === id)
    if (configIndex === -1) {
      return HttpResponse.json({
        success: false,
        message: '配置不存在',
        code: 'CONFIG_NOT_FOUND',
      }, { status: 404 })
    }

    // 更新配置
    const updatedConfig = {
      ...mockConfigs[configIndex],
      ...updateData,
      updatedAt: new Date().toISOString(),
    }
    mockConfigs[configIndex] = updatedConfig

    const response: ApiResponse<SystemConfig> = {
      success: true,
      data: updatedConfig,
      message: '配置更新成功',
    }

    return HttpResponse.json(response)
  }),

  // 删除配置
  http.delete('/api/configs/:id', async ({ params }) => {
    const { id } = params

    const configIndex = mockConfigs.findIndex(c => c.id === id)
    if (configIndex === -1) {
      return HttpResponse.json({
        success: false,
        message: '配置不存在',
        code: 'CONFIG_NOT_FOUND',
      }, { status: 404 })
    }

    // 删除配置
    mockConfigs.splice(configIndex, 1)

    const response: ApiResponse<null> = {
      success: true,
      data: null,
      message: '配置删除成功',
    }

    return HttpResponse.json(response)
  }),

  // 获取配置分类列表
  http.get('/api/configs/categories', async () => {
    const categories = [...new Set(mockConfigs.map(c => c.category))]

    const response: ApiResponse<string[]> = {
      success: true,
      data: categories,
      message: '获取配置分类成功',
    }

    return HttpResponse.json(response)
  }),
]