"""
邮件服务

提供邮件发送功能，支持多种邮件模板和配置
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional, Dict, Any
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor

from commonlib.exceptions.exceptions import BusinessError


class EmailTemplate:
    """邮件模板"""
    
    # 用户激活邮件模板
    USER_ACTIVATION = """
    <html>
    <body>
        <h2>欢迎加入 {tenant_name}！</h2>
        <p>亲爱的 {username}，</p>
        <p>您的账户已创建成功，请点击下面的链接激活您的账户：</p>
        <p><a href="{activation_url}" style="background-color: #4CAF50; color: white; padding: 14px 20px; text-decoration: none; display: inline-block;">激活账户</a></p>
        <p>或复制以下链接到浏览器地址栏：</p>
        <p>{activation_url}</p>
        <p>此链接将在24小时后失效。</p>
        <p>如果您没有注册此账户，请忽略此邮件。</p>
        <br>
        <p>祝好，<br>{tenant_name} 团队</p>
    </body>
    </html>
    """
    
    # 密码重置邮件模板
    PASSWORD_RESET = """
    <html>
    <body>
        <h2>密码重置请求</h2>
        <p>亲爱的 {username}，</p>
        <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
        <p><a href="{reset_url}" style="background-color: #f44336; color: white; padding: 14px 20px; text-decoration: none; display: inline-block;">重置密码</a></p>
        <p>或复制以下链接到浏览器地址栏：</p>
        <p>{reset_url}</p>
        <p>此链接将在30分钟后失效。</p>
        <p>如果您没有请求重置密码，请忽略此邮件。</p>
        <br>
        <p>祝好，<br>{tenant_name} 团队</p>
    </body>
    </html>
    """
    
    # 邮箱验证码模板
    EMAIL_VERIFICATION_CODE = """
    <html>
    <body>
        <h2>邮箱验证码</h2>
        <p>亲爱的 {username}，</p>
        <p>您的邮箱验证码是：</p>
        <h1 style="color: #4CAF50; font-size: 32px; letter-spacing: 5px;">{verification_code}</h1>
        <p>此验证码将在5分钟后失效。</p>
        <p>如果您没有请求此验证码，请忽略此邮件。</p>
        <br>
        <p>祝好，<br>{tenant_name} 团队</p>
    </body>
    </html>
    """
    
    # MFA设置邮件模板
    MFA_SETUP = """
    <html>
    <body>
        <h2>多因子认证设置</h2>
        <p>亲爱的 {username}，</p>
        <p>您已成功启用多因子认证。您的账户安全性已得到提升。</p>
        <p>备用恢复码（请妥善保存）：</p>
        <ul>
        {backup_codes}
        </ul>
        <p>如果您丢失了认证设备，可以使用这些恢复码登录。</p>
        <p>如果这不是您的操作，请立即联系管理员。</p>
        <br>
        <p>祝好，<br>{tenant_name} 团队</p>
    </body>
    </html>
    """


class EmailService:
    """邮件服务类"""
    
    def __init__(
        self,
        smtp_server: str,
        smtp_port: int,
        username: str,
        password: str,
        use_tls: bool = True,
        sender_name: str = "IAM Service",
        max_workers: int = 5
    ):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.use_tls = use_tls
        self.sender_name = sender_name
        self.sender_email = username
        
        # 线程池用于异步发送邮件
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    async def send_user_activation_email(
        self,
        to_email: str,
        username: str,
        tenant_name: str,
        activation_token: str,
        base_url: str = "https://your-domain.com"
    ) -> bool:
        """发送用户激活邮件"""
        activation_url = f"{base_url}/activate?token={activation_token}"
        
        content = EmailTemplate.USER_ACTIVATION.format(
            username=username,
            tenant_name=tenant_name,
            activation_url=activation_url
        )
        
        return await self.send_email(
            to_email=to_email,
            subject=f"激活您在 {tenant_name} 的账户",
            html_content=content
        )

    async def send_password_reset_email(
        self,
        to_email: str,
        username: str,
        tenant_name: str,
        reset_token: str,
        base_url: str = "https://your-domain.com"
    ) -> bool:
        """发送密码重置邮件"""
        reset_url = f"{base_url}/reset-password?token={reset_token}"
        
        content = EmailTemplate.PASSWORD_RESET.format(
            username=username,
            tenant_name=tenant_name,
            reset_url=reset_url
        )
        
        return await self.send_email(
            to_email=to_email,
            subject=f"重置您在 {tenant_name} 的密码",
            html_content=content
        )

    async def send_verification_code_email(
        self,
        to_email: str,
        username: str,
        tenant_name: str,
        verification_code: str
    ) -> bool:
        """发送邮箱验证码"""
        content = EmailTemplate.EMAIL_VERIFICATION_CODE.format(
            username=username,
            tenant_name=tenant_name,
            verification_code=verification_code
        )
        
        return await self.send_email(
            to_email=to_email,
            subject=f"{tenant_name} 邮箱验证码",
            html_content=content
        )

    async def send_mfa_setup_email(
        self,
        to_email: str,
        username: str,
        tenant_name: str,
        backup_codes: List[str]
    ) -> bool:
        """发送MFA设置确认邮件"""
        backup_codes_html = "\n".join([f"<li>{code}</li>" for code in backup_codes])
        
        content = EmailTemplate.MFA_SETUP.format(
            username=username,
            tenant_name=tenant_name,
            backup_codes=backup_codes_html
        )
        
        return await self.send_email(
            to_email=to_email,
            subject=f"{tenant_name} 多因子认证已启用",
            html_content=content
        )

    async def send_email(
        self,
        to_email: str,
        subject: str,
        text_content: Optional[str] = None,
        html_content: Optional[str] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """
        发送邮件
        
        支持文本和HTML内容，以及附件
        """
        try:
            # 在线程池中执行邮件发送
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._send_email_sync,
                to_email,
                subject,
                text_content,
                html_content,
                attachments
            )
            return result
        except Exception as e:
            print(f"发送邮件失败: {str(e)}")
            return False

    def _send_email_sync(
        self,
        to_email: str,
        subject: str,
        text_content: Optional[str] = None,
        html_content: Optional[str] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """同步发送邮件"""
        try:
            # 创建邮件消息
            msg = MIMEMultipart('alternative')
            msg['From'] = f"{self.sender_name} <{self.sender_email}>"
            msg['To'] = to_email
            msg['Subject'] = subject
            msg['Date'] = datetime.now().strftime("%a, %d %b %Y %H:%M:%S %z")

            # 添加文本内容
            if text_content:
                text_part = MIMEText(text_content, 'plain', 'utf-8')
                msg.attach(text_part)

            # 添加HTML内容
            if html_content:
                html_part = MIMEText(html_content, 'html', 'utf-8')
                msg.attach(html_part)

            # 添加附件
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)

            # 发送邮件
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls(context=context)
                server.login(self.username, self.password)
                server.send_message(msg)

            print(f"邮件发送成功: {to_email} - {subject}")
            return True

        except Exception as e:
            print(f"邮件发送失败: {str(e)}")
            return False

    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict[str, Any]):
        """添加附件到邮件"""
        try:
            filename = attachment['filename']
            content = attachment['content']
            content_type = attachment.get('content_type', 'application/octet-stream')

            part = MIMEBase(*content_type.split('/'))
            part.set_payload(content)
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            msg.attach(part)
        except Exception as e:
            print(f"添加附件失败: {str(e)}")

    async def test_connection(self) -> bool:
        """测试邮件服务连接"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._test_connection_sync
            )
            return result
        except Exception:
            return False

    def _test_connection_sync(self) -> bool:
        """同步测试连接"""
        try:
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls(context=context)
                server.login(self.username, self.password)
            return True
        except Exception:
            return False

    def close(self):
        """关闭邮件服务"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
