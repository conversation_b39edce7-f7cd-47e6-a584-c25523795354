/**
 * @file 登录表单组件 V2.0
 * @description 重构后的登录表单，使用React Hook Form + Zod验证，支持新的UI设计
 */

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import clsx from 'clsx'
import { Button, Input } from '@/components/ui'
import { SocialLogin } from '@/components/auth'
import { LoginRequest, TenantInfo } from '@/types'
import { useAuth } from '@/stores/authStore'

// Zod验证模式
const loginSchema = z.object({
  tenant_id: z.string().min(1, '请选择租户'),
  identifier: z.string().email('请输入有效的邮箱地址'),
  credential: z.string().min(6, '密码至少需要6个字符'),
  remember_me: z.boolean().optional()
})

type LoginFormData = z.infer<typeof loginSchema>

export interface LoginFormProps {
  tenant?: TenantInfo
  onSuccess?: () => void
  onError?: (error: string) => void
  onSwitchToRegister?: () => void
  onForgotPassword?: () => void
  className?: string
  showSocialLogin?: boolean
  showRememberMe?: boolean
}

export const LoginForm: React.FC<LoginFormProps> = ({
  tenant,
  onSuccess,
  onError,
  onSwitchToRegister,
  onForgotPassword,
  className,
  showSocialLogin = true,
  showRememberMe = true
}) => {
  const { login, isLoading, error, clearError } = useAuth()

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    clearErrors
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      tenant_id: tenant?.id || '',
      identifier: '',
      credential: '',
      remember_me: false
    }
  })

  // 监听表单变化以清除错误
  const watchedFields = watch()
  React.useEffect(() => {
    if (error) {
      clearError()
    }
  }, [watchedFields, error, clearError])

  // 如果有租户信息，自动填充租户ID
  React.useEffect(() => {
    if (tenant?.id) {
      setValue('tenant_id', tenant.id)
      clearErrors('tenant_id')
    }
  }, [tenant, setValue, clearErrors])

  const onSubmit = async (data: LoginFormData) => {
    try {
      const loginRequest: LoginRequest = {
        tenant_id: data.tenant_id,
        login_type: 'email',
        identifier: data.identifier,
        credential: data.credential,
        remember_me: data.remember_me
      }

      await login(loginRequest)
      onSuccess?.()
    } catch (err: any) {
      const errorMessage = err.message || '登录失败，请重试'
      onError?.(errorMessage)
    }
  }

  const handleSocialLogin = (providerId: string) => {
    // TODO: 实现社交登录逻辑
    console.log('Social login with:', providerId)
  }

  return (
    <div className={clsx('space-y-6', className)}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* 租户选择 - 如果没有预设租户 */}
        {!tenant && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Input
              label="租户"
              placeholder="请选择或输入租户"
              error={errors.tenant_id?.message}
              disabled={isLoading || isSubmitting}
              {...register('tenant_id')}
            />
          </motion.div>
        )}

        {/* 邮箱输入 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Input
            label="邮箱"
            type="email"
            placeholder="请输入您的邮箱地址"
            error={errors.identifier?.message}
            disabled={isLoading || isSubmitting}
            {...register('identifier')}
          />
        </motion.div>

        {/* 密码输入 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Input
            label="密码"
            type="password"
            placeholder="请输入您的密码"
            error={errors.credential?.message}
            disabled={isLoading || isSubmitting}
            {...register('credential')}
          />
        </motion.div>

        {/* 记住我和忘记密码 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="flex items-center justify-between"
        >
          {showRememberMe && (
            <label className="flex items-center">
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                disabled={isLoading || isSubmitting}
                {...register('remember_me')}
              />
              <span className="ml-2 text-sm text-gray-600">记住我</span>
            </label>
          )}
          
          <button
            type="button"
            onClick={onForgotPassword}
            className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
            disabled={isLoading || isSubmitting}
          >
            忘记密码？
          </button>
        </motion.div>

        {/* 全局错误信息 */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
            className="bg-red-50 border border-red-200 rounded-lg p-3"
          >
            <div className="flex items-center">
              <svg className="h-5 w-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </motion.div>
        )}

        {/* 登录按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isLoading || isSubmitting}
            disabled={isLoading || isSubmitting}
            className="w-full"
          >
            {isLoading || isSubmitting ? '登录中...' : '登录'}
          </Button>
        </motion.div>
      </form>

      {/* 社交登录 */}
      {showSocialLogin && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <SocialLogin
            onProviderClick={handleSocialLogin}
            disabled={isLoading || isSubmitting}
            loading={isLoading || isSubmitting}
          />
        </motion.div>
      )}

      {/* 注册链接 */}
      {onSwitchToRegister && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.6 }}
          className="text-center"
        >
          <p className="text-sm text-gray-600">
            还没有账户？
            <button
              type="button"
              onClick={onSwitchToRegister}
              className="ml-1 text-blue-600 hover:text-blue-500 font-medium transition-colors"
              disabled={isLoading || isSubmitting}
            >
              立即注册
            </button>
          </p>
        </motion.div>
      )}
    </div>
  )
}
