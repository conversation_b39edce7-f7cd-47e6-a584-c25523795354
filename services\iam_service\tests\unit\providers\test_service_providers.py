"""
Service Provider测试

测试业务服务相关的providers
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from dependency_injector import providers
from container import ServiceContainer
from services.tenant_service import TenantService
from services.user_service import UserService
from services.auth_service import AuthService
from services.role_service import RoleService
from security.jwt_manager import J<PERSON><PERSON>anager
from security.session_manager import Session<PERSON>anager
from security.security_utils import SecurityUtils


class TestServiceProviders:
    """Service Provider测试类"""

    @pytest.mark.unit
    def test_tenant_service_provider(self):
        """测试租户服务provider"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        
        # 测试租户服务创建
        with patch.object(TenantService, '__init__', return_value=None) as mock_init:
            tenant_service = container.tenant_service()
            
            # 验证TenantService被正确初始化
            mock_init.assert_called_once()
            call_kwargs = mock_init.call_args[1]
            assert 'session' in call_kwargs
            assert 'redis_repo' in call_kwargs
            assert 'user_model' in call_kwargs
            assert 'tenant_model' in call_kwargs

    @pytest.mark.unit
    def test_user_service_provider(self):
        """测试用户服务provider"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_security_utils = MagicMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        container.security_utils.override(mock_security_utils)
        
        # 测试用户服务创建
        with patch.object(UserService, '__init__', return_value=None) as mock_init:
            user_service = container.user_service()
            
            # 验证UserService被正确初始化
            mock_init.assert_called_once()
            call_kwargs = mock_init.call_args[1]
            assert 'session' in call_kwargs
            assert 'redis_repo' in call_kwargs
            assert 'security_utils' in call_kwargs

    @pytest.mark.unit
    def test_auth_service_provider(self):
        """测试认证服务provider"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_jwt_manager = MagicMock()
        mock_session_manager = AsyncMock()
        mock_security_utils = MagicMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        container.jwt_manager.override(mock_jwt_manager)
        container.session_manager.override(mock_session_manager)
        container.security_utils.override(mock_security_utils)
        
        # 测试认证服务创建
        with patch.object(AuthService, '__init__', return_value=None) as mock_init:
            auth_service = container.auth_service()
            
            # 验证AuthService被正确初始化
            mock_init.assert_called_once()
            call_kwargs = mock_init.call_args[1]
            assert 'session' in call_kwargs
            assert 'redis_repo' in call_kwargs
            assert 'jwt_manager' in call_kwargs
            assert 'session_manager' in call_kwargs
            assert 'security_utils' in call_kwargs

    @pytest.mark.unit
    def test_role_service_provider(self):
        """测试角色服务provider"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        
        # 测试角色服务创建
        with patch.object(RoleService, '__init__', return_value=None) as mock_init:
            role_service = container.role_service()
            
            # 验证RoleService被正确初始化
            mock_init.assert_called_once()
            call_kwargs = mock_init.call_args[1]
            assert 'session' in call_kwargs
            assert 'redis_repo' in call_kwargs
            assert 'role_model' in call_kwargs

    @pytest.mark.unit
    def test_jwt_manager_provider(self):
        """测试JWT管理器provider"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.redis_repo.override(mock_redis_repo)
        
        # Mock安全配置
        with patch('container.security_config_manager') as mock_security_config:
            mock_security_config.jwt_config.private_key = "test_private_key"
            mock_security_config.jwt_config.public_key = "test_public_key"
            mock_security_config.jwt_config.access_token_expire_minutes = 30
            mock_security_config.jwt_config.refresh_token_expire_days = 7
            mock_security_config.jwt_config.algorithm = "RS256"
            
            with patch.object(JWTManager, '__init__', return_value=None) as mock_init:
                jwt_manager = container.jwt_manager()
                
                # 验证JWTManager被正确初始化
                mock_init.assert_called_once()
                call_kwargs = mock_init.call_args[1]
                assert 'redis_repo' in call_kwargs
                assert 'private_key' in call_kwargs
                assert 'public_key' in call_kwargs
                assert 'access_token_expire_minutes' in call_kwargs
                assert 'refresh_token_expire_days' in call_kwargs
                assert 'algorithm' in call_kwargs

    @pytest.mark.unit
    def test_session_manager_provider(self):
        """测试会话管理器provider"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.redis_repo.override(mock_redis_repo)
        
        # Mock安全配置
        with patch('container.security_config_manager') as mock_security_config:
            mock_security_config.session_config.session_timeout_minutes = 60
            mock_security_config.session_config.max_concurrent_sessions = 5
            mock_security_config.session_config.enable_device_tracking = True
            
            with patch.object(SessionManager, '__init__', return_value=None) as mock_init:
                session_manager = container.session_manager()
                
                # 验证SessionManager被正确初始化
                mock_init.assert_called_once()
                call_kwargs = mock_init.call_args[1]
                assert 'redis_repo' in call_kwargs
                assert 'session_timeout_minutes' in call_kwargs
                assert 'max_concurrent_sessions' in call_kwargs
                assert 'enable_device_tracking' in call_kwargs

    @pytest.mark.unit
    def test_security_utils_provider(self):
        """测试安全工具provider"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 测试安全工具创建
        with patch.object(SecurityUtils, '__init__', return_value=None) as mock_init:
            security_utils = container.security_utils()
            
            # 验证SecurityUtils被正确初始化
            mock_init.assert_called_once()

    @pytest.mark.unit
    def test_service_provider_types(self):
        """测试服务provider类型"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证所有业务服务都是Factory类型
        assert isinstance(container.tenant_service, providers.Factory)
        assert isinstance(container.user_service, providers.Factory)
        assert isinstance(container.auth_service, providers.Factory)
        assert isinstance(container.role_service, providers.Factory)
        assert isinstance(container.permission_service, providers.Factory)
        assert isinstance(container.rbac_service, providers.Factory)
        assert isinstance(container.audit_service, providers.Factory)
        
        # 验证安全组件都是Factory类型
        assert isinstance(container.jwt_manager, providers.Factory)
        assert isinstance(container.session_manager, providers.Factory)
        assert isinstance(container.security_utils, providers.Factory)

    @pytest.mark.unit
    def test_external_service_providers(self):
        """测试外部服务providers"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证外部服务provider类型
        assert isinstance(container.sms_provider, providers.Singleton)
        assert isinstance(container.sms_service, providers.Singleton)
        assert isinstance(container.email_service, providers.Singleton)
        assert isinstance(container.verification_service, providers.Singleton)

    @pytest.mark.unit
    def test_service_dependency_injection(self):
        """测试服务依赖注入"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock所有依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_jwt_manager = MagicMock()
        mock_session_manager = AsyncMock()
        mock_security_utils = MagicMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        container.jwt_manager.override(mock_jwt_manager)
        container.session_manager.override(mock_session_manager)
        container.security_utils.override(mock_security_utils)
        
        # 测试认证服务的复杂依赖注入
        with patch.object(AuthService, '__init__', return_value=None) as mock_init:
            auth_service = container.auth_service()
            
            # 验证所有依赖都被正确注入
            call_kwargs = mock_init.call_args[1]
            assert call_kwargs['session'] is mock_session
            assert call_kwargs['redis_repo'] is mock_redis_repo
            assert call_kwargs['jwt_manager'] is mock_jwt_manager
            assert call_kwargs['session_manager'] is mock_session_manager
            assert call_kwargs['security_utils'] is mock_security_utils

    @pytest.mark.unit
    def test_service_model_injection(self):
        """测试服务模型注入"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        
        # 测试租户服务的模型注入
        with patch.object(TenantService, '__init__', return_value=None) as mock_init:
            tenant_service = container.tenant_service()
            
            # 验证模型被正确注入
            call_kwargs = mock_init.call_args[1]
            assert 'user_model' in call_kwargs
            assert 'tenant_model' in call_kwargs
            assert 'role_model' in call_kwargs
            assert 'permission_model' in call_kwargs
            assert 'user_role_model' in call_kwargs
            assert 'role_permission_model' in call_kwargs
            assert 'audit_log_model' in call_kwargs

    @pytest.mark.unit
    def test_service_provider_error_handling(self):
        """测试服务provider错误处理"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock会话抛出异常
        container.session.override(Exception("Database connection failed"))
        
        # 测试服务创建时的异常处理
        with pytest.raises(Exception):
            container.tenant_service()

    @pytest.mark.unit
    def test_service_provider_with_none_dependencies(self):
        """测试服务provider处理None依赖"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖为None
        container.session.override(None)
        container.redis_repo.override(None)
        
        # 测试服务创建处理None依赖
        with patch.object(TenantService, '__init__', return_value=None) as mock_init:
            tenant_service = container.tenant_service()
            
            # 验证服务仍然被初始化，但使用None依赖
            call_kwargs = mock_init.call_args[1]
            assert call_kwargs['session'] is None
            assert call_kwargs['redis_repo'] is None
