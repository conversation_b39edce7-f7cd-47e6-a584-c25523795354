"""
高级安全服务使用示例

演示如何使用高级安全服务进行MFA、安全策略、威胁检测等功能
"""

import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from services.iam_service.services.advanced_security_service import AdvancedSecurityService
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import (
    User, Tenant, UserMFA, SecurityPolicy, SecurityEvent, AuditLog
)


async def create_security_service():
    """创建高级安全服务实例"""
    # 创建数据库引擎和会话
    engine = create_async_engine("sqlite+aiosqlite:///example.db")
    async_session = sessionmaker(engine, class_=AsyncSession)
    session = async_session()
    
    # 创建Redis仓库
    redis_repo = RedisRepository(host="localhost", port=6379, db=0)
    
    # 创建高级安全服务
    security_service = AdvancedSecurityService(
        session=session,
        redis_repo=redis_repo,
        user_model=User,
        tenant_model=Tenant,
        user_mfa_model=UserMFA,
        security_policy_model=SecurityPolicy,
        security_event_model=SecurityEvent,
        audit_log_model=AuditLog
    )
    
    return security_service, session


async def demo_mfa_management():
    """演示MFA管理功能"""
    security_service, session = await create_security_service()
    
    try:
        print("=== MFA管理演示 ===")
        
        # 1. 设置TOTP MFA
        print("\n1. 设置TOTP MFA")
        setup_result = await security_service.setup_mfa(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            mfa_type="totp"
        )
        print(f"设置令牌: {setup_result['setup_token']}")
        print(f"密钥: {setup_result['secret_key']}")
        print(f"备用恢复码数量: {len(setup_result['backup_codes'])}")
        print(f"过期时间: {setup_result['expires_at']}")
        
        # 2. 验证MFA设置
        print("\n2. 验证MFA设置")
        # 注意：在实际应用中，用户需要使用TOTP应用生成验证码
        # 这里使用模拟验证码进行演示
        verify_result = await security_service.verify_mfa_setup(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            setup_token=setup_result['setup_token'],
            verification_code="123456"  # 模拟验证码
        )
        print(f"MFA启用状态: {verify_result['mfa_enabled']}")
        print(f"MFA类型: {verify_result['mfa_type']}")
        print(f"启用时间: {verify_result['enabled_at']}")
        
        # 3. 生成新的备用恢复码
        print("\n3. 生成备用恢复码")
        backup_result = await security_service.generate_backup_codes(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            count=8
        )
        print(f"生成的备用恢复码:")
        for i, code in enumerate(backup_result['backup_codes'], 1):
            print(f"  {i}. {code}")
        
        # 4. 设置SMS MFA
        print("\n4. 设置SMS MFA")
        sms_setup_result = await security_service.setup_mfa(
            tenant_id="tenant_demo",
            user_id="user_demo_456",
            mfa_type="sms",
            phone="13800138000"
        )
        print(f"SMS MFA设置令牌: {sms_setup_result['setup_token']}")
        
        # 5. 禁用MFA
        print("\n5. 禁用MFA")
        disable_result = await security_service.disable_mfa(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            verification_code="123456",  # 模拟当前MFA验证码
            reason="用户请求禁用"
        )
        print(f"MFA禁用状态: {not disable_result['mfa_enabled']}")
        print(f"禁用时间: {disable_result['disabled_at']}")
        print(f"禁用原因: {disable_result['reason']}")
        
    except Exception as e:
        print(f"MFA管理演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_security_policy():
    """演示安全策略功能"""
    security_service, session = await create_security_service()
    
    try:
        print("\n=== 安全策略演示 ===")
        
        # 1. 设置密码策略
        print("\n1. 设置密码策略")
        password_policy = await security_service.set_security_policy(
            tenant_id="tenant_demo",
            policy_name="password_policy",
            policy_config={
                "min_length": 8,
                "require_uppercase": True,
                "require_lowercase": True,
                "require_numbers": True,
                "require_symbols": False,
                "max_age_days": 90,
                "history_count": 5
            },
            enabled=True
        )
        print(f"密码策略ID: {password_policy['policy_id']}")
        print(f"策略配置: {password_policy['policy_config']}")
        
        # 2. 设置会话策略
        print("\n2. 设置会话策略")
        session_policy = await security_service.set_security_policy(
            tenant_id="tenant_demo",
            policy_name="session_policy",
            policy_config={
                "max_session_duration": 7200,  # 2小时
                "idle_timeout": 1800,  # 30分钟
                "max_concurrent_sessions": 3,
                "require_mfa_for_sensitive": True
            },
            enabled=True
        )
        print(f"会话策略ID: {session_policy['policy_id']}")
        print(f"最大会话时长: {session_policy['policy_config']['max_session_duration']}秒")
        
        # 3. 设置登录策略
        print("\n3. 设置登录策略")
        login_policy = await security_service.set_security_policy(
            tenant_id="tenant_demo",
            policy_name="login_policy",
            policy_config={
                "max_failed_attempts": 5,
                "lockout_duration": 900,  # 15分钟
                "require_captcha_after": 3,
                "allowed_ip_ranges": ["***********/24", "10.0.0.0/8"]
            },
            enabled=True
        )
        print(f"登录策略ID: {login_policy['policy_id']}")
        print(f"最大失败尝试次数: {login_policy['policy_config']['max_failed_attempts']}")
        
        # 4. 设置全局策略
        print("\n4. 设置全局策略")
        global_policy = await security_service.set_security_policy(
            tenant_id=None,  # 全局策略
            policy_name="global_security_policy",
            policy_config={
                "enable_audit_log": True,
                "enable_threat_detection": True,
                "data_retention_days": 365,
                "encryption_algorithm": "AES-256"
            },
            enabled=True
        )
        print(f"全局策略ID: {global_policy['policy_id']}")
        print(f"数据保留天数: {global_policy['policy_config']['data_retention_days']}")
        
    except Exception as e:
        print(f"安全策略演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_security_events():
    """演示安全事件管理功能"""
    security_service, session = await create_security_service()
    
    try:
        print("\n=== 安全事件管理演示 ===")
        
        # 1. 创建模拟安全事件
        print("\n1. 创建安全事件")
        await security_service._create_security_event(
            tenant_id="tenant_demo",
            event_type="login_anomaly",
            severity="high",
            title="异常登录检测",
            description="检测到来自异常地理位置的登录尝试",
            user_id="user_demo_123",
            ip_address="***********",
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
            details={
                "location": "Unknown Location",
                "previous_location": "Beijing, China",
                "risk_score": 85,
                "detection_rules": ["geo_anomaly", "device_fingerprint"]
            }
        )
        print("创建了异常登录事件")
        
        await security_service._create_security_event(
            tenant_id="tenant_demo",
            event_type="brute_force",
            severity="critical",
            title="暴力破解攻击",
            description="检测到针对用户账户的暴力破解攻击",
            user_id="user_demo_456",
            ip_address="************",
            details={
                "failed_attempts": 15,
                "time_window": "5 minutes",
                "attack_pattern": "dictionary_attack"
            }
        )
        print("创建了暴力破解事件")
        
        # 2. 查询安全事件
        print("\n2. 查询安全事件")
        events_result = await security_service.query_security_events(
            tenant_id="tenant_demo",
            severity="high",
            page=1,
            page_size=10
        )
        
        print(f"查询到 {events_result['total']} 个高危安全事件")
        for event in events_result['events']:
            print(f"  - {event['title']} ({event['event_type']}) - {event['severity']}")
            print(f"    时间: {event['created_at']}")
            print(f"    状态: {event['status']}")
        
        # 3. 按事件类型查询
        print("\n3. 按事件类型查询")
        login_events = await security_service.query_security_events(
            tenant_id="tenant_demo",
            event_type="login_anomaly",
            page=1,
            page_size=5
        )
        print(f"登录异常事件数量: {login_events['total']}")
        
        # 4. 更新安全事件状态
        print("\n4. 更新安全事件状态")
        if events_result['events']:
            event_id = events_result['events'][0]['event_id']
            update_result = await security_service.update_security_event(
                tenant_id="tenant_demo",
                event_id=event_id,
                status="investigating",
                notes="安全团队正在调查此事件",
                assigned_to="admin_user_123"
            )
            print(f"事件 {event_id} 状态已更新为: {update_result['status']}")
            print(f"更新时间: {update_result['updated_at']}")
        
        # 5. 按时间范围查询
        print("\n5. 按时间范围查询")
        start_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = datetime.now()
        
        time_range_events = await security_service.query_security_events(
            tenant_id="tenant_demo",
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            page=1,
            page_size=20
        )
        print(f"今日安全事件数量: {time_range_events['total']}")
        
        # 按严重程度统计
        severity_stats = {}
        for event in time_range_events['events']:
            severity = event['severity']
            severity_stats[severity] = severity_stats.get(severity, 0) + 1
        
        print("严重程度统计:")
        for severity, count in severity_stats.items():
            print(f"  {severity}: {count} 个")
        
    except Exception as e:
        print(f"安全事件管理演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_security_tools():
    """演示安全工具功能"""
    security_service, session = await create_security_service()
    
    try:
        print("\n=== 安全工具演示 ===")
        
        # 1. TOTP工具演示
        print("\n1. TOTP工具演示")
        secret = security_service._generate_totp_secret()
        print(f"生成的TOTP密钥: {secret}")
        
        qr_url = security_service._generate_totp_qr_url(
            secret, "<EMAIL>", "Demo IAM"
        )
        print(f"二维码URL长度: {len(qr_url)} 字符")
        print(f"二维码格式: {'data:image/png;base64,' in qr_url}")
        
        # 2. 备用恢复码生成演示
        print("\n2. 备用恢复码生成演示")
        backup_codes = [security_service._generate_backup_code() for _ in range(5)]
        print("生成的备用恢复码:")
        for i, code in enumerate(backup_codes, 1):
            print(f"  {i}. {code}")
            # 验证格式
            assert len(code) == 9, "恢复码长度应为9"
            assert code[4] == "-", "恢复码应包含连字符"
        
        # 3. TOTP验证演示
        print("\n3. TOTP验证演示")
        # 注意：在实际应用中需要使用真实的TOTP应用生成验证码
        # 这里只演示验证逻辑
        test_token = "123456"
        is_valid = security_service._verify_totp(secret, test_token)
        print(f"验证码 {test_token} 验证结果: {is_valid}")
        
        print("\n安全工具演示完成！")
        
    except Exception as e:
        print(f"安全工具演示出错: {e}")
    finally:
        await session.close()


async def main():
    """主函数"""
    print("高级安全服务使用示例")
    print("=" * 50)
    
    # 运行各个演示
    await demo_mfa_management()
    await demo_security_policy()
    await demo_security_events()
    await demo_security_tools()
    
    print("\n演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
