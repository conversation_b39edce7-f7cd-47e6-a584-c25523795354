/**
 * @file 类型定义统一导出
 * @description 导出所有类型定义，提供统一的类型导入入口
 */

// 认证相关类型
export type {
  LoginType,
  AuthStatus,
  FormStatus,
  PasswordStrength,
  AuthFormMode,
  AuthLayoutMode,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  UserInfo,
  TenantInfo,
  TenantTheme,
  TenantSettings,
  PasswordPolicy,
  FormValidationError,
  AuthError,
  PasswordStrengthResult,
  PasswordRequirement,
  FieldState,
  AuthFormState,
  TenantDetectionResult,
  SessionInfo,
  DeviceInfo,
  AuthEvent
} from './auth'

// 主题相关类型
export type {
  ThemeMode,
  ThemeVariant,
  ColorMode,
  ThemeConfig,
  ThemeColors,
  ColorPalette,
  ThemeTypography,
  ThemeSpacing,
  ThemeAnimations,
  ThemeBreakpoints,
  TenantThemeConfig,
  ThemeState,
  ThemeContextValue,
  CSSVariables,
  ThemeProviderProps,
  ThemeSwitcherProps,
  ThemePreview
} from './theme'

// 租户相关类型
export type {
  TenantStatus,
  TenantType,
  TenantPlan,
  TenantConfig,
  TenantLogo,
  TenantBranding,
  TenantSettings as TenantSettingsExtended,
  TenantFeatures,
  TenantLimits,
  TenantContact,
  TenantAddress,
  TenantStats,
  TenantOption,
  TenantDetectionConfig,
  TenantSwitchEvent,
  TenantValidationResult,
  TenantSearchResult,
  TenantInvitation,
  TenantState,
  TenantContextValue,
  NotificationPreferences
} from './tenant'

// 通用类型
export type {
  ApiResponse,
  ApiError,
  ResponseMeta,
  PaginationRequest,
  PaginationResponse,
  SearchRequest,
  Option,
  SelectOption,
  MenuItem,
  NavItem,
  FieldConfig,
  ValidationRule,
  FormConfig,
  LoadingState,
  ErrorState,
  AsyncState,
  ModalConfig,
  NotificationConfig,
  ConfirmConfig,
  UploadConfig,
  FileInfo,
  DeviceInfo as CommonDeviceInfo,
  LocationInfo,
  KeyValuePair,
  TimeRange,
  ColorConfig,
  SizeConfig,
  SpacingConfig,
  BaseComponentProps,
  ClickableProps,
  FocusableProps,
  FormControlProps,
  EnvConfig
} from './common'

// 重新导出常用的React类型
export type {
  FC,
  ReactNode,
  ReactElement,
  ComponentProps,
  ComponentType,
  CSSProperties,
  MouseEvent,
  ChangeEvent,
  FocusEvent,
  KeyboardEvent,
  FormEvent
} from 'react'
