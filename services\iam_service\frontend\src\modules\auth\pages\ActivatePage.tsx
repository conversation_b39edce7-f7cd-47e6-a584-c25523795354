/**
 * @file 账户激活页面
 * @description 通过激活令牌激活用户账户
 * @status 开发中
 */

import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Button } from '@/components/ui/Button'
import { PageLoading } from '@/components/common/LoadingSpinner'
import * as authApi from '@/api/auth'
import { ActivateUserResponse } from '@/api/types'

export const ActivatePage: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [isLoading, setIsLoading] = useState(true)
  const [activationResult, setActivationResult] = useState<ActivateUserResponse | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const activateAccount = async () => {
      const token = searchParams.get('token')
      
      if (!token) {
        setError('激活令牌无效或缺失')
        setIsLoading(false)
        return
      }

      try {
        const response = await authApi.activateUser({ activation_token: token })
        setActivationResult(response)
      } catch (err: any) {
        setError(err.message || '账户激活失败')
      } finally {
        setIsLoading(false)
      }
    }

    activateAccount()
  }, [searchParams])

  // 加载中状态
  if (isLoading) {
    return <PageLoading text="正在激活账户..." />
  }

  // 激活成功
  if (activationResult) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        {/* 页面头部 */}
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="flex justify-center">
            {/* 成功图标 */}
            <div className="h-16 w-16 bg-green-600 rounded-full flex items-center justify-center">
              <svg
                className="h-10 w-10 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </div>
          
          <h1 className="mt-6 text-center text-3xl font-bold text-gray-900">
            账户激活成功！
          </h1>
          <p className="mt-2 text-center text-sm text-gray-600">
            您的账户已成功激活，现在可以登录使用了
          </p>
        </div>

        {/* 激活信息 */}
        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="text-center space-y-6">
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex">
                  <svg className="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">
                      激活完成
                    </h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p>您的账户已成功激活，可以正常使用所有功能。</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3 text-sm text-gray-600">
                <p>
                  <strong>用户ID：</strong>{activationResult.user_id}
                </p>
                <p>
                  <strong>激活时间：</strong>{new Date(activationResult.activated_at).toLocaleString()}
                </p>
                <p>
                  <strong>账户状态：</strong>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 ml-1">
                    已激活
                  </span>
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="text-sm text-blue-700">
                  <h4 className="font-medium mb-2">现在您可以：</h4>
                  <ul className="list-disc list-inside space-y-1 text-left">
                    <li>使用注册的邮箱和密码登录</li>
                    <li>访问系统的所有功能</li>
                    <li>管理您的个人资料和设置</li>
                    <li>享受完整的服务体验</li>
                  </ul>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => navigate('/login')}
                  className="w-full"
                >
                  立即登录
                </Button>
                
                <Button
                  variant="outline"
                  size="md"
                  onClick={() => navigate('/')}
                  className="w-full"
                >
                  返回首页
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 页面底部 */}
        <div className="mt-8 text-center">
          <div className="text-sm text-gray-500">
            <p>感谢您选择我们的服务！</p>
          </div>
        </div>
      </div>
    )
  }

  // 激活失败
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* 页面头部 */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          {/* 错误图标 */}
          <div className="h-16 w-16 bg-red-600 rounded-full flex items-center justify-center">
            <svg
              className="h-10 w-10 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
        </div>
        
        <h1 className="mt-6 text-center text-3xl font-bold text-gray-900">
          激活失败
        </h1>
        <p className="mt-2 text-center text-sm text-gray-600">
          账户激活过程中出现了问题
        </p>
      </div>

      {/* 错误信息 */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    激活失败
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="text-sm text-yellow-700">
                <h4 className="font-medium mb-2">可能的原因：</h4>
                <ul className="list-disc list-inside space-y-1 text-left">
                  <li>激活链接已过期</li>
                  <li>激活链接已被使用</li>
                  <li>激活链接格式不正确</li>
                  <li>账户已经被激活</li>
                </ul>
              </div>
            </div>

            <div className="space-y-3">
              <Button
                variant="primary"
                size="lg"
                onClick={() => navigate('/register')}
                className="w-full"
              >
                重新注册
              </Button>
              
              <Button
                variant="outline"
                size="md"
                onClick={() => navigate('/login')}
                className="w-full"
              >
                尝试登录
              </Button>
              
              <Button
                variant="ghost"
                size="md"
                onClick={() => navigate('/')}
                className="w-full"
              >
                返回首页
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 页面底部 */}
      <div className="mt-8 text-center">
        <div className="text-sm text-gray-500">
          <p>如需帮助，请联系系统管理员</p>
        </div>
      </div>
    </div>
  )
}
