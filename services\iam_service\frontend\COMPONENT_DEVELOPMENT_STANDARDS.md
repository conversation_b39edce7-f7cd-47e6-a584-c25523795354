
## 📋 目录
- [项目架构概述](#项目架构概述)
- [组件开发规范](#组件开发规范)
- [模块开发规范](#模块开发规范)
- [代码质量要求](#代码质量要求)
- [性能优化要求](#性能优化要求)
- [安全性要求](#安全性要求)
- [测试要求](#测试要求)
- [文档要求](#文档要求)

## 🏗️ 项目架构概述

### 技术栈
- **核心**: React 18 + TypeScript + Vite
- **路由**: React Router v6 (支持 future flags)
- **状态管理**: Zustand
- **样式**: Tailwind CSS + PostCSS
- **测试**: Vitest + Testing Library + MSW
- **开发工具**: ESLint + Prettier + Husky
- **PWA**: Vite PWA Plugin + Workbox

### 目录结构
```
src/
├── components/          # 组件库
│   ├── ui/             # 基础 UI 组件
│   ├── common/         # 通用组件
│   └── layout/         # 布局组件
├── modules/            # 功能模块
├── api/               # API 接口
├── hooks/             # 自定义 Hooks
├── stores/            # 状态管理
├── utils/             # 工具函数
├── mocks/             # Mock 数据
├── styles/            # 全局样式
└── test/              # 测试配置
```

## 🧩 组件开发规范

### 1. 组件分类和职责

#### 1.1 UI 组件 (`/components/ui/`)
**用途**: 基础可复用 UI 组件
**特征**:
- 无业务逻辑，纯展示组件
- 高度可复用
- 支持主题定制
- 完整的 TypeScript 类型定义

**示例**: Button, Input, Card, Modal

**开发要求**:
```typescript
// ✅ 正确示例
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(({ ... }) => {
  // 实现
})
```

**限制**:
- ❌ 禁止直接调用 API
- ❌ 禁止包含业务逻辑
- ❌ 禁止直接访问全局状态
- ✅ 必须使用 forwardRef
- ✅ 必须支持 className 覆盖
- ✅ 必须使用 cn() 工具函数合并样式

#### 1.2 通用组件 (`/components/common/`)
**用途**: 跨模块共享的业务组件
**特征**:
- 包含一定业务逻辑
- 可复用但有特定用途
- 可以调用 API 和状态

**示例**: ErrorBoundary, LoadingSpinner, UserAvatar

#### 1.3 布局组件 (`/components/layout/`)
**用途**: 页面布局和导航组件
**特征**:
- 负责页面整体结构
- 处理导航和路由
- 管理全局状态

**示例**: Layout, Header, Sidebar, Footer

### 2. 组件命名规范

```typescript
// ✅ 组件文件命名: PascalCase
Button.tsx
UserProfile.tsx
DataTable.tsx

// ✅ 组件导出命名
export const Button = ...
export const UserProfile = ...

// ✅ Props 接口命名
export interface ButtonProps { ... }
export interface UserProfileProps { ... }

// ✅ 组件内部状态和函数: camelCase
const [isLoading, setIsLoading] = useState(false)
const handleClick = () => { ... }
```

### 3. 组件文件结构

```typescript
/**
 * @file 组件描述
 * @description 详细说明组件功能和用途
 * @status 开发状态 - 完成/开发中/待开发
 */

import React, { forwardRef } from 'react'
import { cn } from '@/utils/common'

// 1. 类型定义
export interface ComponentProps {
  // props 定义
}

// 2. 组件实现
const Component = forwardRef<HTMLElement, ComponentProps>(
  ({ className, ...props }, ref) => {
    // 组件逻辑
    
    return (
      <div
        ref={ref}
        className={cn('base-styles', className)}
        {...props}
      >
        {/* JSX */}
      </div>
    )
  }
)

// 3. 显示名称
Component.displayName = 'Component'

// 4. 导出
export { Component }
```

### 4. 样式规范

#### 4.1 Tailwind CSS 使用规范
```typescript
// ✅ 使用 cn() 函数合并样式
import { cn } from '@/utils/common'

const Button = ({ className, variant = 'primary' }) => {
  return (
    <button
      className={cn(
        // 基础样式
        'inline-flex items-center justify-center rounded-md font-medium',
        // 变体样式
        {
          'bg-blue-600 text-white': variant === 'primary',
          'bg-gray-100 text-gray-900': variant === 'secondary',
        },
        // 外部传入样式
        className
      )}
    >
      {children}
    </button>
  )
}
```

#### 4.2 响应式设计
```typescript
// ✅ 移动优先的响应式设计
className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"

// ✅ 使用项目定义的断点
// sm: 640px, md: 768px, lg: 1024px, xl: 1280px
```

#### 4.3 主题色彩使用
```typescript
// ✅ 使用项目定义的主色调
className="bg-primary-600 text-white hover:bg-primary-700"

// ✅ 使用语义化颜色
className="text-gray-600"  // 次要文本
className="text-gray-900"  // 主要文本
className="bg-red-600"     // 错误状态
className="bg-green-600"   // 成功状态
```

## 🔧 模块开发规范

### 1. 模块结构
每个模块必须遵循以下结构：

```typescript
// modules/[module-name]/index.tsx
export const moduleConfig: ModuleConfig = {
  id: 'module-name',
  name: '模块显示名称',
  version: '1.0.0',
  description: '模块描述',
  enabled: true,
  lazy: false, // 是否懒加载
  dependencies: [], // 依赖的其他模块
  permissions: [], // 所需权限
}

export const routes: RouteObject[] = [
  {
    path: '/module-path',
    element: <ModuleComponent />,
  },
]

export const moduleDefinition: Module = {
  config: moduleConfig,
  component: ModuleComponent,
  routes,
  initialize: async () => {
    // 模块初始化逻辑
  },
  destroy: async () => {
    // 模块销毁逻辑
  },
}
```

### 2. 模块注册
```typescript
// App.tsx 中注册模块
import { moduleDefinition } from '@/modules/module-name'

useEffect(() => {
  moduleRegistry.register(moduleDefinition)
  moduleRegistry.load('module-name')
}, [])
```

### 3. 模块间通信
```typescript
// ✅ 使用 EventBus 进行模块间通信
import { useEventBus } from '@/utils/EventBus'

const { emit, on } = useEventBus()

// 发送事件
emit('user:login', { userId: '123' })

// 监听事件
on('user:login', (data) => {
  console.log('User logged in:', data)
})
```

## 📏 代码质量要求

### 1. TypeScript 使用规范

#### 1.1 类型定义
```typescript
// ✅ 严格的类型定义
interface User {
  id: string
  name: string
  email: string
  role: 'admin' | 'user' | 'guest'
  createdAt: Date
}

// ✅ 使用泛型
interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
}

// ❌ 避免使用 any
const data: any = response.data // 不推荐

// ✅ 使用具体类型
const data: User[] = response.data
```

#### 1.2 Props 类型继承
```typescript
// ✅ 继承原生 HTML 属性
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary'
  loading?: boolean
}

// ✅ 使用 Pick 和 Omit
interface CustomInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  size?: 'sm' | 'md' | 'lg'
}
```

### 2. 错误处理

#### 2.1 组件错误边界
```typescript
// ✅ 使用 ErrorBoundary 包装组件
<ErrorBoundary fallback={<ErrorFallback />}>
  <SomeComponent />
</ErrorBoundary>
```

#### 2.2 异步操作错误处理
```typescript
// ✅ 使用 try-catch 处理异步错误
const handleSubmit = async () => {
  try {
    setLoading(true)
    await api.submitData(data)
    toast.success('提交成功')
  } catch (error) {
    console.error('Submit failed:', error)
    toast.error('提交失败，请重试')
  } finally {
    setLoading(false)
  }
}
```

### 3. 性能优化

#### 3.1 组件优化
```typescript
// ✅ 使用 React.memo 优化纯组件
const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{/* 复杂渲染逻辑 */}</div>
})

// ✅ 使用 useMemo 缓存计算结果
const expensiveValue = useMemo(() => {
  return heavyCalculation(data)
}, [data])

// ✅ 使用 useCallback 缓存函数
const handleClick = useCallback(() => {
  onItemClick(item.id)
}, [item.id, onItemClick])
```

#### 3.2 懒加载
```typescript
// ✅ 使用 createLazyComponent 进行懒加载
const LazyComponent = createLazyComponent(
  () => import('./HeavyComponent'),
  <div>Loading...</div>
)
```

## 🔒 安全性要求

### 1. XSS 防护
```typescript
// ✅ 避免 dangerouslySetInnerHTML
// ❌ 不安全
<div dangerouslySetInnerHTML={{ __html: userInput }} />

// ✅ 安全的文本渲染
<div>{userInput}</div>

// ✅ 如必须使用 HTML，进行清理
import DOMPurify from 'dompurify'
<div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(htmlContent) }} />
```

### 2. 权限控制
```typescript
// ✅ 组件级权限检查
const ProtectedComponent = ({ requiredPermission, children }) => {
  const { hasPermission } = useAuth()
  
  if (!hasPermission(requiredPermission)) {
    return <div>无权限访问</div>
  }
  
  return <>{children}</>
}
```

## 🧪 测试要求

### 1. 单元测试
每个组件必须包含对应的测试文件：

```typescript
// Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from './Button'

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('shows loading state', () => {
    render(<Button loading>Click me</Button>)
    expect(screen.getByRole('button')).toBeDisabled()
  })
})
```

### 2. Storybook 文档
UI 组件必须包含 Storybook 故事：

```typescript
// Button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import { Button } from './Button'

const meta: Meta<typeof Button> = {
  title: 'UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Button',
  },
}

export const Loading: Story = {
  args: {
    loading: true,
    children: 'Loading...',
  },
}
```

## 📚 文档要求

### 1. 组件文档
每个组件文件必须包含详细的 JSDoc 注释：

```typescript
/**
 * @file 按钮组件
 * @description 可复用的按钮组件，支持多种变体和状态
 * @status 框架文件 - 完成
 * @example
 * ```tsx
 * <Button variant="primary" onClick={handleClick}>
 *   点击我
 * </Button>
 * ```
 */
```

### 2. README 文档
每个模块目录应包含 README.md 文件，说明：
- 模块功能和用途
- 安装和使用方法
- API 文档
- 示例代码

## 🚫 开发限制和禁止事项

### 1. 禁止的做法
- ❌ 直接修改 props
- ❌ 在组件内部使用 console.log（生产环境）
- ❌ 硬编码 API 地址
- ❌ 使用内联样式（除特殊情况）
- ❌ 忽略 TypeScript 类型检查
- ❌ 不处理错误状态
- ❌ 不考虑加载状态

### 2. 必须遵循的规则
- ✅ 所有组件必须有 TypeScript 类型定义
- ✅ 所有 UI 组件必须支持 ref 转发
- ✅ 所有异步操作必须有错误处理
- ✅ 所有用户交互必须有反馈
- ✅ 所有组件必须支持无障碍访问
- ✅ 所有代码必须通过 ESLint 检查
- ✅ 所有提交必须通过 Prettier 格式化

## 🔄 开发流程

### 1. 开发前检查
```bash
# 安装依赖
npm install

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 运行测试
npm run test
```

### 2. 开发中规范
- 遵循 Git 提交规范
- 及时编写测试
- 保持代码整洁
- 定期运行类型检查

### 3. 提交前检查
```bash
# 格式化代码
npm run format

# 修复 lint 问题
npm run lint:fix

# 运行完整测试
npm run test:coverage

# 构建检查
npm run build
```

## 🛠️ 实用工具和最佳实践

### 1. 常用 Hooks 使用指南

#### 1.1 useApi Hook
```typescript
// ✅ 使用项目提供的 useApi Hook
import { useApi } from '@/hooks/useApi'

const UserProfile = () => {
  const { data: user, loading, error, refetch } = useApi<User>('/api/users/me')

  if (loading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />
  if (!user) return <div>用户不存在</div>

  return <div>{user.name}</div>
}
```

#### 1.2 useLocalStorage Hook
```typescript
// ✅ 使用本地存储 Hook
import { useLocalStorage } from '@/hooks/useLocalStorage'

const Settings = () => {
  const [theme, setTheme] = useLocalStorage('theme', 'light')

  return (
    <select value={theme} onChange={(e) => setTheme(e.target.value)}>
      <option value="light">浅色</option>
      <option value="dark">深色</option>
    </select>
  )
}
```

### 2. 状态管理最佳实践

#### 2.1 Zustand Store 创建
```typescript
// stores/userStore.ts
import { create } from 'zustand'

interface UserState {
  user: User | null
  isAuthenticated: boolean
  login: (user: User) => void
  logout: () => void
}

export const useUserStore = create<UserState>((set) => ({
  user: null,
  isAuthenticated: false,
  login: (user) => set({ user, isAuthenticated: true }),
  logout: () => set({ user: null, isAuthenticated: false }),
}))
```

#### 2.2 Store 使用规范
```typescript
// ✅ 选择性订阅，避免不必要的重渲染
const user = useUserStore((state) => state.user)
const login = useUserStore((state) => state.login)

// ❌ 避免订阅整个 store
const store = useUserStore() // 不推荐
```

### 3. API 调用规范

#### 3.1 API Client 使用
```typescript
// ✅ 使用项目配置的 API 客户端
import { apiClient } from '@/api/client'

const fetchUsers = async (): Promise<User[]> => {
  try {
    const response = await apiClient.get<ApiResponse<User[]>>('/users')
    return response.data.data
  } catch (error) {
    console.error('Failed to fetch users:', error)
    throw error
  }
}
```

#### 3.2 错误处理模式
```typescript
// ✅ 统一的错误处理
const handleApiError = (error: unknown) => {
  if (error instanceof ApiError) {
    toast.error(error.message)
  } else {
    toast.error('操作失败，请重试')
    console.error('Unexpected error:', error)
  }
}
```

### 4. 表单处理规范

#### 4.1 受控组件模式
```typescript
const LoginForm = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleChange = (field: string) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }))
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validate = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.email) {
      newErrors.email = '邮箱不能为空'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = '邮箱格式不正确'
    }

    if (!formData.password) {
      newErrors.password = '密码不能为空'
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少6位'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validate()) return

    try {
      await login(formData)
      toast.success('登录成功')
    } catch (error) {
      handleApiError(error)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Input
        type="email"
        placeholder="邮箱"
        value={formData.email}
        onChange={handleChange('email')}
        error={errors.email}
      />
      <Input
        type="password"
        placeholder="密码"
        value={formData.password}
        onChange={handleChange('password')}
        error={errors.password}
      />
      <Button type="submit" className="w-full">
        登录
      </Button>
    </form>
  )
}
```

### 5. 无障碍访问 (A11y) 要求

#### 5.1 语义化 HTML
```typescript
// ✅ 使用语义化标签
const Article = () => (
  <article>
    <header>
      <h1>文章标题</h1>
      <time dateTime="2024-01-01">2024年1月1日</time>
    </header>
    <main>
      <p>文章内容...</p>
    </main>
  </article>
)
```

#### 5.2 ARIA 属性
```typescript
// ✅ 添加必要的 ARIA 属性
const Modal = ({ isOpen, onClose, title, children }) => (
  <div
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    aria-describedby="modal-description"
  >
    <h2 id="modal-title">{title}</h2>
    <div id="modal-description">{children}</div>
    <button onClick={onClose} aria-label="关闭对话框">
      ×
    </button>
  </div>
)
```

#### 5.3 键盘导航
```typescript
// ✅ 支持键盘操作
const Dropdown = () => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault()
        toggleDropdown()
        break
      case 'Escape':
        closeDropdown()
        break
    }
  }

  return (
    <button
      onKeyDown={handleKeyDown}
      aria-expanded={isOpen}
      aria-haspopup="true"
    >
      选项
    </button>
  )
}
```

## 🎨 设计系统集成

### 1. 颜色使用规范
```typescript
// ✅ 使用设计系统定义的颜色
const colorClasses = {
  primary: 'text-primary-600 bg-primary-50',
  success: 'text-green-600 bg-green-50',
  warning: 'text-yellow-600 bg-yellow-50',
  error: 'text-red-600 bg-red-50',
  info: 'text-blue-600 bg-blue-50',
}
```

### 2. 间距和尺寸
```typescript
// ✅ 使用一致的间距系统
const spacingClasses = {
  xs: 'p-2',      // 8px
  sm: 'p-3',      // 12px
  md: 'p-4',      // 16px
  lg: 'p-6',      // 24px
  xl: 'p-8',      // 32px
}
```

### 3. 字体和排版
```typescript
// ✅ 使用项目字体系统
const typographyClasses = {
  h1: 'text-3xl font-bold text-gray-900',
  h2: 'text-2xl font-semibold text-gray-900',
  h3: 'text-xl font-medium text-gray-900',
  body: 'text-base text-gray-700',
  caption: 'text-sm text-gray-500',
}
```

## 🔧 开发工具配置

### 1. VSCode 推荐设置
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  }
}
```

### 2. 推荐的 VSCode 扩展
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens

### 3. 代码片段示例
```json
// .vscode/snippets.json
{
  "React Functional Component": {
    "prefix": "rfc",
    "body": [
      "import React from 'react'",
      "",
      "interface ${1:Component}Props {",
      "  $2",
      "}",
      "",
      "export const ${1:Component}: React.FC<${1:Component}Props> = ({ $3 }) => {",
      "  return (",
      "    <div>",
      "      $4",
      "    </div>",
      "  )",
      "}",
      ""
    ],
    "description": "Create a React functional component"
  }
}
```

## 📊 性能监控和优化

### 1. 性能监控
```typescript
// ✅ 使用 React DevTools Profiler
import { Profiler } from 'react'

const onRenderCallback = (id, phase, actualDuration) => {
  if (import.meta.env.DEV) {
    console.log('Render:', { id, phase, actualDuration })
  }
}

<Profiler id="ExpensiveComponent" onRender={onRenderCallback}>
  <ExpensiveComponent />
</Profiler>
```

### 2. Bundle 分析
```bash
# 分析打包大小
npm run analyze

# 查看依赖关系
npm run build -- --analyze
```

### 3. 代码分割策略
```typescript
// ✅ 路由级别的代码分割
const LazyDashboard = lazy(() => import('@/modules/dashboard'))
const LazySettings = lazy(() => import('@/modules/settings'))

// ✅ 组件级别的代码分割
const LazyChart = lazy(() => import('@/components/Chart'))
```

## 🚀 部署和构建

### 1. 环境变量管理
```typescript
// ✅ 使用类型安全的环境变量
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_TITLE: string
  readonly VITE_ENABLE_MOCK: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 使用
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
```

### 2. 构建优化
```typescript
// vite.config.ts 中的优化配置
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['framer-motion', 'react-hot-toast'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
})
```

---

## 📋 检查清单

### 组件开发完成检查清单
- [ ] 组件有完整的 TypeScript 类型定义
- [ ] 组件支持 ref 转发（如适用）
- [ ] 组件有对应的单元测试
- [ ] 组件有 Storybook 故事（UI 组件）
- [ ] 组件文档完整（JSDoc 注释）
- [ ] 样式使用 Tailwind CSS 和 cn() 函数
- [ ] 错误状态和加载状态处理完善
- [ ] 无障碍访问支持
- [ ] 性能优化（memo, useMemo, useCallback）
- [ ] 代码通过 ESLint 和 TypeScript 检查

### 模块开发完成检查清单
- [ ] 模块配置完整
- [ ] 路由配置正确
- [ ] 模块注册和加载逻辑
- [ ] 权限控制实现
- [ ] 错误边界包装
- [ ] 模块间通信使用 EventBus
- [ ] 模块文档完整
- [ ] 集成测试覆盖

### 代码提交前检查清单
- [ ] 代码格式化（Prettier）
- [ ] 代码检查通过（ESLint）
- [ ] 类型检查通过（TypeScript）
- [ ] 单元测试通过
- [ ] 构建成功
- [ ] 提交信息规范

---

**注意**: 本文档会根据项目发展持续更新，请定期查看最新版本。如有疑问或建议，请联系项目维护者。
