/**
 * @file 安全信息展示组件
 * @description 显示安全相关信息和提示
 */

import React from 'react'
import { motion } from 'framer-motion'
import clsx from 'clsx'

export interface SecurityInfoProps {
  className?: string
  showTips?: boolean
  showLastLogin?: boolean
  lastLoginTime?: string
  lastLoginLocation?: string
}

export const SecurityInfo: React.FC<SecurityInfoProps> = ({
  className,
  showTips = true,
  showLastLogin = false,
  lastLoginTime,
  lastLoginLocation
}) => {
  return (
    <div className={clsx('space-y-4', className)}>
      {/* 上次登录信息 */}
      {showLastLogin && (lastLoginTime || lastLoginLocation) && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="p-3 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <div className="flex items-start">
            <svg className="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <div className="text-sm">
              <p className="text-blue-800 font-medium">上次登录信息</p>
              {lastLoginTime && (
                <p className="text-blue-600 mt-1">
                  时间: {lastLoginTime}
                </p>
              )}
              {lastLoginLocation && (
                <p className="text-blue-600">
                  地点: {lastLoginLocation}
                </p>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {/* 安全提示 */}
      {showTips && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="p-3 bg-amber-50 border border-amber-200 rounded-lg"
        >
          <div className="flex items-start">
            <svg className="w-4 h-4 text-amber-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div className="text-sm">
              <p className="text-amber-800 font-medium">安全提示</p>
              <ul className="text-amber-700 mt-1 space-y-1">
                <li>• 请勿在公共设备上保存密码</li>
                <li>• 定期更新您的密码</li>
                <li>• 发现异常登录请及时联系管理员</li>
              </ul>
            </div>
          </div>
        </motion.div>
      )}

      {/* 安全功能说明 */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="p-3 bg-green-50 border border-green-200 rounded-lg"
      >
        <div className="flex items-start">
          <svg className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          <div className="text-sm">
            <p className="text-green-800 font-medium">安全保障</p>
            <ul className="text-green-700 mt-1 space-y-1">
              <li>• 256位SSL加密传输</li>
              <li>• 多因子身份验证</li>
              <li>• 实时安全监控</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
