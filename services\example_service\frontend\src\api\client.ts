/**
 * @file API客户端
 * @description 基于axios的HTTP客户端，提供统一的API请求接口和错误处理
 * @status 框架文件 - 完成
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ApiResponse, ApiError, RequestConfig } from './types'

class ApiClient {
  private instance: AxiosInstance
  private baseURL: string

  constructor(baseURL = '/api') {
    this.baseURL = baseURL
    this.instance = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证token
        const token = this.getAuthToken()
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 添加请求ID用于追踪
        if (config.headers) {
          config.headers['X-Request-ID'] = this.generateRequestId()
        }

        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`, {
          params: config.params,
          data: config.data,
        })

        return config
      },
      (error) => {
        console.error('[API] Request error:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`[API] Response:`, response.data)
        return response
      },
      async (error) => {
        console.error('[API] Response error:', error)

        // 处理认证错误
        if (error.response?.status === 401) {
          await this.handleAuthError()
        }

        // 处理网络错误
        if (!error.response) {
          return Promise.reject({
            success: false,
            message: '网络连接错误，请检查网络设置',
            code: 'NETWORK_ERROR',
          } as ApiError)
        }

        // 处理服务器错误
        const apiError: ApiError = {
          success: false,
          message: error.response.data?.message || '服务器错误',
          code: error.response.data?.code || `HTTP_${error.response.status}`,
          details: error.response.data?.details,
          timestamp: new Date().toISOString(),
        }

        return Promise.reject(apiError)
      }
    )
  }

  private getAuthToken(): string | null {
    return localStorage.getItem('accessToken')
  }

  private setAuthToken(token: string) {
    localStorage.setItem('accessToken', token)
  }

  private removeAuthToken() {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private async handleAuthError() {
    // 尝试刷新token
    const refreshToken = localStorage.getItem('refreshToken')
    if (refreshToken) {
      try {
        const response = await this.post<{ accessToken: string }>('/auth/refresh', {
          refreshToken,
        })
        this.setAuthToken(response.data.accessToken)
        return
      } catch (error) {
        console.error('Token refresh failed:', error)
      }
    }

    // 清除认证信息并重定向到登录页
    this.removeAuthToken()
    window.location.href = '/login'
  }

  // GET请求
  async get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.get<ApiResponse<T>>(url, {
      params: config?.params,
      timeout: config?.timeout,
      headers: config?.headers,
    })
    return response.data
  }

  // POST请求
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, {
      timeout: config?.timeout,
      headers: config?.headers,
      params: config?.params,
    })
    return response.data
  }

  // PUT请求
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, {
      timeout: config?.timeout,
      headers: config?.headers,
      params: config?.params,
    })
    return response.data
  }

  // DELETE请求
  async delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url, {
      timeout: config?.timeout,
      headers: config?.headers,
      params: config?.params,
    })
    return response.data
  }

  // PATCH请求
  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.patch<ApiResponse<T>>(url, data, {
      timeout: config?.timeout,
      headers: config?.headers,
      params: config?.params,
    })
    return response.data
  }

  // 上传文件
  async upload<T = any>(url: string, file: File, config?: RequestConfig): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
      timeout: config?.timeout || 30000, // 上传超时时间更长
      params: config?.params,
    })
    return response.data
  }

  // 设置基础URL
  setBaseURL(baseURL: string) {
    this.baseURL = baseURL
    this.instance.defaults.baseURL = baseURL
  }

  // 设置默认headers
  setDefaultHeaders(headers: Record<string, string>) {
    Object.assign(this.instance.defaults.headers.common, headers)
  }
}

// 创建默认实例
const apiClient = new ApiClient()

export { apiClient, ApiClient }
export default apiClient 