/**
 * @file 登录页面
 * @description 用户登录页面，包含登录表单和页面布局
 * @status 开发中
 */

import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { LoginForm } from '../components/LoginForm'
import { useAuth } from '@/stores/authStore'
import { PageLoading } from '@/components/common/LoadingSpinner'

export const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const { isAuthenticated, isLoading, checkAuthStatus } = useAuth()

  // 检查用户是否已登录
  useEffect(() => {
    checkAuthStatus()
  }, [checkAuthStatus])

  // 如果已登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // 登录成功处理
  const handleLoginSuccess = () => {
    // 登录成功后重定向到首页或之前访问的页面
    const redirectTo = new URLSearchParams(window.location.search).get('redirect') || '/'
    navigate(redirectTo, { replace: true })
  }

  // 登录错误处理
  const handleLoginError = (error: string) => {
    console.error('Login error:', error)
    // 错误已经在LoginForm中显示，这里可以添加额外的错误处理逻辑
  }

  // 如果正在检查认证状态，显示加载页面
  if (isLoading) {
    return <PageLoading text="检查登录状态..." />
  }

  // 如果已登录，显示加载页面（等待重定向）
  if (isAuthenticated) {
    return <PageLoading text="正在跳转..." />
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* 页面头部 */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          {/* Logo */}
          <div className="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center">
            <svg
              className="h-8 w-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>
        </div>
        
        <h1 className="mt-6 text-center text-3xl font-bold text-gray-900">
          IAM 管理系统
        </h1>
        <p className="mt-2 text-center text-sm text-gray-600">
          身份与访问管理平台
        </p>
      </div>

      {/* 登录表单 */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <LoginForm
          onSuccess={handleLoginSuccess}
          onError={handleLoginError}
          className="shadow-lg"
        />
      </div>

      {/* 页面底部 */}
      <div className="mt-8 text-center">
        <div className="text-sm text-gray-500">
          <p>© 2024 IAM 管理系统. 保留所有权利.</p>
        </div>
        
        {/* 开发环境提示 */}
        {import.meta.env.DEV && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md mx-auto max-w-md">
            <div className="flex">
              <svg className="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>开发模式</strong>
                </p>
                <p className="text-xs text-yellow-600 mt-1">
                  当前运行在开发环境中
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
