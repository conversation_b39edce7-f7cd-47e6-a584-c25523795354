"""
Mock Fixtures

提供各种Mock对象的fixtures
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.storages.connect_manager import ConnectionManager
from security.jwt_manager import <PERSON><PERSON><PERSON>anager
from security.session_manager import <PERSON>Manager
from security.cache_manager import <PERSON>acheManager
from security.security_utils import SecurityUtils


@pytest.fixture
def mock_async_session():
    """Mock异步数据库会话"""
    session = AsyncMock(spec=AsyncSession)
    session.add = MagicMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.flush = AsyncMock()
    session.delete = AsyncMock()
    session.execute = AsyncMock()
    session.scalar = AsyncMock()
    session.scalars = AsyncMock()
    session.close = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repository():
    """Mock Redis仓库"""
    repo = AsyncMock(spec=RedisRepository)
    repo.get = AsyncMock()
    repo.set = AsyncMock()
    repo.delete = AsyncMock()
    repo.exists = AsyncMock()
    repo.expire = AsyncMock()
    repo.hget = AsyncMock()
    repo.hset = AsyncMock()
    repo.hdel = AsyncMock()
    repo.hgetall = AsyncMock()
    repo.lpush = AsyncMock()
    repo.rpop = AsyncMock()
    repo.llen = AsyncMock()
    return repo


@pytest.fixture
def mock_connection_manager():
    """Mock连接管理器"""
    manager = MagicMock(spec=ConnectionManager)
    
    # Mock各种连接器
    mock_redis_connector = AsyncMock()
    mock_postgres_connector = AsyncMock()
    mock_mysql_connector = AsyncMock()
    mock_mongodb_connector = AsyncMock()
    mock_rabbitmq_connector = AsyncMock()
    
    manager.get_connector.side_effect = lambda name: {
        'redis': mock_redis_connector,
        'postgres': mock_postgres_connector,
        'mysql': mock_mysql_connector,
        'mongodb': mock_mongodb_connector,
        'rabbitmq': mock_rabbitmq_connector
    }.get(name)
    
    return manager


@pytest.fixture
def mock_jwt_manager():
    """Mock JWT管理器"""
    manager = MagicMock(spec=JWTManager)
    manager.generate_token_pair = MagicMock(return_value={
        "access_token": "mock_access_token",
        "refresh_token": "mock_refresh_token",
        "expires_in": 7200,
        "refresh_expires_in": 86400
    })
    manager.verify_token = MagicMock(return_value={"user_id": "test_user"})
    manager.decode_token = MagicMock(return_value={"user_id": "test_user"})
    manager.revoke_token = AsyncMock()
    return manager


@pytest.fixture
def mock_session_manager():
    """Mock会话管理器"""
    manager = AsyncMock(spec=SessionManager)
    manager.create_session = AsyncMock(return_value=MagicMock(session_id="test_session"))
    manager.get_session = AsyncMock()
    manager.update_session = AsyncMock()
    manager.delete_session = AsyncMock()
    manager.cleanup_expired_sessions = AsyncMock()
    return manager


@pytest.fixture
def mock_cache_manager():
    """Mock缓存管理器"""
    manager = AsyncMock(spec=CacheManager)
    manager.get = AsyncMock()
    manager.set = AsyncMock()
    manager.delete = AsyncMock()
    manager.clear = AsyncMock()
    manager.get_stats = AsyncMock(return_value={"hits": 0, "misses": 0})
    return manager


@pytest.fixture
def mock_security_utils():
    """Mock安全工具"""
    utils = MagicMock(spec=SecurityUtils)
    utils.hash_password = MagicMock(return_value="hashed_password")
    utils.verify_password = MagicMock(return_value=True)
    utils.generate_salt = MagicMock(return_value="test_salt")
    utils.generate_random_string = MagicMock(return_value="random_string")
    utils.verify_totp = MagicMock(return_value=True)
    utils.generate_totp_secret = MagicMock(return_value="totp_secret")
    return utils


@pytest.fixture
def mock_email_service():
    """Mock邮件服务"""
    service = AsyncMock()
    service.send_email = AsyncMock(return_value=True)
    service.send_verification_email = AsyncMock(return_value=True)
    service.send_password_reset_email = AsyncMock(return_value=True)
    return service


@pytest.fixture
def mock_sms_service():
    """Mock短信服务"""
    service = AsyncMock()
    service.send_sms = AsyncMock(return_value=True)
    service.send_verification_code = AsyncMock(return_value=True)
    return service


@pytest.fixture
def mock_verification_service():
    """Mock验证服务"""
    service = AsyncMock()
    service.generate_code = AsyncMock(return_value="123456")
    service.verify_code = AsyncMock(return_value=True)
    service.send_email_verification = AsyncMock(return_value=True)
    service.send_sms_verification = AsyncMock(return_value=True)
    return service


@pytest.fixture
def mock_database_models():
    """Mock数据库模型"""
    return {
        "User": MagicMock(),
        "Tenant": MagicMock(),
        "Role": MagicMock(),
        "Permission": MagicMock(),
        "UserRole": MagicMock(),
        "RolePermission": MagicMock(),
        "AuditLog": MagicMock(),
        "SystemConfig": MagicMock(),
        "SecurityPolicy": MagicMock(),
        "SecurityEvent": MagicMock()
    }


@pytest.fixture
def mock_business_services():
    """Mock业务服务"""
    return {
        "tenant_service": AsyncMock(),
        "user_service": AsyncMock(),
        "auth_service": AsyncMock(),
        "role_service": AsyncMock(),
        "permission_service": AsyncMock(),
        "rbac_service": AsyncMock(),
        "audit_service": AsyncMock(),
        "system_config_service": AsyncMock(),
        "advanced_security_service": AsyncMock(),
        "system_service": AsyncMock()
    }


@pytest.fixture
def mock_config_data():
    """Mock配置数据"""
    return {
        "application": {
            "project_name": "test_iam_service",
            "debug": True,
            "title": "Test IAM Service",
            "description": "Test IAM Service API"
        },
        "persistence": {
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 15
            },
            "postgres": {
                "host": "localhost",
                "port": 5432,
                "database": "test_iam"
            }
        },
        "security": {
            "jwt": {
                "algorithm": "RS256",
                "access_token_expire_minutes": 30,
                "refresh_token_expire_days": 7
            },
            "session": {
                "session_timeout_minutes": 60,
                "max_concurrent_sessions": 5
            }
        }
    }


@pytest.fixture
def mock_provider_overrides():
    """Mock provider覆盖配置"""
    return {
        "session": AsyncMock(),
        "redis_repo": AsyncMock(),
        "jwt_manager": MagicMock(),
        "session_manager": AsyncMock(),
        "security_utils": MagicMock()
    }
