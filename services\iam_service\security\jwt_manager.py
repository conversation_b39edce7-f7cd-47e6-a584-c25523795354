"""
JWT令牌管理器

负责JWT访问令牌和刷新令牌的生成、验证、刷新等操作
"""
import json

import jwt
import uuid
import hashlib
import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives.serialization import load_pem_private_key, load_pem_public_key

from commonlib.storages.persistence.redis.repository import RedisRepository


@dataclass
class TokenPair:
    """令牌对"""
    access_token: str
    refresh_token: str
    expires_in: float
    token_type: str = "Bearer"


@dataclass
class TokenPayload:
    """令牌载荷"""
    user_id: str
    tenant_id: str
    session_id: str
    roles: list
    permissions_hash: str
    device_fingerprint: Optional[str] = None
    ip_address: Optional[str] = None
    issued_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class JWTManager:
    """JWT令牌管理器"""
    
    def __init__(
        self,
        redis_repo: RedisRepository,
        private_key: Optional[str] = None,
        public_key: Optional[str] = None,
        access_token_expire_minutes: float = 30,
        refresh_token_expire_days: int = 7,
        algorithm: str = "RS256"
    ):
        self.redis_repo = redis_repo
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days

        # 密钥对将在第一次使用时异步初始化
        self.private_key: Optional[str] = private_key
        self.public_key: Optional[str] = public_key
        self._keys_initialized = False
        self._init_lock = asyncio.Lock()

    async def _ensure_keys_initialized(self):
        """确保密钥已初始化"""
        if self._keys_initialized:
            return

        async with self._init_lock:
            if self._keys_initialized:
                return

            self.private_key, self.public_key = await self._init_keys(
                self.private_key, self.public_key
            )
            self._keys_initialized = True
    
    async def _init_keys(self, private_key: Optional[str], public_key: Optional[str]) -> Tuple[str, str]:
        """初始化RSA密钥对"""
        # 1. 如果明确提供了密钥，直接使用
        if private_key and public_key:
            await self._validate_key_pair(private_key, public_key)
            return private_key, public_key

        # 2. 尝试从环境变量加载
        env_private = os.getenv('JWT_PRIVATE_KEY')
        env_public = os.getenv('JWT_PUBLIC_KEY')

        if env_private and env_public:
            await self._validate_key_pair(env_private, env_public)
            return env_private, env_public

        # 3. 从Redis缓存中获取或生成新密钥对
        return await self._get_or_create_keys_from_cache()

    async def _get_or_create_keys_from_cache(self) -> Tuple[str, str]:
        """从Redis缓存获取或创建密钥对"""
        cache_private_key = "jwt_keys:private_key"
        cache_public_key = "jwt_keys:public_key"
        lock_key = "jwt_keys:init_lock"

        # 首先尝试从缓存获取现有密钥
        cached_private = await self.redis_repo.get(cache_private_key)
        cached_public = await self.redis_repo.get(cache_public_key)

        if cached_private and cached_public:
            try:
                await self._validate_key_pair(cached_private, cached_public)
                return cached_private, cached_public
            except Exception as e:
                # 缓存的密钥无效，需要重新生成
                await self._log_token_error("invalid_cached_keys", str(e))

        # 使用分布式锁防止并发生成密钥
        return await self._generate_keys_with_lock(lock_key, cache_private_key, cache_public_key)

    async def _generate_keys_with_lock(
        self,
        lock_key: str,
        cache_private_key: str,
        cache_public_key: str
    ) -> Tuple[str, str]:
        """使用分布式锁生成密钥对"""
        lock_timeout = 30  # 30秒锁超时
        lock_acquired = False

        try:
            # 尝试获取分布式锁
            lock_acquired = await self._acquire_redis_lock(lock_key, lock_timeout)

            if not lock_acquired:
                # 获取锁失败，等待其他实例完成并重试获取密钥
                await asyncio.sleep(1)
                return await self._wait_for_keys_from_cache(cache_private_key, cache_public_key)

            # 获取锁成功，再次检查是否已有其他实例生成了密钥
            cached_private = await self.redis_repo.get(cache_private_key)
            cached_public = await self.redis_repo.get(cache_public_key)

            if cached_private and cached_public:
                try:
                    await self._validate_key_pair(cached_private, cached_public)
                    return cached_private, cached_public
                except Exception:
                    pass  # 密钥无效，继续生成新的

            # 生成新的密钥对
            private_pem, public_pem = self._generate_new_key_pair()

            # 保存到Redis缓存（永久保存）
            await self.redis_repo.set(cache_private_key, private_pem)
            await self.redis_repo.set(cache_public_key, public_pem)

            # 记录密钥生成日志
            await self._log_key_generation()

            return private_pem, public_pem

        finally:
            # 释放分布式锁
            if lock_acquired:
                await self._release_redis_lock(lock_key)

    async def _wait_for_keys_from_cache(
        self,
        cache_private_key: str,
        cache_public_key: str,
        max_wait_seconds: int = 10
    ) -> Tuple[str, str]:
        """等待其他实例生成密钥并从缓存获取"""
        start_time = datetime.now()

        while (datetime.now() - start_time).total_seconds() < max_wait_seconds:
            cached_private = await self.redis_repo.get(cache_private_key)
            cached_public = await self.redis_repo.get(cache_public_key)

            if cached_private and cached_public:
                try:
                    await self._validate_key_pair(cached_private, cached_public)
                    return cached_private, cached_public
                except Exception:
                    pass

            await asyncio.sleep(0.5)  # 等待500ms后重试

        # 等待超时，生成临时密钥对
        return self._generate_new_key_pair()

    def _generate_new_key_pair(self) -> Tuple[str, str]:
        """生成新的RSA密钥对"""
        private_key_obj = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )

        private_pem = private_key_obj.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ).decode('utf-8')

        public_pem = private_key_obj.public_key().public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        ).decode('utf-8')

        return private_pem, public_pem

    async def _acquire_redis_lock(self, lock_key: str, timeout_seconds: int) -> bool:
        """获取Redis分布式锁"""
        try:
            # 使用SET命令的NX选项实现分布式锁
            lock_value = str(uuid.uuid4())

            # 注意：这里需要Redis仓库支持NX选项，如果不支持，使用替代方案
            try:
                # 尝试使用带NX选项的set方法
                success = await self.redis_repo.set(
                    lock_key,
                    lock_value,
                    ttl=timeout_seconds,
                    nx=True
                )
            except TypeError:
                # 如果Redis仓库不支持nx参数，使用替代方案
                exists = await self.redis_repo.exists(lock_key)
                if not exists:
                    await self.redis_repo.set(lock_key, lock_value, ttl=timeout_seconds)
                    success = True
                else:
                    success = False

            if success:
                # 保存锁值用于释放时验证
                self._current_lock_value = lock_value
                return True

            return False

        except Exception as e:
            await self._log_token_error("lock_acquire_failed", str(e))
            return False

    async def _release_redis_lock(self, lock_key: str) -> bool:
        """释放Redis分布式锁"""
        try:
            # 简单的删除锁（在生产环境中应该使用Lua脚本确保原子性）
            current_value = await self.redis_repo.get(lock_key)
            expected_value = getattr(self, '_current_lock_value', '')

            if current_value == expected_value:
                await self.redis_repo.delete(lock_key)
                return True

            return False

        except Exception as e:
            await self._log_token_error("lock_release_failed", str(e))
            return False

    async def _validate_key_pair(self, private_key: str, public_key: str) -> None:
        """验证密钥对的有效性"""
        try:
            # 验证私钥格式
            private_key_obj = load_pem_private_key(
                private_key.encode('utf-8'),
                password=None
            )

            # 验证公钥格式
            public_key_obj = load_pem_public_key(public_key.encode('utf-8'))

            # 验证密钥对匹配性 - 使用JWT签名验证
            test_payload = {"test": "key_validation", "iat": datetime.now().timestamp()}

            # 使用私钥签名
            test_token = jwt.encode(test_payload, private_key, algorithm=self.algorithm)

            # 使用公钥验证
            jwt.decode(test_token, public_key, algorithms=[self.algorithm])

        except Exception as e:
            raise ValueError(f"Invalid key pair: {str(e)}")

    async def _log_key_generation(self) -> None:
        """记录密钥生成日志"""
        log_entry = {
            "event": "jwt_keys_generated",
            "timestamp": datetime.now().isoformat(),
            "algorithm": self.algorithm,
            "key_size": 2048
        }
        log_entry = json.dumps(log_entry,ensure_ascii=False)
        await self.redis_repo.lpush("jwt_key_events", log_entry)
        await self.redis_repo.ltrim("jwt_key_events", 0, 99)  # 保留最近100条

    async def generate_token_pair(
        self,
        user_id: str,
        tenant_id: str,
        session_id: str,
        roles: list,
        permissions: list,
        device_fingerprint: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> TokenPair:
        """生成JWT令牌对"""
        # 确保密钥已初始化
        await self._ensure_keys_initialized()

        now = datetime.now()
        access_expires = now + timedelta(minutes=self.access_token_expire_minutes)

        # 计算权限摘要
        permissions_hash = self._calculate_permissions_hash(permissions)

        # 构建访问令牌载荷
        access_payload = {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "session_id": session_id,
            "roles": roles,
            "permissions_hash": permissions_hash,
            "device_fingerprint": device_fingerprint,
            "ip_address": ip_address,
            "iat": now.timestamp(),
            "exp": access_expires.timestamp(),
            "type": "access"
        }

        # 生成访问令牌
        access_token = jwt.encode(
            access_payload,
            self.private_key,
            algorithm=self.algorithm
        )
        
        # 生成刷新令牌
        refresh_token_id = str(uuid.uuid4())
        refresh_token = self._generate_refresh_token(
            refresh_token_id, user_id, tenant_id, session_id
        )
        
        # 存储刷新令牌信息
        await self._store_refresh_token(
            refresh_token_id, user_id, tenant_id, session_id,
            device_fingerprint, ip_address
        )
        
        return TokenPair(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=self.access_token_expire_minutes * 60
        )
    
    async def verify_access_token(self, token: str) -> Optional[TokenPayload]:
        """验证访问令牌"""
        try:
            # 确保密钥已初始化
            await self._ensure_keys_initialized()

            # 1. 解码JWT令牌
            payload = jwt.decode(
                token,
                self.public_key,
                algorithms=[self.algorithm]
            )

            # 2. 验证令牌类型
            if payload.get("type") != "access":
                return None

            # 3. 检查令牌是否在黑名单中
            is_blacklisted = await self._is_token_blacklisted(token)
            if is_blacklisted:
                return None

            # 4. 验证会话有效性
            session_valid = await self._verify_session(payload.get("session_id"))
            if not session_valid:
                return None

            # 5. 构建并返回令牌载荷
            return TokenPayload(
                user_id=payload["user_id"],
                tenant_id=payload["tenant_id"],
                session_id=payload["session_id"],
                roles=payload.get("roles", []),
                permissions_hash=payload.get("permissions_hash", ""),
                device_fingerprint=payload.get("device_fingerprint"),
                ip_address=payload.get("ip_address"),
                issued_at=datetime.fromtimestamp(payload["iat"]),
                expires_at=datetime.fromtimestamp(payload["exp"])
            )

        except jwt.ExpiredSignatureError:
            # 令牌已过期
            return None
        except jwt.InvalidTokenError:
            # 令牌无效
            return None
        except Exception:
            # 其他异常
            return None
    
    async def refresh_token_pair(
        self,
        refresh_token: str,
        device_fingerprint: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> Optional[TokenPair]:
        """刷新令牌对"""
        try:
            # 1. 验证刷新令牌有效性
            token_info = await self._verify_refresh_token(refresh_token)
            if not token_info:
                return None

            # 2. 检查设备指纹匹配
            if (device_fingerprint and
                token_info.get("device_fingerprint") and
                token_info.get("device_fingerprint") != device_fingerprint):
                # 设备指纹不匹配，可能是令牌泄露，撤销令牌
                await self._revoke_refresh_token(refresh_token)
                return None

            # 3. 检查IP地址变化（可选的安全检查）
            if (ip_address and
                token_info.get("ip_address") and
                await self._is_suspicious_ip_change(token_info.get("ip_address"), ip_address)):
                # IP地址异常变化，需要重新认证
                await self._revoke_refresh_token(refresh_token)
                return None

            # 4. 获取用户最新信息
            user_id = token_info["user_id"]
            tenant_id = token_info["tenant_id"]
            session_id = token_info["session_id"]

            # 这里需要从数据库获取最新的角色和权限
            # 暂时使用空列表，实际应该从数据库查询
            roles, permissions = [], []

            # 5. 销毁旧的刷新令牌（一次性使用）
            await self._revoke_refresh_token(refresh_token)

            # 6. 生成新的令牌对
            new_token_pair = await self.generate_token_pair(
                user_id, tenant_id, session_id, roles, permissions,
                device_fingerprint, ip_address
            )

            # 7. 记录令牌刷新日志
            await self._log_token_refresh(user_id, tenant_id, session_id, ip_address)

            return new_token_pair

        except Exception as e:
            # 刷新失败，记录错误日志
            await self._log_token_error("refresh_failed", str(e))
            return None
    
    async def revoke_token(self, token: str, token_type: str = "refresh") -> bool:
        """撤销令牌"""
        # TODO: 实现令牌撤销逻辑
        # 1. 验证令牌格式
        # 2. 添加到黑名单
        # 3. 清理相关缓存
        # 4. 记录撤销日志
        # 5. 返回撤销结果
        
        if token_type == "refresh":
            return await self._revoke_refresh_token(token)
        else:
            return await self._revoke_access_token(token)
    
    def _calculate_permissions_hash(self, permissions: list) -> str:
        """计算权限摘要哈希"""
        permissions_str = "|".join(sorted(permissions))
        return hashlib.sha256(permissions_str.encode()).hexdigest()[:16]
    
    def _generate_refresh_token(
        self,
        token_id: str,
        user_id: str,
        tenant_id: str,
        session_id: str
    ) -> str:
        """生成刷新令牌"""
        # 使用UUID + 用户信息生成安全的刷新令牌
        token_data = f"{token_id}:{user_id}:{tenant_id}:{session_id}"
        return hashlib.sha256(token_data.encode()).hexdigest()
    
    async def _store_refresh_token(
        self,
        token_id: str,
        user_id: str,
        tenant_id: str,
        session_id: str,
        device_fingerprint: Optional[str],
        ip_address: Optional[str]
    ) -> None:
        """存储刷新令牌信息"""
        token_info = {
            "token_id": token_id,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "session_id": session_id,
            "device_fingerprint": device_fingerprint,
            "ip_address": ip_address,
            "created_at": datetime.now().isoformat(),
            "expires_at": (datetime.now() + timedelta(days=self.refresh_token_expire_days)).isoformat()
        }
        
        # 存储到Redis
        await self.redis_repo.set(
            f"refresh_token:{token_id}",
            token_info,
            ttl=self.refresh_token_expire_days * 24 * 3600
        )
    
    async def _verify_refresh_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """验证刷新令牌"""
        try:
            # 从Redis中查找所有刷新令牌，匹配哈希值
            # 这是一个简化的实现，实际中可以优化存储结构
            pattern = "refresh_token:*"
            keys = await self.redis_repo.keys(pattern)

            for key in keys:
                token_info = await self.redis_repo.get(key)
                if not token_info:
                    continue

                # 重新生成令牌哈希进行比较
                token_id = token_info.get("token_id")
                user_id = token_info.get("user_id")
                tenant_id = token_info.get("tenant_id")
                session_id = token_info.get("session_id")

                expected_token = self._generate_refresh_token(
                    token_id, user_id, tenant_id, session_id
                )

                if expected_token == refresh_token:
                    # 检查令牌是否过期
                    expires_at_str = token_info.get("expires_at")
                    if expires_at_str:
                        expires_at = datetime.fromisoformat(expires_at_str)
                        if datetime.now() > expires_at:
                            # 令牌已过期，删除
                            await self.redis_repo.delete(key)
                            return None

                    return token_info

            return None

        except Exception:
            return None
    
    async def _verify_session(self, session_id: str) -> bool:
        """验证会话有效性"""
        session_info = await self.redis_repo.get(f"session:{session_id}")
        return session_info is not None
    
    async def _revoke_refresh_token(self, refresh_token: str) -> bool:
        """撤销刷新令牌"""
        try:
            # 查找并删除刷新令牌
            pattern = "refresh_token:*"
            keys = await self.redis_repo.keys(pattern)

            for key in keys:
                token_info = await self.redis_repo.get(key)
                if not token_info:
                    continue

                # 重新生成令牌哈希进行比较
                token_id = token_info.get("token_id")
                user_id = token_info.get("user_id")
                tenant_id = token_info.get("tenant_id")
                session_id = token_info.get("session_id")

                expected_token = self._generate_refresh_token(
                    token_id, user_id, tenant_id, session_id
                )

                if expected_token == refresh_token:
                    await self.redis_repo.delete(key)
                    return True

            return False

        except Exception:
            return False

    async def _revoke_access_token(self, access_token: str) -> bool:
        """撤销访问令牌（添加到黑名单）"""
        try:
            # 解码令牌获取过期时间
            payload = jwt.decode(
                access_token,
                self.public_key,
                algorithms=[self.algorithm],
                options={"verify_exp": False}  # 不验证过期时间
            )

            # 计算令牌剩余有效时间
            exp_timestamp = payload.get("exp")
            if exp_timestamp:
                expires_at = datetime.fromtimestamp(exp_timestamp)
                now = datetime.now()

                if expires_at > now:
                    # 令牌还未过期，添加到黑名单
                    ttl = int((expires_at - now).total_seconds())
                    token_hash = hashlib.sha256(access_token.encode()).hexdigest()

                    await self.redis_repo.set(
                        f"blacklist_token:{token_hash}",
                        {"revoked_at": now.isoformat()},
                        ttl=ttl
                    )

                    return True

            return False

        except Exception:
            return False

    async def _is_token_blacklisted(self, access_token: str) -> bool:
        """检查令牌是否在黑名单中"""
        try:
            token_hash = hashlib.sha256(access_token.encode()).hexdigest()
            blacklist_info = await self.redis_repo.get(f"blacklist_token:{token_hash}")
            return blacklist_info is not None
        except Exception:
            return False

    async def _is_suspicious_ip_change(self, old_ip: str, new_ip: str) -> bool:
        """检查IP地址变化是否可疑"""
        # 简单实现：如果IP完全不同则认为可疑
        # 实际应用中可以集成地理位置服务进行更精确的判断
        return old_ip != new_ip

    async def _log_token_refresh(
        self,
        user_id: str,
        tenant_id: str,
        session_id: str,
        ip_address: Optional[str]
    ) -> None:
        """记录令牌刷新日志"""
        log_entry = {
            "event": "token_refresh",
            "user_id": user_id,
            "tenant_id": tenant_id,
            "session_id": session_id,
            "ip_address": ip_address,
            "timestamp": datetime.now().isoformat()
        }
        log_entry = json.dumps(log_entry,ensure_ascii=False)
        # 存储到审计日志
        await self.redis_repo.lpush(f"audit_log:{user_id}", log_entry)

        # 限制日志数量，只保留最近100条
        await self.redis_repo.ltrim(f"audit_log:{user_id}", 0, 99)

    async def _log_token_error(self, error_type: str, error_message: str) -> None:
        """记录令牌错误日志"""
        log_entry = {
            "event": "token_error",
            "error_type": error_type,
            "error_message": error_message,
            "timestamp": datetime.now().isoformat()
        }
        log_entry = json.dumps(log_entry,ensure_ascii=False)
        await self.redis_repo.lpush("token_errors", log_entry)
        await self.redis_repo.ltrim("token_errors", 0, 999)  # 保留最近1000条错误日志

    async def get_token_stats(self) -> Dict[str, Any]:
        """获取令牌统计信息"""
        try:
            # 统计活跃的刷新令牌数量
            refresh_token_keys = await self.redis_repo.keys("refresh_token:*")
            active_refresh_tokens = len(refresh_token_keys)

            # 统计黑名单令牌数量
            blacklist_keys = await self.redis_repo.keys("blacklist_token:*")
            blacklisted_tokens = len(blacklist_keys)

            # 获取最近的错误日志数量
            error_count = await self.redis_repo.llen("token_errors")

            return {
                "active_refresh_tokens": active_refresh_tokens,
                "blacklisted_tokens": blacklisted_tokens,
                "recent_errors": error_count,
                "timestamp": datetime.now().isoformat()
            }

        except Exception:
            return {
                "active_refresh_tokens": 0,
                "blacklisted_tokens": 0,
                "recent_errors": 0,
                "timestamp": datetime.now().isoformat()
            }

    async def cleanup_expired_tokens(self) -> Dict[str, int]:
        """清理过期的令牌和黑名单"""
        cleaned_refresh = 0
        cleaned_blacklist = 0

        try:
            # 清理过期的刷新令牌
            refresh_keys = await self.redis_repo.keys("refresh_token:*")
            for key in refresh_keys:
                token_info = await self.redis_repo.get(key)
                if token_info and token_info.get("expires_at"):
                    expires_at = datetime.fromisoformat(token_info["expires_at"])
                    if datetime.now() > expires_at:
                        await self.redis_repo.delete(key)
                        cleaned_refresh += 1

            # Redis会自动清理过期的黑名单令牌（通过TTL）
            # 这里只是统计当前的数量
            blacklist_keys = await self.redis_repo.keys("blacklist_token:*")
            cleaned_blacklist = len(blacklist_keys)

        except Exception:
            pass

        return {
            "cleaned_refresh_tokens": cleaned_refresh,
            "remaining_blacklist_tokens": cleaned_blacklist
        }
