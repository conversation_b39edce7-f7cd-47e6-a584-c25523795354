/**
 * @file 忘记密码表单组件
 * @description 用户忘记密码时的邮箱验证表单
 */

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import clsx from 'clsx'
import { Button, Input } from '@/components/ui'
import { TenantInfo } from '@/types'
import * as authApi from '@/api/auth'

// Zod验证模式
const forgotPasswordSchema = z.object({
  tenant_code: z.string().min(1, '请输入租户编码'),
  email: z.string().email('请输入有效的邮箱地址')
})

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>

export interface ForgotPasswordFormProps {
  tenant?: TenantInfo
  onSuccess?: (email: string) => void
  onError?: (error: string) => void
  onBackToLogin?: () => void
  className?: string
}

export const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  tenant,
  onSuccess,
  onError,
  onBackToLogin,
  className
}) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const [emailSent, setEmailSent] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    getValues
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      tenant_code: tenant?.code || '',
      email: ''
    }
  })

  // 如果有租户信息，自动填充租户编码
  React.useEffect(() => {
    if (tenant?.code) {
      setValue('tenant_code', tenant.code)
    }
  }, [tenant, setValue])

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsLoading(true)

    try {
      // 先获取租户信息
      const tenantInfo = await authApi.getTenantByCode(data.tenant_code)

      // 发送重置密码邮件
      const resetRequest = {
        tenant_id: tenantInfo.tenant_id,
        email: data.email,
        scene: 'reset_password' as const
      }

      await authApi.sendEmailCode(resetRequest)
      
      setEmailSent(true)
      onSuccess?.(data.email)
    } catch (err: any) {
      const errorMessage = err.message || '发送重置邮件失败，请重试'
      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendEmail = async () => {
    const { tenant_code, email } = getValues()
    
    if (!email) {
      onError?.('请先输入邮箱地址')
      return
    }

    setIsLoading(true)

    try {
      const tenantInfo = await authApi.getTenantByCode(tenant_code)
      
      const resetRequest = {
        tenant_id: tenantInfo.tenant_id,
        email,
        scene: 'reset_password' as const
      }

      await authApi.sendEmailCode(resetRequest)
      onSuccess?.('重置邮件已重新发送')
    } catch (err: any) {
      onError?.(err.message || '重新发送失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (emailSent) {
    return (
      <div className={clsx('space-y-6 text-center', className)}>
        {/* 成功图标 */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, type: 'spring' }}
          className="flex justify-center"
        >
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </motion.div>

        {/* 成功信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-4"
        >
          <h3 className="text-xl font-semibold text-gray-900">
            重置邮件已发送
          </h3>
          <p className="text-gray-600">
            我们已向您的邮箱发送了密码重置链接，请查收邮件并按照指引重置密码。
          </p>
          <p className="text-sm text-gray-500">
            如果您没有收到邮件，请检查垃圾邮件文件夹，或点击下方按钮重新发送。
          </p>
        </motion.div>

        {/* 操作按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="space-y-3"
        >
          <Button
            variant="outline"
            size="lg"
            onClick={handleResendEmail}
            loading={isLoading}
            disabled={isLoading}
            className="w-full"
          >
            重新发送邮件
          </Button>
          
          {onBackToLogin && (
            <Button
              variant="ghost"
              size="lg"
              onClick={onBackToLogin}
              disabled={isLoading}
              className="w-full"
            >
              返回登录
            </Button>
          )}
        </motion.div>
      </div>
    )
  }

  return (
    <div className={clsx('space-y-6', className)}>
      {/* 表单头部说明 */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="text-center space-y-2"
      >
        <h3 className="text-lg font-semibold text-gray-900">
          重置密码
        </h3>
        <p className="text-sm text-gray-600">
          请输入您的邮箱地址，我们将发送重置密码的链接给您
        </p>
      </motion.div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* 租户编码 */}
        {!tenant && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Input
              label="租户编码"
              placeholder="请输入租户编码"
              error={errors.tenant_code?.message}
              disabled={isLoading || isSubmitting}
              {...register('tenant_code')}
            />
          </motion.div>
        )}

        {/* 邮箱输入 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Input
            label="邮箱地址"
            type="email"
            placeholder="请输入您的邮箱地址"
            error={errors.email?.message}
            disabled={isLoading || isSubmitting}
            {...register('email')}
          />
        </motion.div>

        {/* 提交按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isLoading || isSubmitting}
            disabled={isLoading || isSubmitting}
            className="w-full"
          >
            {isLoading || isSubmitting ? '发送中...' : '发送重置邮件'}
          </Button>
        </motion.div>
      </form>

      {/* 返回登录 */}
      {onBackToLogin && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="text-center"
        >
          <button
            type="button"
            onClick={onBackToLogin}
            className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
            disabled={isLoading || isSubmitting}
          >
            ← 返回登录
          </button>
        </motion.div>
      )}
    </div>
  )
}
