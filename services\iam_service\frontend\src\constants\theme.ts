/**
 * @file 主题相关常量
 * @description 主题系统的常量定义
 */

// 主题模式常量
export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
} as const

// 主题变体常量
export const THEME_VARIANTS = {
  DEFAULT: 'default',
  TENANT: 'tenant',
  CUSTOM: 'custom'
} as const

// 颜色模式常量
export const COLOR_MODES = {
  LIGHT: 'light',
  DARK: 'dark'
} as const

// 主题存储键名
export const THEME_STORAGE_KEYS = {
  MODE: 'theme_mode',
  VARIANT: 'theme_variant',
  TENANT_CONFIG: 'theme_tenant_config',
  CUSTOM_COLORS: 'theme_custom_colors'
} as const

// 默认主题配置
export const DEFAULT_THEME_CONFIG = {
  mode: THEME_MODES.LIGHT,
  variant: THEME_VARIANTS.DEFAULT,
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a'
    },
    secondary: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a'
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d'
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f'
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d'
    },
    info: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e'
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827'
    }
  }
} as const

// 浅色主题颜色
export const LIGHT_THEME_COLORS = {
  background: {
    primary: '#ffffff',
    secondary: '#f9fafb',
    tertiary: '#f3f4f6',
    overlay: 'rgba(0, 0, 0, 0.5)'
  },
  text: {
    primary: '#111827',
    secondary: '#6b7280',
    tertiary: '#9ca3af',
    inverse: '#ffffff',
    disabled: '#d1d5db'
  },
  border: {
    primary: '#e5e7eb',
    secondary: '#d1d5db',
    focus: '#3b82f6',
    error: '#ef4444'
  },
  shadow: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  }
} as const

// 深色主题颜色
export const DARK_THEME_COLORS = {
  background: {
    primary: '#111827',
    secondary: '#1f2937',
    tertiary: '#374151',
    overlay: 'rgba(0, 0, 0, 0.7)'
  },
  text: {
    primary: '#f9fafb',
    secondary: '#d1d5db',
    tertiary: '#9ca3af',
    inverse: '#111827',
    disabled: '#6b7280'
  },
  border: {
    primary: '#374151',
    secondary: '#4b5563',
    focus: '#60a5fa',
    error: '#f87171'
  },
  shadow: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3)'
  }
} as const

// 字体配置
export const FONT_FAMILIES = {
  sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
  serif: ['ui-serif', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
  mono: ['ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace']
} as const

// 字体大小
export const FONT_SIZES = {
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  base: '1rem',     // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.875rem', // 30px
  '4xl': '2.25rem', // 36px
  '5xl': '3rem',    // 48px
  '6xl': '3.75rem'  // 60px
} as const

// 字体粗细
export const FONT_WEIGHTS = {
  thin: 100,
  light: 300,
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
  extrabold: 800
} as const

// 行高
export const LINE_HEIGHTS = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.625,
  loose: 2
} as const

// 字母间距
export const LETTER_SPACINGS = {
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em'
} as const

// 间距系统
export const SPACING = {
  0: '0px',
  1: '0.25rem',   // 4px
  2: '0.5rem',    // 8px
  3: '0.75rem',   // 12px
  4: '1rem',      // 16px
  5: '1.25rem',   // 20px
  6: '1.5rem',    // 24px
  8: '2rem',      // 32px
  10: '2.5rem',   // 40px
  12: '3rem',     // 48px
  16: '4rem',     // 64px
  20: '5rem',     // 80px
  24: '6rem',     // 96px
  32: '8rem',     // 128px
  40: '10rem',    // 160px
  48: '12rem',    // 192px
  56: '14rem',    // 224px
  64: '16rem'     // 256px
} as const

// 动画持续时间
export const ANIMATION_DURATIONS = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms'
} as const

// 动画缓动函数
export const ANIMATION_EASINGS = {
  linear: 'linear',
  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
} as const

// 响应式断点
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
} as const

// 认证页面布局模式
export const AUTH_LAYOUT_MODES = {
  SPLIT: 'split',
  CENTERED: 'centered'
} as const

// 认证页面断点配置
export const AUTH_LAYOUT_BREAKPOINTS = {
  MOBILE_MAX: 767,
  TABLET_MIN: 768,
  TABLET_MAX: 1023,
  DESKTOP_MIN: 1024
} as const

// 品牌区域配置
export const BRAND_SECTION_CONFIG = {
  WIDTH_PERCENTAGE: 40,
  MIN_WIDTH: '320px',
  MAX_WIDTH: '600px',
  BACKGROUND_GRADIENT: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
} as const

// 表单区域配置
export const FORM_SECTION_CONFIG = {
  WIDTH_PERCENTAGE: 60,
  MAX_WIDTH: '400px',
  PADDING: '2rem',
  BORDER_RADIUS: '0.5rem'
} as const

// CSS变量前缀
export const CSS_VARIABLE_PREFIX = '--tsif' as const

// 主题CSS变量映射
export const THEME_CSS_VARIABLES = {
  PRIMARY_COLOR: `${CSS_VARIABLE_PREFIX}-primary`,
  SECONDARY_COLOR: `${CSS_VARIABLE_PREFIX}-secondary`,
  BACKGROUND_COLOR: `${CSS_VARIABLE_PREFIX}-background`,
  TEXT_COLOR: `${CSS_VARIABLE_PREFIX}-text`,
  BORDER_COLOR: `${CSS_VARIABLE_PREFIX}-border`,
  SHADOW_COLOR: `${CSS_VARIABLE_PREFIX}-shadow`
} as const

// 主题切换动画
export const THEME_TRANSITION = {
  PROPERTY: 'background-color, color, border-color, box-shadow',
  DURATION: '200ms',
  TIMING_FUNCTION: 'ease-in-out'
} as const
