/**
 * @file 无障碍访问Hook
 * @description 提供键盘导航、屏幕阅读器支持和焦点管理功能
 */

import React from 'react'
import { useHotkeys } from 'react-hotkeys-hook'

export interface AccessibilityOptions {
  enableKeyboardNavigation?: boolean
  enableFocusManagement?: boolean
  enableScreenReaderSupport?: boolean
  announceChanges?: boolean
}

export interface AccessibilityState {
  isKeyboardUser: boolean
  currentFocusIndex: number
  announcements: string[]
}

export const useAccessibility = (options: AccessibilityOptions = {}) => {
  const {
    enableKeyboardNavigation = true,
    enableFocusManagement = true,
    enableScreenReaderSupport = true,
    announceChanges = true
  } = options

  const [state, setState] = React.useState<AccessibilityState>({
    isKeyboardUser: false,
    currentFocusIndex: -1,
    announcements: []
  })

  const focusableElementsRef = React.useRef<HTMLElement[]>([])
  const announcementTimeoutRef = React.useRef<NodeJS.Timeout>()

  // 检测键盘用户
  React.useEffect(() => {
    if (!enableKeyboardNavigation) return

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        setState(prev => ({ ...prev, isKeyboardUser: true }))
      }
    }

    const handleMouseDown = () => {
      setState(prev => ({ ...prev, isKeyboardUser: false }))
    }

    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('mousedown', handleMouseDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('mousedown', handleMouseDown)
    }
  }, [enableKeyboardNavigation])

  // 管理可聚焦元素
  const updateFocusableElements = React.useCallback(() => {
    if (!enableFocusManagement) return

    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ')

    const elements = Array.from(
      document.querySelectorAll(focusableSelectors)
    ) as HTMLElement[]

    focusableElementsRef.current = elements.filter(el => {
      const style = window.getComputedStyle(el)
      return style.display !== 'none' && style.visibility !== 'hidden'
    })
  }, [enableFocusManagement])

  // 焦点导航
  const focusElement = React.useCallback((index: number) => {
    const elements = focusableElementsRef.current
    if (elements.length === 0) return

    const targetIndex = Math.max(0, Math.min(index, elements.length - 1))
    const element = elements[targetIndex]
    
    if (element) {
      element.focus()
      setState(prev => ({ ...prev, currentFocusIndex: targetIndex }))
    }
  }, [])

  const focusNext = React.useCallback(() => {
    updateFocusableElements()
    const nextIndex = state.currentFocusIndex + 1
    focusElement(nextIndex >= focusableElementsRef.current.length ? 0 : nextIndex)
  }, [state.currentFocusIndex, focusElement, updateFocusableElements])

  const focusPrevious = React.useCallback(() => {
    updateFocusableElements()
    const prevIndex = state.currentFocusIndex - 1
    focusElement(prevIndex < 0 ? focusableElementsRef.current.length - 1 : prevIndex)
  }, [state.currentFocusIndex, focusElement, updateFocusableElements])

  const focusFirst = React.useCallback(() => {
    updateFocusableElements()
    focusElement(0)
  }, [focusElement, updateFocusableElements])

  const focusLast = React.useCallback(() => {
    updateFocusableElements()
    focusElement(focusableElementsRef.current.length - 1)
  }, [focusElement, updateFocusableElements])

  // 键盘快捷键
  useHotkeys('tab', (e) => {
    if (!enableKeyboardNavigation) return
    e.preventDefault()
    focusNext()
  }, { enableOnFormTags: true })

  useHotkeys('shift+tab', (e) => {
    if (!enableKeyboardNavigation) return
    e.preventDefault()
    focusPrevious()
  }, { enableOnFormTags: true })

  useHotkeys('home', (e) => {
    if (!enableKeyboardNavigation || !state.isKeyboardUser) return
    e.preventDefault()
    focusFirst()
  }, { enableOnFormTags: true })

  useHotkeys('end', (e) => {
    if (!enableKeyboardNavigation || !state.isKeyboardUser) return
    e.preventDefault()
    focusLast()
  }, { enableOnFormTags: true })

  // 屏幕阅读器公告
  const announce = React.useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!enableScreenReaderSupport || !announceChanges) return

    setState(prev => ({
      ...prev,
      announcements: [...prev.announcements, message]
    }))

    // 创建或更新ARIA live region
    let liveRegion = document.getElementById('accessibility-announcements')
    if (!liveRegion) {
      liveRegion = document.createElement('div')
      liveRegion.id = 'accessibility-announcements'
      liveRegion.setAttribute('aria-live', priority)
      liveRegion.setAttribute('aria-atomic', 'true')
      liveRegion.style.position = 'absolute'
      liveRegion.style.left = '-10000px'
      liveRegion.style.width = '1px'
      liveRegion.style.height = '1px'
      liveRegion.style.overflow = 'hidden'
      document.body.appendChild(liveRegion)
    }

    liveRegion.textContent = message

    // 清理公告
    if (announcementTimeoutRef.current) {
      clearTimeout(announcementTimeoutRef.current)
    }

    announcementTimeoutRef.current = setTimeout(() => {
      setState(prev => ({
        ...prev,
        announcements: prev.announcements.filter(a => a !== message)
      }))
    }, 5000)
  }, [enableScreenReaderSupport, announceChanges])

  // 焦点陷阱（用于模态框等）
  const createFocusTrap = React.useCallback((container: HTMLElement) => {
    if (!enableFocusManagement) return () => {}

    const focusableElements = container.querySelectorAll(
      'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault()
            lastElement?.focus()
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault()
            firstElement?.focus()
          }
        }
      }

      if (e.key === 'Escape') {
        // 可以通过回调处理ESC键
        const escapeEvent = new CustomEvent('focustrap:escape', { bubbles: true })
        container.dispatchEvent(escapeEvent)
      }
    }

    container.addEventListener('keydown', handleKeyDown)
    firstElement?.focus()

    return () => {
      container.removeEventListener('keydown', handleKeyDown)
    }
  }, [enableFocusManagement])

  // 跳过链接（用于快速导航）
  const createSkipLink = React.useCallback((targetId: string, label: string = '跳到主内容') => {
    if (!enableKeyboardNavigation) return null

    const skipLink = document.createElement('a')
    skipLink.href = `#${targetId}`
    skipLink.textContent = label
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50'
    
    skipLink.addEventListener('click', (e) => {
      e.preventDefault()
      const target = document.getElementById(targetId)
      if (target) {
        target.focus()
        target.scrollIntoView({ behavior: 'smooth' })
      }
    })

    return skipLink
  }, [enableKeyboardNavigation])

  // 清理函数
  React.useEffect(() => {
    return () => {
      if (announcementTimeoutRef.current) {
        clearTimeout(announcementTimeoutRef.current)
      }
      
      // 清理ARIA live region
      const liveRegion = document.getElementById('accessibility-announcements')
      if (liveRegion) {
        liveRegion.remove()
      }
    }
  }, [])

  return {
    ...state,
    focusNext,
    focusPrevious,
    focusFirst,
    focusLast,
    focusElement,
    announce,
    createFocusTrap,
    createSkipLink,
    updateFocusableElements
  }
}

// 辅助Hook：用于管理ARIA属性
export const useAriaAttributes = () => {
  const setAriaLabel = React.useCallback((element: HTMLElement | null, label: string) => {
    if (element) {
      element.setAttribute('aria-label', label)
    }
  }, [])

  const setAriaDescribedBy = React.useCallback((element: HTMLElement | null, id: string) => {
    if (element) {
      element.setAttribute('aria-describedby', id)
    }
  }, [])

  const setAriaExpanded = React.useCallback((element: HTMLElement | null, expanded: boolean) => {
    if (element) {
      element.setAttribute('aria-expanded', expanded.toString())
    }
  }, [])

  const setAriaHidden = React.useCallback((element: HTMLElement | null, hidden: boolean) => {
    if (element) {
      element.setAttribute('aria-hidden', hidden.toString())
    }
  }, [])

  const setAriaLive = React.useCallback((element: HTMLElement | null, live: 'off' | 'polite' | 'assertive') => {
    if (element) {
      element.setAttribute('aria-live', live)
    }
  }, [])

  return {
    setAriaLabel,
    setAriaDescribedBy,
    setAriaExpanded,
    setAriaHidden,
    setAriaLive
  }
}
