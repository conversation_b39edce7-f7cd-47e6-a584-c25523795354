#!/usr/bin/env python3
"""
错误代码覆盖率测试脚本
验证每个错误代码都有对应的异常类型
"""

import inspect
from typing import Dict, List

from commonlib.exceptions import error_codes, exceptions


def get_all_error_codes() -> Dict[str, List[str]]:
    """获取所有错误代码"""
    error_code_classes = {}

    # 获取所有以"Errors"结尾的类
    for name, obj in inspect.getmembers(error_codes):
        if inspect.isclass(obj) and name.endswith("Errors") and name != "ErrorCode":

            # 获取类中的所有错误代码常量
            error_constants = []
            for attr_name, attr_value in inspect.getmembers(obj):
                if (
                    not attr_name.startswith("_")
                    and hasattr(attr_value, "code")
                    and hasattr(attr_value, "type")
                ):
                    error_constants.append(attr_name)

            if error_constants:
                error_code_classes[name] = error_constants

    return error_code_classes


def get_all_exception_classes() -> Dict[str, str]:
    """获取所有异常类及其使用的错误代码"""
    exception_classes = {}

    # 获取所有继承自APIError的异常类
    for name, obj in inspect.getmembers(exceptions):
        if (
            inspect.isclass(obj)
            and issubclass(obj, exceptions.APIError)
            and obj != exceptions.APIError
        ):

            # 尝试创建实例来获取错误代码
            try:
                # 根据具体的异常类型提供参数
                if name == "MissingFieldError":
                    instance = obj("test_field")
                elif name == "InvalidValueError":
                    instance = obj("test_field", "invalid_value")
                elif name == "FieldTooLongError":
                    instance = obj("test_field", 100, 50)
                elif name == "FieldTooShortError":
                    instance = obj("test_field", 2, 5)
                elif name == "InvalidEmailError":
                    instance = obj("test@invalid")
                elif name == "InvalidPhoneError":
                    instance = obj("invalid_phone")
                elif name == "InvalidDateError":
                    instance = obj("invalid_date")
                elif name == "InvalidJsonError":
                    instance = obj("invalid_json")
                elif name == "SchemaValidationError":
                    instance = obj(["error1", "error2"])
                elif name == "UnsupportedFileTypeError":
                    instance = obj("txt")
                elif name == "FileTooLargeError":
                    instance = obj(1000000, 500000)
                elif "Auth" in name or "Token" in name or "Account" in name:
                    # 认证相关异常
                    instance = obj()
                elif "NotFound" in name:
                    # NotFound相关异常
                    if name == "UserNotFoundError":
                        instance = obj("user123")
                    elif name == "EndpointNotFoundError":
                        instance = obj("/api/test")
                    elif name == "FileNotFoundError":
                        instance = obj("/path/to/file")
                    elif name == "PageNotFoundError":
                        instance = obj("/page/url")
                    else:
                        instance = obj("test_resource", "123")
                elif "Database" in name:
                    # 数据库相关异常
                    if name == "DatabaseConnectionError":
                        instance = obj()
                    elif name == "DatabaseTransactionError":
                        instance = obj()
                    elif name == "DatabaseConstraintError":
                        instance = obj("pk_constraint")
                    elif name == "DatabaseDuplicateKeyError":
                        instance = obj("email", "<EMAIL>")
                    elif name == "DatabaseTimeoutError":
                        instance = obj("SELECT query")
                    else:
                        instance = obj()
                elif name == "ExternalApiRateLimitedError":
                    instance = obj("openai_api")
                elif name == "ExternalInvalidResponseError":
                    instance = obj("openai_api")
                elif name == "ExternalTimeoutError":
                    instance = obj("openai_api", 30)
                elif name == "ExternalAuthenticationFailedError":
                    instance = obj("openai_api")
                elif name == "ExternalServiceError":
                    instance = obj()
                elif name in ["VersionConflictError"]:
                    instance = obj("document", "doc123", "v1", "v2")
                elif name in ["DuplicateResourceError"]:
                    instance = obj("user", "email", "<EMAIL>")
                elif name in ["ApiQuotaExceededError"]:
                    instance = obj("openai_api", 1000)
                elif name in ["ConcurrentLimitExceededError"]:
                    instance = obj(100, 50)
                elif name in ["DocumentNotIndexedError"]:
                    instance = obj("doc123")
                elif name in ["ResourceLockedError"]:
                    instance = obj("document", "doc123")
                elif name in ["QuotaExceededError"]:
                    instance = obj("api_calls", 1000, 500)
                elif name == "RateLimitException":
                    instance = obj("api", 100)
                else:
                    # 默认情况，尝试无参数或单个字符串参数
                    try:
                        instance = obj()
                    except:
                        try:
                            instance = obj("test message")
                        except:
                            print(
                                f"Warning: Could not instantiate {name} with default parameters"
                            )
                            continue

                if hasattr(instance, "error_code") and hasattr(
                    instance.error_code, "type"
                ):
                    exception_classes[name] = instance.error_code.type

            except Exception as e:
                # 对于ExternalAuthenticationFailedError，尝试特殊处理
                if name == "ExternalAuthenticationFailedError":
                    try:
                        instance = obj("test_service")
                        if hasattr(instance, "error_code") and hasattr(
                            instance.error_code, "type"
                        ):
                            exception_classes[name] = instance.error_code.type
                    except Exception as e2:
                        print(
                            f"Warning: Could not instantiate {name} even with special handling: {e2}"
                        )
                        continue
                else:
                    print(f"Warning: Could not instantiate {name}: {e}")
                    continue

    return exception_classes


def analyze_coverage():
    """分析错误代码覆盖率"""
    print("🔍 分析错误代码覆盖率...\n")

    # 获取所有错误代码
    error_codes_dict = get_all_error_codes()
    print(f"📊 发现 {len(error_codes_dict)} 个错误代码类别:")
    for class_name, codes in error_codes_dict.items():
        print(f"  - {class_name}: {len(codes)} 个错误代码")

    # 获取所有异常类
    exception_classes = get_all_exception_classes()
    print(f"\n📊 发现 {len(exception_classes)} 个异常类")

    # 统计所有错误代码
    all_error_codes = set()
    error_code_to_class = {}
    for class_name, codes in error_codes_dict.items():
        for code in codes:
            # 获取错误代码的type
            error_obj = getattr(getattr(error_codes, class_name), code)
            error_type = error_obj.type
            all_error_codes.add(error_type)
            error_code_to_class[error_type] = f"{class_name}.{code}"

    # 统计已覆盖的错误代码
    covered_error_codes = set(exception_classes.values())

    # 找出未覆盖的错误代码
    uncovered_codes = all_error_codes - covered_error_codes

    print(f"\n📈 覆盖率统计:")
    print(f"  总错误代码数: {len(all_error_codes)}")
    print(f"  已覆盖数: {len(covered_error_codes)}")
    print(f"  未覆盖数: {len(uncovered_codes)}")
    print(f"  覆盖率: {len(covered_error_codes)/len(all_error_codes)*100:.1f}%")

    if uncovered_codes:
        print(f"\n❌ 未覆盖的错误代码:")
        for code in sorted(uncovered_codes):
            class_info = error_code_to_class.get(code, "Unknown")
            print(f"  - {code} ({class_info})")
    else:
        print(f"\n✅ 所有错误代码都有对应的异常类型!")

    # 显示异常类型映射
    print(f"\n📋 异常类型映射:")
    for exc_name, error_type in sorted(exception_classes.items()):
        class_info = error_code_to_class.get(error_type, "Unknown")
        print(f"  {exc_name} -> {error_type} ({class_info})")

    return len(uncovered_codes) == 0


def main():
    """主函数"""
    print("🧪 错误代码覆盖率测试\n")

    success = analyze_coverage()

    if success:
        print("\n🎉 测试通过! 所有错误代码都有对应的异常类型")
        return True
    else:
        print("\n❌ 测试失败! 存在未覆盖的错误代码")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
