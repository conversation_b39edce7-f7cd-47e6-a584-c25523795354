from datetime import datetime
from typing import Optional

from domain_common.models.base_model import Base
from domain_common.models.constants import JSONType
from domain_common.models.fields import Fields
from sqlalchemy import (Boolean, DateTime, Index, Integer, String, Text,
                        UniqueConstraint, text)
from sqlalchemy.dialects.postgresql import INET
from sqlalchemy.orm import Mapped, mapped_column

from domain_common.models.mixins import TimestampMixin


class UserSessionHistory(Base,TimestampMixin):
    """用户会话历史模型

    ⚠️ 重要说明：
    - 此模型仅用于存储会话历史记录，用于审计和分析
    - 活跃会话应存储在Redis中，格式如下：

    Redis存储结构:
    Key: "session:{session_id}"
    Value: {
        "user_id": "用户ID",
        "tenant_id": "租户ID",
        "access_token_hash": "访问令牌哈希",
        "refresh_token_hash": "刷新令牌哈希",
        "device_info": {...},
        "ip_address": "***********",
        "user_agent": "浏览器信息",
        "created_at": "2024-01-01T00:00:00Z",
        "last_activity": "2024-01-01T01:00:00Z"
    }
    TTL: 7200秒 (2小时)

    此数据库表用于：
    1. 会话历史审计
    2. 用户登录行为分析
    3. 安全事件调查
    4. 合规性报告
    """

    __tablename__ = "user_session_history"

    # 重写主键为session_id
    session_id: Mapped[str] = mapped_column(String(64), primary_key=True, doc="会话ID")
    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 用户关联字段
    user_id: Mapped[str] = Fields.user(required=True)

    # 设备和网络信息字段
    device_info: Mapped[Optional[JSONType]] = Fields.json_field(
        default={}, doc="设备信息"
    )
    ip_address: Mapped[Optional[str]] = Fields.ip_address()
    user_agent: Mapped[Optional[str]] = Fields.long_text(doc="用户代理")
    login_type: Mapped[str] = Fields.code(max_length=20, doc="登录类型: normal, sso, mfa")


    last_activity_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="最后活动时间"
    )
    ended_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="会话结束时间"
    )
    end_reason: Mapped[Optional[str]] = Fields.code(max_length=50, required=False, doc="结束原因: logout, timeout, forced, expired")
    duration_seconds: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, doc="会话持续时间（秒）"
    )

    __table_args__ = (
        Index("idx_user_session_history_tenant_user", "tenant_id", "user_id"),
        Index("idx_user_session_history_created_at", "created_at"),
        Index("idx_user_session_history_user_created", "user_id", "created_at"),
        Index("idx_user_session_history_ip", "ip_address"),
        Index("idx_user_session_history_end_reason", "end_reason"),
    )

    def __repr__(self) -> str:
        return f"<UserSessionHistory(session_id={self.session_id}, user_id={self.user_id}, tenant_id={self.tenant_id})>"





class UserMFA(Base,TimestampMixin):
    """用户多因子认证模型

    管理用户的多因子认证设置，支持 TOTP、SMS、邮件等方式
    """

    __tablename__ = "user_mfa"

    # 使用BIGSERIAL作为主键
    id: Mapped[int] = Fields.bigint_id(doc="MFA记录ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 用户关联字段
    user_id: Mapped[str] = Fields.user(required=True)

    # MFA配置字段
    mfa_type: Mapped[str] = Fields.code(max_length=20, doc="MFA类型: totp, sms, email")
    secret_key: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, doc="TOTP密钥"
    )
    backup_codes: Mapped[Optional[JSONType]] = Fields.json_field(doc="备用码")
    device_name: Mapped[Optional[str]] = Fields.name(required=False, doc="设备名称")

    # 状态字段
    is_enabled: Mapped[bool] = mapped_column(Boolean, default=False, doc="是否启用")
    verified_at: Mapped[Optional[datetime]] = mapped_column(
        nullable=True, doc="验证时间"
    )

    __table_args__ = (
        UniqueConstraint(
            "tenant_id", "user_id", "mfa_type", name="uq_user_mfa_tenant_user_type"
        ),
        Index("idx_user_mfa_tenant_user", "tenant_id", "user_id"),
        Index("idx_user_mfa_type", "mfa_type"),
        Index("idx_user_mfa_enabled", "is_enabled"),
    )

    def __repr__(self) -> str:
        return f"<UserMFA(id={self.id}, user_id={self.user_id}, mfa_type={self.mfa_type}, tenant_id={self.tenant_id})>"


class VerificationCode(Base,TimestampMixin):
    """验证码模型

    管理各种场景的验证码，支持短信、邮件等多种发送方式
    ⚠️ 注意：验证码也建议存储在Redis中，此处保留数据库模型主要用于历史记录
    """

    __tablename__ = "verification_codes"

    # 重写主键为code_id
    code_id: Mapped[str] = mapped_column(String(64), primary_key=True, doc="验证码ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 用户关联字段（可选，某些验证码可能不关联用户）
    user_id: Mapped[Optional[str]] = mapped_column(
        String(64), nullable=True, doc="用户ID"
    )

    # 验证码基本信息字段
    code_type: Mapped[str] = mapped_column(
        String(20), nullable=False, doc="验证码类型: sms, email, reset_password"
    )
    code_hash: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="验证码哈希值"
    )
    target: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="目标地址（手机号或邮箱）"
    )
    scene: Mapped[str] = mapped_column(String(50), nullable=False, doc="使用场景")

    # 验证控制字段
    attempts: Mapped[int] = mapped_column(Integer, default=0, doc="尝试次数")
    max_attempts: Mapped[int] = mapped_column(Integer, default=5, doc="最大尝试次数")
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False, doc="是否已验证")
    verified_at: Mapped[Optional[datetime]] = mapped_column(
        nullable=True, doc="验证时间"
    )
    expires_at: Mapped[datetime] = mapped_column(nullable=False, doc="过期时间")

    __table_args__ = (
        Index("idx_verification_codes_tenant_user", "tenant_id", "user_id"),
        Index("idx_verification_codes_target", "target"),
        Index("idx_verification_codes_type_scene", "code_type", "scene"),
        Index("idx_verification_codes_expires_at", "expires_at"),
        Index("idx_verification_codes_verified", "is_verified"),
    )

    def __repr__(self) -> str:
        return f"<VerificationCode(code_id={self.code_id}, code_type={self.code_type}, tenant_id={self.tenant_id})>"


class PasswordHistory(Base,TimestampMixin):
    """密码历史模型

    记录用户的密码历史，防止密码重用
    """

    __tablename__ = "password_history"

    # 使用BIGSERIAL作为主键
    id: Mapped[int] = Fields.bigint_id(doc="密码历史记录ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 用户关联字段
    user_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="用户ID")

    # 密码信息字段
    password_hash: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="密码哈希值"
    )
    salt: Mapped[str] = mapped_column(String(64), nullable=False, doc="密码盐值")

    # 时间字段（数据库schema中字段名为created_at）
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(),
        server_default=text("CURRENT_TIMESTAMP"),
        doc="创建时间",
    )

    __table_args__ = (
        Index("idx_password_history_tenant_user", "tenant_id", "user_id"),
        Index("idx_password_history_created_at", "created_at"),
        Index("idx_password_history_user_created", "user_id", "created_at"),
    )

    def __repr__(self) -> str:
        return f"<PasswordHistory(id={self.id}, user_id={self.user_id}, tenant_id={self.tenant_id})>"
