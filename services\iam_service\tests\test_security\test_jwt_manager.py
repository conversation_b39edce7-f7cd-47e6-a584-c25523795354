"""
JWT管理器测试用例
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from security.jwt_manager import JWTManager, TokenPair, TokenPayload
from commonlib.storages.persistence.redis.repository import RedisRepository


class TestJWTManager:
    """JWT管理器测试类"""
    
    @pytest.fixture
    async def redis_repo(self):
        """模拟Redis仓库"""
        mock_repo = AsyncMock(spec=RedisRepository)
        return mock_repo
    
    @pytest.fixture
    async def jwt_manager(self, redis_repo):
        """JWT管理器实例"""
        return JWTManager(
            redis_repo=redis_repo,
            access_token_expire_minutes=30,
            refresh_token_expire_days=7
        )
    
    @pytest.fixture
    def sample_user_data(self):
        """示例用户数据"""
        return {
            "user_id": "user_123",
            "tenant_id": "tenant_456",
            "session_id": "session_789",
            "roles": ["admin", "user"],
            "permissions": ["user:read", "user:write", "role:read"],
            "device_fingerprint": "device_abc123",
            "ip_address": "*************"
        }
    
    @pytest.mark.asyncio
    async def test_generate_token_pair_success(self, jwt_manager, redis_repo, sample_user_data):
        """测试成功生成令牌对"""
        # 模拟Redis操作
        redis_repo.set.return_value = None
        
        # 生成令牌对
        token_pair = await jwt_manager.generate_token_pair(
            user_id=sample_user_data["user_id"],
            tenant_id=sample_user_data["tenant_id"],
            session_id=sample_user_data["session_id"],
            roles=sample_user_data["roles"],
            permissions=sample_user_data["permissions"],
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )
        
        # 验证结果
        assert isinstance(token_pair, TokenPair)
        assert token_pair.access_token is not None
        assert token_pair.refresh_token is not None
        assert token_pair.token_type == "Bearer"
        assert token_pair.expires_in == 30 * 60  # 30分钟
        
        # 验证Redis存储被调用
        redis_repo.set.assert_called()
    
    @pytest.mark.asyncio
    async def test_verify_access_token_success(self, jwt_manager, redis_repo, sample_user_data):
        """测试成功验证访问令牌"""
        # 先生成令牌
        redis_repo.set.return_value = None
        redis_repo.get.return_value = {"session_id": sample_user_data["session_id"]}
        
        token_pair = await jwt_manager.generate_token_pair(
            user_id=sample_user_data["user_id"],
            tenant_id=sample_user_data["tenant_id"],
            session_id=sample_user_data["session_id"],
            roles=sample_user_data["roles"],
            permissions=sample_user_data["permissions"],
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )
        
        # 验证令牌
        token_payload = await jwt_manager.verify_access_token(token_pair.access_token)
        
        # 验证结果
        assert isinstance(token_payload, TokenPayload)
        assert token_payload.user_id == sample_user_data["user_id"]
        assert token_payload.tenant_id == sample_user_data["tenant_id"]
        assert token_payload.session_id == sample_user_data["session_id"]
        assert token_payload.roles == sample_user_data["roles"]
        assert token_payload.device_fingerprint == sample_user_data["device_fingerprint"]
        assert token_payload.ip_address == sample_user_data["ip_address"]
    
    @pytest.mark.asyncio
    async def test_verify_access_token_invalid(self, jwt_manager, redis_repo):
        """测试验证无效访问令牌"""
        # 验证无效令牌
        token_payload = await jwt_manager.verify_access_token("invalid_token")
        
        # 验证结果
        assert token_payload is None
    
    @pytest.mark.asyncio
    async def test_verify_access_token_blacklisted(self, jwt_manager, redis_repo, sample_user_data):
        """测试验证被黑名单的访问令牌"""
        # 生成令牌
        redis_repo.set.return_value = None
        redis_repo.get.return_value = {"session_id": sample_user_data["session_id"]}
        
        token_pair = await jwt_manager.generate_token_pair(
            user_id=sample_user_data["user_id"],
            tenant_id=sample_user_data["tenant_id"],
            session_id=sample_user_data["session_id"],
            roles=sample_user_data["roles"],
            permissions=sample_user_data["permissions"]
        )
        
        # 撤销令牌（添加到黑名单）
        await jwt_manager.revoke_token(token_pair.access_token, "access")
        
        # 模拟黑名单检查返回True
        with patch.object(jwt_manager, '_is_token_blacklisted', return_value=True):
            token_payload = await jwt_manager.verify_access_token(token_pair.access_token)
        
        # 验证结果
        assert token_payload is None
    
    @pytest.mark.asyncio
    async def test_refresh_token_pair_success(self, jwt_manager, redis_repo, sample_user_data):
        """测试成功刷新令牌对"""
        # 模拟Redis操作
        redis_repo.set.return_value = None
        redis_repo.get.return_value = {"session_id": sample_user_data["session_id"]}
        redis_repo.keys.return_value = ["refresh_token:token_123"]
        redis_repo.delete.return_value = None
        redis_repo.lpush.return_value = None
        redis_repo.ltrim.return_value = None
        
        # 生成初始令牌对
        original_token_pair = await jwt_manager.generate_token_pair(
            user_id=sample_user_data["user_id"],
            tenant_id=sample_user_data["tenant_id"],
            session_id=sample_user_data["session_id"],
            roles=sample_user_data["roles"],
            permissions=sample_user_data["permissions"],
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )
        
        # 模拟刷新令牌验证成功
        mock_token_info = {
            "token_id": "token_123",
            "user_id": sample_user_data["user_id"],
            "tenant_id": sample_user_data["tenant_id"],
            "session_id": sample_user_data["session_id"],
            "device_fingerprint": sample_user_data["device_fingerprint"],
            "ip_address": sample_user_data["ip_address"],
            "expires_at": (datetime.now() + timedelta(days=7)).isoformat()
        }
        
        with patch.object(jwt_manager, '_verify_refresh_token', return_value=mock_token_info):
            # 刷新令牌对
            new_token_pair = await jwt_manager.refresh_token_pair(
                original_token_pair.refresh_token,
                device_fingerprint=sample_user_data["device_fingerprint"],
                ip_address=sample_user_data["ip_address"]
            )
        
        # 验证结果
        assert isinstance(new_token_pair, TokenPair)
        assert new_token_pair.access_token != original_token_pair.access_token
        assert new_token_pair.refresh_token != original_token_pair.refresh_token
    
    @pytest.mark.asyncio
    async def test_refresh_token_pair_device_mismatch(self, jwt_manager, redis_repo, sample_user_data):
        """测试设备指纹不匹配的刷新令牌"""
        # 模拟刷新令牌验证成功但设备指纹不匹配
        mock_token_info = {
            "token_id": "token_123",
            "user_id": sample_user_data["user_id"],
            "tenant_id": sample_user_data["tenant_id"],
            "session_id": sample_user_data["session_id"],
            "device_fingerprint": "different_device",
            "ip_address": sample_user_data["ip_address"],
            "expires_at": (datetime.now() + timedelta(days=7)).isoformat()
        }
        
        with patch.object(jwt_manager, '_verify_refresh_token', return_value=mock_token_info):
            with patch.object(jwt_manager, '_revoke_refresh_token', return_value=True):
                # 尝试刷新令牌（设备指纹不匹配）
                new_token_pair = await jwt_manager.refresh_token_pair(
                    "refresh_token",
                    device_fingerprint=sample_user_data["device_fingerprint"],
                    ip_address=sample_user_data["ip_address"]
                )
        
        # 验证结果
        assert new_token_pair is None
    
    @pytest.mark.asyncio
    async def test_revoke_access_token(self, jwt_manager, redis_repo, sample_user_data):
        """测试撤销访问令牌"""
        # 生成令牌
        redis_repo.set.return_value = None
        redis_repo.get.return_value = {"session_id": sample_user_data["session_id"]}
        
        token_pair = await jwt_manager.generate_token_pair(
            user_id=sample_user_data["user_id"],
            tenant_id=sample_user_data["tenant_id"],
            session_id=sample_user_data["session_id"],
            roles=sample_user_data["roles"],
            permissions=sample_user_data["permissions"]
        )
        
        # 撤销访问令牌
        result = await jwt_manager.revoke_token(token_pair.access_token, "access")
        
        # 验证结果
        assert result is True
        # 验证令牌被添加到黑名单
        redis_repo.set.assert_called()
    
    @pytest.mark.asyncio
    async def test_revoke_refresh_token(self, jwt_manager, redis_repo, sample_user_data):
        """测试撤销刷新令牌"""
        # 模拟Redis操作
        redis_repo.keys.return_value = ["refresh_token:token_123"]
        redis_repo.get.return_value = {
            "token_id": "token_123",
            "user_id": sample_user_data["user_id"],
            "tenant_id": sample_user_data["tenant_id"],
            "session_id": sample_user_data["session_id"]
        }
        redis_repo.delete.return_value = None
        
        # 撤销刷新令牌
        result = await jwt_manager.revoke_token("refresh_token", "refresh")
        
        # 验证结果
        assert result is True
        redis_repo.delete.assert_called()
    
    @pytest.mark.asyncio
    async def test_get_token_stats(self, jwt_manager, redis_repo):
        """测试获取令牌统计信息"""
        # 模拟Redis操作
        redis_repo.keys.side_effect = [
            ["refresh_token:1", "refresh_token:2"],  # 活跃刷新令牌
            ["blacklist_token:1"]  # 黑名单令牌
        ]
        redis_repo.llen.return_value = 5  # 错误日志数量
        
        # 获取统计信息
        stats = await jwt_manager.get_token_stats()
        
        # 验证结果
        assert stats["active_refresh_tokens"] == 2
        assert stats["blacklisted_tokens"] == 1
        assert stats["recent_errors"] == 5
        assert "timestamp" in stats
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_tokens(self, jwt_manager, redis_repo):
        """测试清理过期令牌"""
        # 模拟过期的刷新令牌
        expired_token_info = {
            "token_id": "expired_token",
            "expires_at": (datetime.now() - timedelta(days=1)).isoformat()
        }
        
        valid_token_info = {
            "token_id": "valid_token",
            "expires_at": (datetime.now() + timedelta(days=1)).isoformat()
        }
        
        redis_repo.keys.side_effect = [
            ["refresh_token:expired", "refresh_token:valid"],  # 刷新令牌
            ["blacklist_token:1"]  # 黑名单令牌
        ]
        redis_repo.get.side_effect = [expired_token_info, valid_token_info]
        redis_repo.delete.return_value = None
        
        # 清理过期令牌
        result = await jwt_manager.cleanup_expired_tokens()
        
        # 验证结果
        assert result["cleaned_refresh_tokens"] == 1
        assert result["remaining_blacklist_tokens"] == 1
        redis_repo.delete.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_permissions_hash_calculation(self, jwt_manager):
        """测试权限哈希计算"""
        permissions1 = ["user:read", "user:write", "role:read"]
        permissions2 = ["role:read", "user:write", "user:read"]  # 相同权限，不同顺序
        permissions3 = ["user:read", "user:write"]  # 不同权限
        
        hash1 = jwt_manager._calculate_permissions_hash(permissions1)
        hash2 = jwt_manager._calculate_permissions_hash(permissions2)
        hash3 = jwt_manager._calculate_permissions_hash(permissions3)
        
        # 验证结果
        assert hash1 == hash2  # 相同权限应该产生相同哈希
        assert hash1 != hash3  # 不同权限应该产生不同哈希
        assert len(hash1) == 16  # 哈希长度应该是16位
    
    @pytest.mark.asyncio
    async def test_generate_refresh_token(self, jwt_manager):
        """测试生成刷新令牌"""
        token_id = "token_123"
        user_id = "user_456"
        tenant_id = "tenant_789"
        session_id = "session_abc"
        
        refresh_token = jwt_manager._generate_refresh_token(
            token_id, user_id, tenant_id, session_id
        )
        
        # 验证结果
        assert isinstance(refresh_token, str)
        assert len(refresh_token) == 64  # SHA256哈希的十六进制长度
        
        # 相同参数应该产生相同的令牌
        refresh_token2 = jwt_manager._generate_refresh_token(
            token_id, user_id, tenant_id, session_id
        )
        assert refresh_token == refresh_token2
    
    def test_init_keys_generation(self, redis_repo):
        """测试密钥对生成"""
        # 不提供密钥，应该自动生成
        jwt_manager = JWTManager(redis_repo=redis_repo)
        
        # 验证密钥存在
        assert jwt_manager.private_key is not None
        assert jwt_manager.public_key is not None
        assert "BEGIN PRIVATE KEY" in jwt_manager.private_key
        assert "BEGIN PUBLIC KEY" in jwt_manager.public_key
    
    def test_init_with_provided_keys(self, redis_repo):
        """测试使用提供的密钥初始化"""
        private_key = "test_private_key"
        public_key = "test_public_key"
        
        jwt_manager = JWTManager(
            redis_repo=redis_repo,
            private_key=private_key,
            public_key=public_key
        )
        
        # 验证使用了提供的密钥
        assert jwt_manager.private_key == private_key
        assert jwt_manager.public_key == public_key
