/**
 * @file 认证相关类型定义
 * @description 认证模块的TypeScript类型定义
 */

// 登录方式枚举
export type LoginType = 'email' | 'phone' | 'username'

// 认证状态枚举
export type AuthStatus = 'idle' | 'loading' | 'authenticated' | 'unauthenticated' | 'error'

// 表单状态枚举
export type FormStatus = 'idle' | 'submitting' | 'success' | 'error'

// 密码强度等级
export type PasswordStrength = 'weak' | 'fair' | 'good' | 'strong'

// 表单模式
export type AuthFormMode = 'login' | 'register' | 'forgot-password' | 'reset-password'

// 布局模式
export type AuthLayoutMode = 'split' | 'centered'

// 登录请求接口
export interface LoginRequest {
  tenant_id: string
  login_type: LoginType
  identifier: string
  credential: string
  remember_me?: boolean
}

// 登录响应接口
export interface LoginResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  user: UserInfo
  tenant: TenantInfo
}

// 注册请求接口
export interface RegisterRequest {
  tenant_id: string
  email: string
  password: string
  name: string
  phone?: string
  invitation_code?: string
}

// 注册响应接口
export interface RegisterResponse {
  user_id: string
  email: string
  status: 'pending' | 'active'
  activation_required: boolean
}

// 忘记密码请求接口
export interface ForgotPasswordRequest {
  tenant_id: string
  email: string
}

// 重置密码请求接口
export interface ResetPasswordRequest {
  token: string
  password: string
  confirm_password: string
}

// 用户信息接口
export interface UserInfo {
  id: string
  email: string
  name: string
  avatar?: string
  phone?: string
  status: 'active' | 'inactive' | 'pending'
  roles: string[]
  permissions: string[]
  last_login?: string
  created_at: string
  updated_at: string
}

// 租户信息接口
export interface TenantInfo {
  id: string
  name: string
  display_name: string
  logo?: string
  domain?: string
  theme?: TenantTheme
  settings: TenantSettings
  status: 'active' | 'inactive' | 'suspended'
  created_at: string
  updated_at: string
}

// 租户主题接口
export interface TenantTheme {
  primary_color: string
  secondary_color: string
  logo_url?: string
  background_image?: string
  custom_css?: string
}

// 租户设置接口
export interface TenantSettings {
  allow_registration: boolean
  require_email_verification: boolean
  password_policy: PasswordPolicy
  session_timeout: number
  max_login_attempts: number
}

// 密码策略接口
export interface PasswordPolicy {
  min_length: number
  require_uppercase: boolean
  require_lowercase: boolean
  require_numbers: boolean
  require_symbols: boolean
  max_age_days?: number
}

// 表单验证错误接口
export interface FormValidationError {
  field: string
  message: string
  code?: string
}

// 认证错误接口
export interface AuthError {
  code: string
  message: string
  details?: Record<string, any>
}

// 密码强度检查结果
export interface PasswordStrengthResult {
  strength: PasswordStrength
  score: number
  feedback: string[]
  requirements: PasswordRequirement[]
}

// 密码要求接口
export interface PasswordRequirement {
  key: string
  label: string
  satisfied: boolean
  required: boolean
}

// 表单字段状态
export interface FieldState {
  value: string
  error?: string
  touched: boolean
  dirty: boolean
}

// 认证表单状态
export interface AuthFormState {
  mode: AuthFormMode
  status: FormStatus
  fields: Record<string, FieldState>
  errors: FormValidationError[]
  isValid: boolean
}

// 租户检测结果
export interface TenantDetectionResult {
  tenant?: TenantInfo
  suggestions: TenantInfo[]
  auto_detected: boolean
}

// 会话信息接口
export interface SessionInfo {
  id: string
  user_id: string
  tenant_id: string
  device_info: DeviceInfo
  ip_address: string
  user_agent: string
  created_at: string
  last_activity: string
  expires_at: string
}

// 设备信息接口
export interface DeviceInfo {
  type: 'desktop' | 'mobile' | 'tablet'
  os: string
  browser: string
  location?: string
}

// 认证事件接口
export interface AuthEvent {
  type: 'login' | 'logout' | 'register' | 'password_reset' | 'session_expired'
  timestamp: string
  user_id?: string
  tenant_id?: string
  details?: Record<string, any>
}
