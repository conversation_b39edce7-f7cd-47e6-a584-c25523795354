import inspect
import json
from dataclasses import dataclass
from functools import wraps
from threading import RLock
from typing import Any, Callable, Dict, TypeVar, Union

from commonlib.core.logging.tsif_logging import app_logger
from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.utils.keys_serialize_utils import Safe<PERSON>ache<PERSON>eyGenerator
from domain_common.interface.infra_redis.scripts.context import \
    SCRIPT_CACHE_SET_NX
from domain_common.interface.infra_redis.scripts.script import \
    RedisScriptManager

T = TypeVar("T")


@dataclass
class CacheConfig:
    """缓存配置项"""

    ttl: int = 3600  # 默认1小时
    key_prefix: str = "injectors"
    need_cache: bool = True
    need_hash: bool = True  # 是否将key转hash
    ignore_errors: bool = True  # 是否忽略Redis错误
    use_script: bool = True  # 是否使用Lua脚本
    track_execution: bool = True  # 是否跟踪执行次数


class JsonSerializer:
    """Pickle序列化方案（注意安全风险）"""

    @staticmethod
    def serialize(value: Any) -> str:
        return json.dumps(value, ensure_ascii=False)

    @staticmethod
    def deserialize(data: str) -> Any:
        return json.loads(data)


class RedisCacheDecorator:
    """增强版Redis缓存装饰器，支持执行次数统计"""

    EXECUTION_STATS_HASH = "func_execution_stats"  # 存储执行统计的哈希表名

    def __init__(self, script_manager: RedisScriptManager):
        self._lock = RLock()
        self.redis_repo: RedisRepository = script_manager.redis_repo
        self._script_manager: RedisScriptManager | None = script_manager

    @classmethod
    def _normalize_config(cls, config: Union[CacheConfig, Dict, None]) -> CacheConfig:
        """统一配置格式处理"""
        if isinstance(config, dict):
            config = CacheConfig(**config)
        config = config or CacheConfig()
        return config

    async def _increment_execution_count(self, func_name: str, is_cache_hit: bool):
        """增加函数执行次数统计"""
        if not self.redis_repo:
            return

        field_hits = f"{func_name}:hits"
        field_misses = f"{func_name}:misses"

        with self._lock:
            if is_cache_hit:
                await self.redis_repo.hincrby(self.EXECUTION_STATS_HASH, field_hits, 1)
            else:
                await self.redis_repo.hincrby(
                    self.EXECUTION_STATS_HASH, field_misses, 1
                )

    async def get_execution_stats(self, func_name: str = None) -> Dict[str, int]:
        """获取执行统计信息"""
        if not self.redis_repo:
            return {}

        if func_name:
            hits = (
                await self.redis_repo.hget(
                    self.EXECUTION_STATS_HASH, f"{func_name}:hits"
                )
                or 0
            )
            misses = (
                await self.redis_repo.hget(
                    self.EXECUTION_STATS_HASH, f"{func_name}:misses"
                )
                or 0
            )
            return {
                "hits": int(hits),
                "misses": int(misses),
                "total": int(hits) + int(misses),
                "hit_rate": (
                    float(hits) / (int(hits) + int(misses))
                    if (int(hits) + int(misses)) > 0
                    else 0.0
                ),
            }
        else:
            stats = await self.redis_repo.hgetall(self.EXECUTION_STATS_HASH)
            return {k.decode(): int(v) for k, v in stats.items()} if stats else {}

    def __call__(self, config: Union[CacheConfig, dict] = None):
        config = self._normalize_config(config)

        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> T:
                if not config.need_cache:
                    # 不需要缓存
                    if inspect.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                cache_key = SafeCacheKeyGenerator.make_key(
                    func, config.need_hash, args, **kwargs
                )
                if config.key_prefix:
                    cache_key = f"{config.key_prefix}:{cache_key}"
                try:
                    # 尝试从缓存读取
                    cached: str = await self.redis_repo.get(cache_key)
                    if cached is not None:
                        app_logger.debug(f"Cache hit: {cache_key}")
                        if config.track_execution:
                            await self._increment_execution_count(
                                func.__name__, is_cache_hit=True
                            )
                        return JsonSerializer.deserialize(cached)

                    # 缓存未命中，执行原函数
                    if inspect.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)
                    # 写入缓存
                    await self._set_cache(cache_key, result, config.ttl)

                    if config.track_execution:
                        await self._increment_execution_count(
                            func.__name__, is_cache_hit=False
                        )
                    return result
                except TypeError as e:
                    app_logger.error(f"Cache operation failed: {e}", exception=True)
                    raise
                except Exception as e:
                    app_logger.error(f"Cache operation failed: {e}", exception=True)
                    if not config.ignore_errors:
                        raise
                    if inspect.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)

            return wrapper

        return decorator

    async def _set_cache(self, key: str, value: Any, ttl: int) -> None:
        """使用最优策略设置缓存"""
        key = self.redis_repo.make_key(key)
        value: str = JsonSerializer.serialize(value)
        try:
            if self._script_manager and ttl and ttl > 0:
                # 使用Lua脚本原子化操作
                await self._script_manager.execute(
                    script_name=SCRIPT_CACHE_SET_NX,
                    keys=[key],
                    args=[value, ttl],
                    raise_on_error=False,
                )
            else:
                # 普通set操作
                if ttl and ttl > 0:
                    await self.redis_repo.set(key, value, ttl)
                else:
                    await self.redis_repo.set(key, value, ttl=None)
        except Exception as e:
            app_logger.error(f"Failed to set injectors: {e}", exception=True)
