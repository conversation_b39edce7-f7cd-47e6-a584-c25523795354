from dataclasses import dataclass
from datetime import datetime
from threading import RLock
from typing import Any, Dict, List, Optional

from commonlib.core.logging.tsif_logging import app_logger
from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.utils.singleton import SingletonMeta
from domain_common.interface.infra_redis.scripts.context import DEFAULT_SCRIPTS


# 脚本数据模型
@dataclass
class RedisScript:
    name: str
    version: str
    content: str
    sha: Optional[str] = None
    last_load_failed: bool = False
    last_load_time: Optional[datetime] = None
    load_retry_interval: int = 60  # 秒


# Redis 脚本上下文注册器（单例）
class RedisScriptContext(metaclass=SingletonMeta):
    def __init__(self):
        self._lock = RLock()
        self._scripts: Dict[str, RedisScript] = {}
        for name, script in DEFAULT_SCRIPTS.items():
            self.register_script(
                name=name,
                content=script["content"],
                version=script.get("version", "1.0"),
            )

    def register_script(self, name: str, content: str, version: str = "1.0") -> None:
        with self._lock:
            if name not in self._scripts:
                self._scripts[name] = RedisScript(
                    name=name, content=content.strip(), version=version
                )

    def get_script(self, name: str) -> RedisScript:
        script = self._scripts.get(name)
        if not script:
            raise ValueError(f"[RedisScript] Script '{name}' not registered.")
        return script


# 脚本加载与执行器
class RedisScriptManager:
    """Redis 脚本执行管理器（支持 SHA 缓存、eval 降级、自恢复加载）"""

    def __init__(self, redis_repo: RedisRepository):
        self.redis_repo = redis_repo
        self._context = RedisScriptContext()

    def register_script(self, name: str, content: str, version: str = "1.0") -> None:
        self._context.register_script(name, content, version)

    async def safe_load_with_name(self, script_name: str) -> None:
        script = self._context.get_script(script_name)
        await self._safe_load(script)

    async def _safe_load(self, script: RedisScript) -> None:
        now = datetime.now()

        if script.last_load_failed and script.last_load_time:
            elapsed = (now - script.last_load_time).total_seconds()
            if elapsed < script.load_retry_interval:
                app_logger.debug(
                    f"Skip reload for script '{script.name}', cooldown not passed."
                )
                return

        try:
            script.sha = await self.redis_repo.script_load(script.content)
            script.last_load_failed = False
            script.last_load_time = now
            app_logger.info(
                f"[RedisScript] Loaded '{script.name}' (SHA: {script.sha[:8]}...)"
            )
        except Exception as e:
            script.sha = None
            script.last_load_failed = True
            script.last_load_time = now
            app_logger.error(
                f"[RedisScript] Failed to load '{script.name}': {e}", exception=True
            )

    async def execute(
        self,
        script_name: str,
        keys: List[str],
        args: List[Any],
        raise_on_error: bool = False,
    ) -> Any:
        """
        执行指定名称的 Redis 脚本，支持 SHA fallback 与自动加载重试。
        """
        script = self._context.get_script(script_name)

        try:
            # 优先尝试使用 SHA 调用
            if script.sha:
                try:
                    return await self.redis_repo.evalsha(script.sha, keys, args)
                except Exception as e:
                    if "NOSCRIPT" not in str(e):
                        raise
                    app_logger.warning(
                        f"[RedisScript] SHA missing for '{script_name}', reloading..."
                    )

            # 尝试重新加载
            await self._safe_load(script)

            if script.sha:
                try:
                    return await self.redis_repo.evalsha(script.sha, keys, args)
                except Exception as e:
                    app_logger.warning(
                        f"[RedisScript] Reloaded SHA call failed for '{script_name}': {e}"
                    )

            # 最后尝试 eval 原始内容
            return await self.redis_repo.eval(script.content, keys, args)

        except Exception as e:
            app_logger.error(
                f"[RedisScript] Execution failed for '{script_name}': {e}",
                exception=True,
            )
            if raise_on_error:
                raise
            return None
