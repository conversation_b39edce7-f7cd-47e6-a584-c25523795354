/**
 * @file Badge组件
 * @description 徽章组件，用于显示状态、标签等信息
 */

import React from 'react'
import clsx from 'clsx'

export interface BadgeProps {
  children: React.ReactNode
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  size?: 'sm' | 'md' | 'lg'
  shape?: 'rounded' | 'pill'
  className?: string
  icon?: React.ReactNode
  dot?: boolean
}

const variantClasses = {
  default: 'bg-gray-100 text-gray-800 border-gray-200',
  primary: 'bg-blue-100 text-blue-800 border-blue-200',
  secondary: 'bg-gray-100 text-gray-600 border-gray-200',
  success: 'bg-green-100 text-green-800 border-green-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  error: 'bg-red-100 text-red-800 border-red-200',
  info: 'bg-cyan-100 text-cyan-800 border-cyan-200'
}

const sizeClasses = {
  sm: 'px-2 py-0.5 text-xs',
  md: 'px-2.5 py-1 text-sm',
  lg: 'px-3 py-1.5 text-base'
}

const shapeClasses = {
  rounded: 'rounded-md',
  pill: 'rounded-full'
}

const dotClasses = {
  default: 'bg-gray-400',
  primary: 'bg-blue-500',
  secondary: 'bg-gray-400',
  success: 'bg-green-500',
  warning: 'bg-yellow-500',
  error: 'bg-red-500',
  info: 'bg-cyan-500'
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  shape = 'rounded',
  className,
  icon,
  dot = false
}) => {
  const badgeClasses = clsx(
    'inline-flex items-center font-medium border',
    variantClasses[variant],
    sizeClasses[size],
    shapeClasses[shape],
    className
  )

  return (
    <span className={badgeClasses}>
      {/* 点状指示器 */}
      {dot && (
        <span
          className={clsx(
            'w-2 h-2 rounded-full mr-1.5',
            dotClasses[variant]
          )}
        />
      )}
      
      {/* 图标 */}
      {icon && (
        <span className="mr-1 -ml-0.5">
          {icon}
        </span>
      )}
      
      {/* 内容 */}
      {children}
    </span>
  )
}
