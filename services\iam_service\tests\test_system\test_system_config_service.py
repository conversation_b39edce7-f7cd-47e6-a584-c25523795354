"""
系统配置服务测试

测试系统配置服务的各项功能
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

from services.iam_service.services.system_config_service import SystemConfigService
from domain_common.models.iam_models import (
    Tenant, SystemConfig, TenantConfig, AuditLog
)
from commonlib.exceptions.exceptions import (
    ValidationError, NotFoundError, BusinessError
)


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟Redis仓库"""
    redis_repo = AsyncMock()
    return redis_repo


@pytest.fixture
def system_config_service(mock_session, mock_redis_repo):
    """创建系统配置服务实例"""
    return SystemConfigService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        tenant_model=Tenant,
        system_config_model=SystemConfig,
        tenant_config_model=TenantConfig,
        audit_log_model=AuditLog,
        encryption_key="test_encryption_key_32_bytes_long"
    )


class TestSystemConfigService:
    """系统配置服务测试类"""

    @pytest.mark.asyncio
    async def test_set_global_config_success(self, system_config_service, mock_session):
        """测试成功设置全局配置"""
        # 模拟配置不存在（新建）
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        # 执行测试
        result = await system_config_service.set_config(
            tenant_id=None,
            config_key="test.setting",
            config_value="test_value",
            description="测试配置",
            is_sensitive=False,
            category="general"
        )
        
        # 验证结果
        assert result["config_key"] == "test.setting"
        assert result["new_value"] == "test_value"
        assert result["previous_value"] is None
        assert "config_id" in result
        assert "updated_at" in result
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_set_tenant_config_success(self, system_config_service, mock_session):
        """测试成功设置租户配置"""
        # 模拟租户存在
        mock_tenant = MagicMock()
        mock_tenant.tenant_id = "tenant_123"
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            mock_tenant,  # 租户查询
            None  # 配置不存在
        ]
        
        # 执行测试
        result = await system_config_service.set_config(
            tenant_id="tenant_123",
            config_key="tenant.setting",
            config_value={"key": "value"},
            description="租户配置",
            is_sensitive=False,
            category="security"
        )
        
        # 验证结果
        assert result["config_key"] == "tenant.setting"
        assert result["new_value"] == {"key": "value"}
        assert "config_id" in result
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_set_config_update_existing(self, system_config_service, mock_session):
        """测试更新现有配置"""
        # 模拟现有配置
        mock_config = MagicMock()
        mock_config.config_id = "config_123"
        mock_config.config_value = "old_value"
        mock_config.is_encrypted = False
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_config
        
        # 执行测试
        result = await system_config_service.set_config(
            tenant_id=None,
            config_key="existing.setting",
            config_value="new_value",
            description="更新的配置",
            is_sensitive=False,
            category="general"
        )
        
        # 验证结果
        assert result["config_key"] == "existing.setting"
        assert result["new_value"] == "new_value"
        assert result["previous_value"] == "old_value"
        
        # 验证配置被更新
        assert mock_config.config_value == "new_value"
        assert mock_config.description == "更新的配置"

    @pytest.mark.asyncio
    async def test_set_config_invalid_category(self, system_config_service):
        """测试设置配置时使用无效分类"""
        with pytest.raises(ValidationError, match="无效的配置分类"):
            await system_config_service.set_config(
                tenant_id=None,
                config_key="test.setting",
                config_value="test_value",
                category="invalid_category"
            )

    @pytest.mark.asyncio
    async def test_set_config_tenant_not_found(self, system_config_service, mock_session):
        """测试设置配置时租户不存在"""
        # 模拟租户不存在
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        with pytest.raises(NotFoundError, match="租户不存在"):
            await system_config_service.set_config(
                tenant_id="nonexistent_tenant",
                config_key="test.setting",
                config_value="test_value"
            )

    @pytest.mark.asyncio
    async def test_get_config_success(self, system_config_service, mock_redis_repo, mock_session):
        """测试成功获取配置"""
        # 模拟缓存未命中
        mock_redis_repo.get.return_value = None
        
        # 模拟租户配置存在
        mock_config = MagicMock()
        mock_config.config_value = "tenant_value"
        mock_config.is_encrypted = False
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_config
        
        # 执行测试
        result = await system_config_service.get_config(
            tenant_id="tenant_123",
            config_key="test.setting",
            include_inherited=True
        )
        
        # 验证结果
        assert result["config_key"] == "test.setting"
        assert result["config_value"] == "tenant_value"
        assert result["source"] == "tenant"
        assert result["is_inherited"] is False
        
        # 验证缓存设置
        mock_redis_repo.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_config_from_cache(self, system_config_service, mock_redis_repo):
        """测试从缓存获取配置"""
        # 模拟缓存命中
        cached_config = {
            "config_key": "test.setting",
            "config_value": "cached_value",
            "source": "tenant",
            "is_inherited": False
        }
        mock_redis_repo.get.return_value = cached_config
        
        # 执行测试
        result = await system_config_service.get_config(
            tenant_id="tenant_123",
            config_key="test.setting"
        )
        
        # 验证结果
        assert result == cached_config

    @pytest.mark.asyncio
    async def test_get_config_fallback_to_default(self, system_config_service, mock_redis_repo, mock_session):
        """测试获取配置时回退到默认值"""
        # 模拟缓存未命中
        mock_redis_repo.get.return_value = None
        
        # 模拟租户和全局配置都不存在
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        # 执行测试（使用默认配置中存在的键）
        result = await system_config_service.get_config(
            tenant_id="tenant_123",
            config_key="password_policy.min_length",
            include_inherited=True
        )
        
        # 验证结果
        assert result["config_key"] == "password_policy.min_length"
        assert result["config_value"] == 8  # 默认值
        assert result["source"] == "default"
        assert result["is_inherited"] is True

    @pytest.mark.asyncio
    async def test_get_config_not_found(self, system_config_service, mock_redis_repo, mock_session):
        """测试获取不存在的配置"""
        # 模拟缓存未命中
        mock_redis_repo.get.return_value = None
        
        # 模拟配置不存在
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        with pytest.raises(NotFoundError, match="配置不存在"):
            await system_config_service.get_config(
                tenant_id="tenant_123",
                config_key="nonexistent.setting"
            )

    @pytest.mark.asyncio
    async def test_query_configs_success(self, system_config_service, mock_session):
        """测试成功查询配置列表"""
        # 模拟租户配置查询结果
        mock_config = MagicMock()
        mock_config.config_id = "config_123"
        mock_config.tenant_id = "tenant_123"
        mock_config.config_key = "test.setting"
        mock_config.config_value = "test_value"
        mock_config.description = "测试配置"
        mock_config.is_encrypted = False
        mock_config.category = "general"
        mock_config.created_at = datetime.utcnow()
        mock_config.updated_at = None
        
        mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_config]
        
        # 执行测试
        result = await system_config_service.query_configs(
            tenant_id="tenant_123",
            category="general",
            page=1,
            page_size=10
        )
        
        # 验证结果
        assert "configs" in result
        assert "total" in result
        assert "page" in result
        assert "page_size" in result
        assert "has_next" in result
        
        # 验证配置数据
        configs = result["configs"]
        assert len(configs) >= 1
        config = configs[0]
        assert config["config_key"] == "test.setting"
        assert config["config_value"] == "test_value"
        assert config["category"] == "general"

    @pytest.mark.asyncio
    async def test_delete_config_success(self, system_config_service, mock_session):
        """测试成功删除配置"""
        # 模拟配置存在
        mock_config = MagicMock()
        mock_config.config_id = "config_123"
        mock_config.config_key = "deletable.setting"
        mock_config.category = "general"
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_config
        
        # 执行测试
        result = await system_config_service.delete_config(
            tenant_id=None,
            config_key="deletable.setting"
        )
        
        # 验证结果
        assert result["config_key"] == "deletable.setting"
        assert "deleted_at" in result
        
        # 验证数据库操作
        mock_session.delete.assert_called_once_with(mock_config)
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_config_not_found(self, system_config_service, mock_session):
        """测试删除不存在的配置"""
        # 模拟配置不存在
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        with pytest.raises(NotFoundError, match="配置不存在"):
            await system_config_service.delete_config(
                tenant_id=None,
                config_key="nonexistent.setting"
            )

    @pytest.mark.asyncio
    async def test_delete_config_system_core(self, system_config_service, mock_session):
        """测试删除系统核心配置"""
        # 模拟系统核心配置存在
        mock_config = MagicMock()
        mock_config.config_key = "password_policy.min_length"
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_config
        
        with pytest.raises(ValidationError, match="系统核心配置不可删除"):
            await system_config_service.delete_config(
                tenant_id=None,
                config_key="password_policy.min_length"
            )

    @pytest.mark.asyncio
    async def test_batch_set_configs_success(self, system_config_service, mock_session):
        """测试成功批量设置配置"""
        # 模拟配置不存在（新建）
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        configs = [
            {
                "config_key": "batch.setting1",
                "config_value": "value1",
                "description": "批量配置1",
                "category": "general"
            },
            {
                "config_key": "batch.setting2",
                "config_value": "value2",
                "description": "批量配置2",
                "category": "security"
            }
        ]
        
        # 执行测试
        result = await system_config_service.batch_set_configs(
            tenant_id="tenant_123",
            configs=configs
        )
        
        # 验证结果
        assert result["success_count"] == 2
        assert result["failed_count"] == 0
        assert len(result["failed_configs"]) == 0
        assert "updated_at" in result

    @pytest.mark.asyncio
    async def test_reset_configs_success(self, system_config_service, mock_session):
        """测试成功重置配置"""
        # 模拟要删除的配置
        mock_config1 = MagicMock()
        mock_config1.config_key = "custom.setting1"
        mock_config2 = MagicMock()
        mock_config2.config_key = "custom.setting2"
        
        mock_session.execute.return_value.scalars.return_value.all.return_value = [
            mock_config1, mock_config2
        ]
        
        # 执行测试
        result = await system_config_service.reset_configs(
            tenant_id="tenant_123",
            category="general"
        )
        
        # 验证结果
        assert result["reset_count"] == 2
        assert result["category"] == "general"
        assert "reset_at" in result
        
        # 验证数据库操作
        assert mock_session.delete.call_count == 2
        mock_session.commit.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
