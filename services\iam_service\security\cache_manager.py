"""
缓存管理器

负责用户信息、权限、会话等数据的多层缓存管理
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from dataclasses import dataclass

from commonlib.storages.persistence.redis.repository import RedisRepository


@dataclass
class CacheConfig:
    """缓存配置"""
    user_info_ttl: int = 3600  # 用户基本信息缓存1小时
    user_roles_ttl: int = 1800  # 用户角色缓存30分钟
    user_permissions_ttl: int = 1800  # 用户权限缓存30分钟
    role_permissions_ttl: int = 3600  # 角色权限缓存1小时
    session_ttl: int = 7200  # 会话缓存2小时
    permission_check_ttl: int = 300  # 权限检查结果缓存5分钟


class CacheManager:
    """缓存管理器"""
    
    def __init__(
        self,
        redis_repo: RedisRepository,
        config: Optional[CacheConfig] = None
    ):
        self.redis_repo = redis_repo
        self.config = config or CacheConfig()
    
    # ==================== 用户信息缓存 ====================
    
    async def cache_user_info(
        self,
        user_id: str,
        user_info: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> None:
        """缓存用户基本信息"""
        # TODO: 实现用户信息缓存逻辑
        # 1. 序列化用户信息
        # 2. 设置缓存键
        # 3. 存储到Redis
        # 4. 设置过期时间
        # 5. 记录缓存操作
        
        cache_key = f"user_info:{user_id}"
        ttl = ttl or self.config.user_info_ttl
        
        await self.redis_repo.set(cache_key, user_info, ttl=ttl)
    
    async def get_user_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户基本信息"""
        # TODO: 实现用户信息获取逻辑
        # 1. 构建缓存键
        # 2. 从Redis获取数据
        # 3. 检查数据有效性
        # 4. 反序列化数据
        # 5. 返回用户信息
        
        cache_key = f"user_info:{user_id}"
        return await self.redis_repo.get(cache_key)
    
    async def invalidate_user_info(self, user_id: str) -> None:
        """失效用户基本信息缓存"""
        cache_key = f"user_info:{user_id}"
        await self.redis_repo.delete(cache_key)
    
    # ==================== 用户角色缓存 ====================
    
    async def cache_user_roles(
        self,
        user_id: str,
        tenant_id: str,
        roles: List[Dict[str, Any]],
        ttl: Optional[int] = None
    ) -> None:
        """缓存用户角色信息"""
        # TODO: 实现用户角色缓存逻辑
        # 1. 构建缓存键（包含租户ID）
        # 2. 序列化角色数据
        # 3. 存储到Redis
        # 4. 设置过期时间
        # 5. 更新角色版本号
        
        cache_key = f"user_roles:{tenant_id}:{user_id}"
        ttl = ttl or self.config.user_roles_ttl
        
        # 添加版本号和时间戳
        cache_data = {
            "roles": roles,
            "cached_at": datetime.now().isoformat(),
            "version": await self._get_roles_version(user_id, tenant_id)
        }
        
        await self.redis_repo.set(cache_key, cache_data, ttl=ttl)
    
    async def get_user_roles(
        self,
        user_id: str,
        tenant_id: str
    ) -> Optional[List[Dict[str, Any]]]:
        """获取用户角色信息"""
        cache_key = f"user_roles:{tenant_id}:{user_id}"
        cache_data = await self.redis_repo.get(cache_key)
        
        if not cache_data:
            return None
        
        # 检查版本号
        current_version = await self._get_roles_version(user_id, tenant_id)
        if cache_data.get("version") != current_version:
            await self.redis_repo.delete(cache_key)
            return None
        
        return cache_data.get("roles")
    
    async def invalidate_user_roles(self, user_id: str, tenant_id: str) -> None:
        """失效用户角色缓存"""
        cache_key = f"user_roles:{tenant_id}:{user_id}"
        await self.redis_repo.delete(cache_key)
        
        # 更新版本号
        await self._increment_roles_version(user_id, tenant_id)
    
    # ==================== 用户权限缓存 ====================
    
    async def cache_user_permissions(
        self,
        user_id: str,
        tenant_id: str,
        permissions: List[str],
        ttl: Optional[int] = None
    ) -> None:
        """缓存用户有效权限"""
        # TODO: 实现用户权限缓存逻辑
        # 1. 计算权限哈希值
        # 2. 构建缓存键
        # 3. 存储权限列表和哈希
        # 4. 设置过期时间
        # 5. 更新权限版本号
        
        cache_key = f"user_permissions:{tenant_id}:{user_id}"
        ttl = ttl or self.config.user_permissions_ttl
        
        # 计算权限哈希
        permissions_hash = self._calculate_permissions_hash(permissions)
        
        cache_data = {
            "permissions": permissions,
            "permissions_hash": permissions_hash,
            "cached_at": datetime.now().isoformat(),
            "version": await self._get_permissions_version(user_id, tenant_id)
        }
        
        await self.redis_repo.set(cache_key, cache_data, ttl=ttl)
    
    async def get_user_permissions(
        self,
        user_id: str,
        tenant_id: str
    ) -> Optional[List[str]]:
        """获取用户有效权限"""
        cache_key = f"user_permissions:{tenant_id}:{user_id}"
        cache_data = await self.redis_repo.get(cache_key)
        
        if not cache_data:
            return None
        
        # 检查版本号
        current_version = await self._get_permissions_version(user_id, tenant_id)
        if cache_data.get("version") != current_version:
            await self.redis_repo.delete(cache_key)
            return None
        
        return cache_data.get("permissions")
    
    async def get_user_permissions_hash(
        self,
        user_id: str,
        tenant_id: str
    ) -> Optional[str]:
        """获取用户权限哈希"""
        cache_key = f"user_permissions:{tenant_id}:{user_id}"
        cache_data = await self.redis_repo.get(cache_key)
        
        if cache_data:
            return cache_data.get("permissions_hash")
        return None
    
    async def invalidate_user_permissions(self, user_id: str, tenant_id: str) -> None:
        """失效用户权限缓存"""
        cache_key = f"user_permissions:{tenant_id}:{user_id}"
        await self.redis_repo.delete(cache_key)
        
        # 更新版本号
        await self._increment_permissions_version(user_id, tenant_id)
    
    # ==================== 角色权限缓存 ====================
    
    async def cache_role_permissions(
        self,
        role_id: str,
        tenant_id: str,
        permissions: List[str],
        ttl: Optional[int] = None
    ) -> None:
        """缓存角色权限"""
        cache_key = f"role_permissions:{tenant_id}:{role_id}"
        ttl = ttl or self.config.role_permissions_ttl
        
        cache_data = {
            "permissions": permissions,
            "cached_at": datetime.now().isoformat()
        }
        
        await self.redis_repo.set(cache_key, cache_data, ttl=ttl)
    
    async def get_role_permissions(
        self,
        role_id: str,
        tenant_id: str
    ) -> Optional[List[str]]:
        """获取角色权限"""
        cache_key = f"role_permissions:{tenant_id}:{role_id}"
        cache_data = await self.redis_repo.get(cache_key)
        
        if cache_data:
            return cache_data.get("permissions")
        return None
    
    async def invalidate_role_permissions(self, role_id: str, tenant_id: str) -> None:
        """失效角色权限缓存"""
        cache_key = f"role_permissions:{tenant_id}:{role_id}"
        await self.redis_repo.delete(cache_key)
    
    # ==================== 权限检查缓存 ====================
    
    async def cache_permission_check(
        self,
        user_id: str,
        tenant_id: str,
        resource: str,
        action: str,
        result: bool,
        ttl: Optional[int] = None
    ) -> None:
        """缓存权限检查结果"""
        # TODO: 实现权限检查结果缓存
        # 1. 构建权限检查键
        # 2. 缓存检查结果
        # 3. 设置较短的过期时间
        # 4. 关联用户权限版本
        
        check_key = f"{user_id}:{tenant_id}:{resource}:{action}"
        cache_key = f"permission_check:{hashlib.md5(check_key.encode()).hexdigest()}"
        ttl = ttl or self.config.permission_check_ttl
        
        cache_data = {
            "result": result,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "resource": resource,
            "action": action,
            "cached_at": datetime.now().isoformat(),
            "permissions_version": await self._get_permissions_version(user_id, tenant_id)
        }
        
        await self.redis_repo.set(cache_key, cache_data, ttl=ttl)
    
    async def get_permission_check(
        self,
        user_id: str,
        tenant_id: str,
        resource: str,
        action: str
    ) -> Optional[bool]:
        """获取权限检查结果"""
        check_key = f"{user_id}:{tenant_id}:{resource}:{action}"
        cache_key = f"permission_check:{hashlib.md5(check_key.encode()).hexdigest()}"
        
        cache_data = await self.redis_repo.get(cache_key)
        if not cache_data:
            return None
        
        # 检查权限版本号
        current_version = await self._get_permissions_version(user_id, tenant_id)
        if cache_data.get("permissions_version") != current_version:
            await self.redis_repo.delete(cache_key)
            return None
        
        return cache_data.get("result")
    
    # ==================== 批量操作 ====================
    
    async def invalidate_user_all_cache(self, user_id: str, tenant_id: str) -> None:
        """失效用户所有相关缓存"""
        # TODO: 实现用户所有缓存失效
        # 1. 失效用户基本信息
        # 2. 失效用户角色缓存
        # 3. 失效用户权限缓存
        # 4. 失效权限检查缓存
        # 5. 更新相关版本号
        
        await self.invalidate_user_info(user_id)
        await self.invalidate_user_roles(user_id, tenant_id)
        await self.invalidate_user_permissions(user_id, tenant_id)
        
        # 清理权限检查缓存（通过版本号自动失效）
        await self._increment_permissions_version(user_id, tenant_id)
    
    async def invalidate_role_all_cache(self, role_id: str, tenant_id: str) -> None:
        """失效角色所有相关缓存"""
        await self.invalidate_role_permissions(role_id, tenant_id)
        
        # 需要失效所有拥有此角色的用户缓存
        # 这里需要查询数据库获取相关用户列表
        # users_with_role = await self._get_users_with_role(role_id, tenant_id)
        # for user_id in users_with_role:
        #     await self.invalidate_user_permissions(user_id, tenant_id)
    
    async def warm_up_cache(
        self,
        user_id: str,
        tenant_id: str,
        user_info: Dict[str, Any],
        roles: List[Dict[str, Any]],
        permissions: List[str]
    ) -> None:
        """预热用户缓存"""
        # TODO: 实现缓存预热逻辑
        # 1. 批量缓存用户信息
        # 2. 缓存用户角色
        # 3. 缓存用户权限
        # 4. 预计算常用权限检查
        
        await self.cache_user_info(user_id, user_info)
        await self.cache_user_roles(user_id, tenant_id, roles)
        await self.cache_user_permissions(user_id, tenant_id, permissions)
    
    # ==================== 缓存统计和监控 ====================
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        # TODO: 实现缓存统计逻辑
        # 1. 统计各类缓存数量
        # 2. 计算缓存命中率
        # 3. 统计内存使用情况
        # 4. 返回统计报告
        
        stats = {
            "user_info_count": 0,
            "user_roles_count": 0,
            "user_permissions_count": 0,
            "role_permissions_count": 0,
            "permission_checks_count": 0,
            "total_memory_usage": 0,
            "hit_rate": 0.0
        }
        
        # 这里需要实现具体的统计逻辑
        return stats
    
    async def cleanup_expired_cache(self) -> int:
        """清理过期缓存"""
        # TODO: 实现过期缓存清理
        # 1. 扫描所有缓存键
        # 2. 识别过期缓存
        # 3. 批量删除过期数据
        # 4. 返回清理数量
        
        return 0
    
    # ==================== 私有方法 ====================
    
    def _calculate_permissions_hash(self, permissions: List[str]) -> str:
        """计算权限哈希值"""
        permissions_str = "|".join(sorted(permissions))
        return hashlib.sha256(permissions_str.encode()).hexdigest()[:16]
    
    async def _get_roles_version(self, user_id: str, tenant_id: str) -> int:
        """获取用户角色版本号"""
        version_key = f"roles_version:{tenant_id}:{user_id}"
        version = await self.redis_repo.get(version_key)
        return int(version) if version else 1
    
    async def _increment_roles_version(self, user_id: str, tenant_id: str) -> int:
        """递增用户角色版本号"""
        version_key = f"roles_version:{tenant_id}:{user_id}"
        return await self.redis_repo.incr(version_key)
    
    async def _get_permissions_version(self, user_id: str, tenant_id: str) -> int:
        """获取用户权限版本号"""
        version_key = f"permissions_version:{tenant_id}:{user_id}"
        version = await self.redis_repo.get(version_key)
        return int(version) if version else 1
    
    async def _increment_permissions_version(self, user_id: str, tenant_id: str) -> int:
        """递增用户权限版本号"""
        version_key = f"permissions_version:{tenant_id}:{user_id}"
        return await self.redis_repo.incr(version_key)
