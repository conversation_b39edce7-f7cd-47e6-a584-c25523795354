"""
租户自动状态管理功能测试模块

专门测试租户自动状态管理相关功能，包括：
- 配额检查和监控
- 自动状态变更
- 配额超限处理
- 批量租户管理
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any, List

from sqlalchemy.ext.asyncio import AsyncSession
from domain_common.models.iam_models import Tenant, User, Role, Permission, UserRole, RolePermission, AuditLog
from domain_common.models.constants import CommonStatus
from domain_common.exceptions import NotFoundError, ValidationError, BusinessError, DatabaseError
from infrastructure.repositories.redis_repository import RedisRepository
from services.tenant_service import TenantService


class TestTenantAutoManagement:
    """租户自动管理测试类"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def mock_redis_repo(self):
        """模拟Redis仓储"""
        redis_repo = AsyncMock(spec=RedisRepository)
        return redis_repo

    @pytest.fixture
    def tenant_service(self, mock_session, mock_redis_repo):
        """创建租户服务实例"""
        return TenantService(
            session=mock_session,
            redis_repo=mock_redis_repo,
            user_model=User,
            tenant_model=Tenant,
            role_model=Role,
            permission_model=Permission,
            user_role_model=UserRole,
            role_permission_model=RolePermission,
            audit_log_model=AuditLog
        )

    @pytest.fixture
    def sample_tenant(self):
        """示例租户数据"""
        tenant = MagicMock(spec=Tenant)
        tenant.tenant_id = "test-tenant-id"
        tenant.tenant_name = "测试租户"
        tenant.tenant_code = "TEST_TENANT"
        tenant.status = CommonStatus.ACTIVE
        tenant.settings = {
            "storage_config": {
                "max_storage_mb": 1024
            },
            "session_config": {
                "max_concurrent_sessions": 5
            }
        }
        tenant.max_users = 1000
        tenant.updated_at = datetime.now()
        return tenant

    # ================================
    # 配额检查测试
    # ================================

    @pytest.mark.asyncio
    async def test_check_tenant_quotas_no_violations(self, tenant_service, mock_session, sample_tenant):
        """测试配额检查 - 无违规"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 模拟用户数量查询 - 正常范围内
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 100
        mock_session.execute.return_value = mock_count_result

        # 执行测试
        result = await tenant_service.check_tenant_quotas("test-tenant-id")

        # 验证结果
        assert result["tenant_id"] == "test-tenant-id"
        assert result["has_violations"] is False
        assert len(result["violations"]) == 0
        assert "quotas" in result
        assert "users" in result["quotas"]
        assert result["quotas"]["users"]["exceeded"] is False

    @pytest.mark.asyncio
    async def test_check_tenant_quotas_user_exceeded(self, tenant_service, mock_session, sample_tenant):
        """测试配额检查 - 用户数量超限"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 模拟用户数量查询 - 超限
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 1500  # 超过1000的限制
        mock_session.execute.return_value = mock_count_result

        # 执行测试
        result = await tenant_service.check_tenant_quotas("test-tenant-id")

        # 验证结果
        assert result["has_violations"] is True
        assert "用户数量超限" in result["violations"]
        assert result["quotas"]["users"]["exceeded"] is True
        assert result["quotas"]["users"]["current"] == 1500
        assert result["quotas"]["users"]["limit"] == 1000

    @pytest.mark.asyncio
    async def test_check_user_quota_warning_threshold(self, tenant_service, mock_session):
        """测试用户配额警告阈值"""
        # 模拟用户数量查询 - 接近限制（85%）
        mock_result = AsyncMock()
        mock_result.scalar.return_value = 850
        mock_session.execute.return_value = mock_result

        result = await tenant_service._check_user_quota("test-tenant-id", 1000)

        assert result["exceeded"] is False
        assert result["warning"] is True  # 应该触发警告
        assert result["usage_percentage"] == 85.0

    @pytest.mark.asyncio
    async def test_check_storage_quota(self, tenant_service):
        """测试存储配额检查"""
        storage_config = {
            "max_storage_mb": 2048
        }

        result = await tenant_service._check_storage_quota("test-tenant-id", storage_config)

        assert result["quota_type"] == "storage"
        assert result["limit"] == 2048
        assert result["unit"] == "MB"
        assert result["exceeded"] is False  # 模拟数据显示未超限

    @pytest.mark.asyncio
    async def test_check_api_quota(self, tenant_service):
        """测试API配额检查"""
        settings = {
            "api_config": {
                "daily_api_calls": 5000
            }
        }

        result = await tenant_service._check_api_quota("test-tenant-id", settings)

        assert result["quota_type"] == "api_calls"
        assert result["limit"] == 5000
        assert result["period"] == "daily"
        assert result["exceeded"] is False

    @pytest.mark.asyncio
    async def test_check_session_quota(self, tenant_service):
        """测试会话配额检查"""
        session_config = {
            "max_concurrent_sessions": 10
        }

        result = await tenant_service._check_session_quota("test-tenant-id", session_config)

        assert result["quota_type"] == "sessions"
        assert result["limit"] == 10
        assert result["exceeded"] is False

    # ================================
    # 自动状态管理测试
    # ================================

    @pytest.mark.asyncio
    async def test_auto_manage_tenant_status_single_tenant(self, tenant_service, mock_session, sample_tenant):
        """测试自动管理单个租户状态"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 模拟配额检查 - 无违规
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 100
        mock_session.execute.return_value = mock_count_result

        # 执行测试
        result = await tenant_service.auto_manage_tenant_status(tenant_id="test-tenant-id")

        # 验证结果
        assert result["processed_tenants"] == 1
        assert len(result["status_changes"]) == 0  # 无状态变更
        assert len(result["quota_violations"]) == 0
        assert len(result["errors"]) == 0

    @pytest.mark.asyncio
    async def test_auto_manage_tenant_status_quota_exceeded(self, tenant_service, mock_session, sample_tenant):
        """测试自动管理 - 配额超限导致暂停"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 模拟配额检查 - 用户数量超限
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 1500
        mock_session.execute.return_value = mock_count_result

        # 模拟状态变更方法
        with patch.object(tenant_service, 'change_tenant_status') as mock_change_status:
            mock_change_status.return_value = {
                "tenant_id": "test-tenant-id",
                "old_status": CommonStatus.ACTIVE,
                "new_status": CommonStatus.SUSPENDED,
                "reason": "quota_exceeded"
            }

            # 执行测试
            result = await tenant_service.auto_manage_tenant_status(tenant_id="test-tenant-id")

        # 验证结果
        assert result["processed_tenants"] == 1
        assert len(result["status_changes"]) == 1
        assert result["status_changes"][0]["new_status"] == CommonStatus.SUSPENDED
        assert len(result["quota_violations"]) == 1

        # 验证状态变更被调用
        mock_change_status.assert_called_once_with(
            tenant_id="test-tenant-id",
            new_status=CommonStatus.SUSPENDED,
            reason="quota_exceeded",
            comment="自动暂停：用户数量超限",
            user_id="system"
        )

    @pytest.mark.asyncio
    async def test_auto_manage_tenant_status_recovery(self, tenant_service, mock_session, mock_redis_repo):
        """测试自动管理 - 配额恢复导致激活"""
        # 准备暂停状态的租户
        suspended_tenant = MagicMock(spec=Tenant)
        suspended_tenant.tenant_id = "test-tenant-id"
        suspended_tenant.status = CommonStatus.SUSPENDED
        suspended_tenant.settings = {}
        suspended_tenant.max_users = 1000

        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = suspended_tenant
        mock_session.execute.return_value = mock_result

        # 模拟配额检查 - 无违规
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 100
        mock_session.execute.return_value = mock_count_result

        # 模拟Redis中的暂停信息
        suspension_info = json.dumps({
            "suspension_reason": "quota_exceeded",
            "auto_resume_enabled": True
        })
        mock_redis_repo.get.return_value = suspension_info

        # 模拟状态变更方法
        with patch.object(tenant_service, 'change_tenant_status') as mock_change_status:
            mock_change_status.return_value = {
                "tenant_id": "test-tenant-id",
                "old_status": CommonStatus.SUSPENDED,
                "new_status": CommonStatus.ACTIVE,
                "reason": "auto_management"
            }

            # 执行测试
            result = await tenant_service.auto_manage_tenant_status(tenant_id="test-tenant-id")

        # 验证结果
        assert len(result["status_changes"]) == 1
        assert result["status_changes"][0]["new_status"] == CommonStatus.ACTIVE

        # 验证状态变更被调用
        mock_change_status.assert_called_once_with(
            tenant_id="test-tenant-id",
            new_status=CommonStatus.ACTIVE,
            reason="auto_management",
            comment="自动恢复：配额问题已解决",
            user_id="system"
        )

    @pytest.mark.asyncio
    async def test_auto_manage_tenant_status_batch(self, tenant_service, mock_session):
        """测试批量自动管理租户状态"""
        # 模拟获取租户列表
        mock_result = AsyncMock()
        mock_result.fetchall.return_value = [("tenant1",), ("tenant2",), ("tenant3",)]
        mock_session.execute.return_value = mock_result

        # 模拟租户数据和配额检查
        with patch.object(tenant_service, 'check_tenant_quotas') as mock_check_quotas:
            mock_check_quotas.return_value = {
                "has_violations": False,
                "violations": []
            }

            with patch.object(tenant_service, '_determine_auto_status_change') as mock_determine:
                mock_determine.return_value = None  # 无需状态变更

                # 执行测试
                result = await tenant_service.auto_manage_tenant_status()

        # 验证结果
        assert result["processed_tenants"] == 3
        assert len(result["status_changes"]) == 0
        assert len(result["errors"]) == 0

    @pytest.mark.asyncio
    async def test_get_tenants_for_auto_management(self, tenant_service, mock_session):
        """测试获取需要自动管理的租户列表"""
        # 模拟数据库查询结果
        mock_result = AsyncMock()
        mock_result.fetchall.return_value = [
            ("tenant1",),
            ("tenant2",),
            ("tenant3",)
        ]
        mock_session.execute.return_value = mock_result

        # 执行测试
        tenant_ids = await tenant_service._get_tenants_for_auto_management()

        # 验证结果
        assert len(tenant_ids) == 3
        assert "tenant1" in tenant_ids
        assert "tenant2" in tenant_ids
        assert "tenant3" in tenant_ids

    @pytest.mark.asyncio
    async def test_determine_auto_status_change_no_change(self, tenant_service, mock_session):
        """测试确定自动状态变更 - 无需变更"""
        # 模拟当前状态查询
        mock_result = AsyncMock()
        mock_result.scalar_one.return_value = CommonStatus.ACTIVE
        mock_session.execute.return_value = mock_result

        quota_status = {
            "has_violations": False,
            "violations": []
        }

        result = await tenant_service._determine_auto_status_change("test-tenant-id", quota_status)

        assert result is None

    @pytest.mark.asyncio
    async def test_determine_auto_status_change_suspend(self, tenant_service, mock_session):
        """测试确定自动状态变更 - 需要暂停"""
        # 模拟当前状态查询
        mock_result = AsyncMock()
        mock_result.scalar_one.return_value = CommonStatus.ACTIVE
        mock_session.execute.return_value = mock_result

        quota_status = {
            "has_violations": True,
            "violations": ["用户数量超限", "存储空间超限"]
        }

        result = await tenant_service._determine_auto_status_change("test-tenant-id", quota_status)

        assert result is not None
        assert result["new_status"] == CommonStatus.SUSPENDED
        assert result["reason"] == "quota_exceeded"
        assert "用户数量超限" in result["comment"]

    # ================================
    # 配额超限处理测试
    # ================================

    @pytest.mark.asyncio
    async def test_handle_quota_exceeded_suspend(self, tenant_service, mock_session, sample_tenant):
        """测试处理配额超限 - 暂停租户"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 模拟状态变更方法
        with patch.object(tenant_service, 'change_tenant_status') as mock_change_status:
            mock_change_status.return_value = {
                "tenant_id": "test-tenant-id",
                "old_status": CommonStatus.ACTIVE,
                "new_status": CommonStatus.SUSPENDED,
                "reason": "quota_exceeded"
            }

            # 执行测试
            result = await tenant_service.handle_quota_exceeded(
                tenant_id="test-tenant-id",
                quota_type="users",
                action="suspend"
            )

        # 验证结果
        assert result["tenant_id"] == "test-tenant-id"
        assert result["new_status"] == CommonStatus.SUSPENDED

        # 验证状态变更被调用
        mock_change_status.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_quota_exceeded_notify(self, tenant_service, mock_redis_repo):
        """测试处理配额超限 - 发送通知"""
        with patch.object(tenant_service, '_send_quota_exceeded_notification') as mock_notify:
            mock_notify.return_value = None

            # 执行测试
            result = await tenant_service.handle_quota_exceeded(
                tenant_id="test-tenant-id",
                quota_type="storage",
                action="notify"
            )

        # 验证结果
        assert result["action"] == "notification_sent"
        assert result["quota_type"] == "storage"

        # 验证通知被发送
        mock_notify.assert_called_once_with("test-tenant-id", "storage")

    @pytest.mark.asyncio
    async def test_handle_quota_exceeded_limit(self, tenant_service, mock_redis_repo):
        """测试处理配额超限 - 应用限制"""
        with patch.object(tenant_service, '_apply_quota_limits') as mock_limit:
            mock_limit.return_value = None

            # 执行测试
            result = await tenant_service.handle_quota_exceeded(
                tenant_id="test-tenant-id",
                quota_type="api_calls",
                action="limit"
            )

        # 验证结果
        assert result["action"] == "limits_applied"
        assert result["quota_type"] == "api_calls"

        # 验证限制被应用
        mock_limit.assert_called_once_with("test-tenant-id", "api_calls")

    @pytest.mark.asyncio
    async def test_handle_quota_exceeded_invalid_action(self, tenant_service):
        """测试处理配额超限 - 无效动作"""
        with pytest.raises(ValidationError) as exc_info:
            await tenant_service.handle_quota_exceeded(
                tenant_id="test-tenant-id",
                quota_type="users",
                action="invalid_action"
            )
        
        assert "无效的配额处理动作" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_send_quota_exceeded_notification(self, tenant_service, mock_redis_repo):
        """测试发送配额超限通知"""
        await tenant_service._send_quota_exceeded_notification("test-tenant-id", "users")

        # 验证通知被加入队列
        mock_redis_repo.lpush.assert_called_once()
        call_args = mock_redis_repo.lpush.call_args
        assert call_args[0][0] == "quota_notifications"

        # 验证通知内容
        notification_data = json.loads(call_args[0][1])
        assert notification_data["tenant_id"] == "test-tenant-id"
        assert notification_data["quota_type"] == "users"
        assert notification_data["type"] == "quota_exceeded"

    @pytest.mark.asyncio
    async def test_apply_quota_limits(self, tenant_service, mock_redis_repo):
        """测试应用配额限制"""
        await tenant_service._apply_quota_limits("test-tenant-id", "users")

        # 验证限制信息被缓存
        mock_redis_repo.set.assert_called_once()
        call_args = mock_redis_repo.set.call_args
        assert call_args[0][0] == "tenant_limits:test-tenant-id:users"

        # 验证限制信息内容
        limit_info = json.loads(call_args[0][1])
        assert limit_info["tenant_id"] == "test-tenant-id"
        assert limit_info["quota_type"] == "users"
        assert "禁止创建新用户" in limit_info["restrictions"]
