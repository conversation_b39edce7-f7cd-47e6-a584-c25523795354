/**
 * @file 重置密码表单组件
 * @description 用户通过邮件链接重置密码的表单
 */

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import clsx from 'clsx'
import { Button, Input } from '@/components/ui'
import { TenantInfo } from '@/types'
import * as authApi from '@/api/auth'

// Zod验证模式
const resetPasswordSchema = z.object({
  password: z.string()
    .min(8, '密码至少需要8个字符')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      '密码必须包含大小写字母、数字和特殊字符'),
  confirmPassword: z.string(),
  verification_code: z.string()
    .min(4, '验证码至少4位')
    .max(8, '验证码最多8位')
    .regex(/^[0-9]+$/, '验证码只能包含数字')
}).refine(data => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword']
})

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>

export interface ResetPasswordFormProps {
  tenant?: TenantInfo
  email?: string
  token?: string
  onSuccess?: () => void
  onError?: (error: string) => void
  onBackToLogin?: () => void
  className?: string
}

export const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({
  tenant,
  email,
  token,
  onSuccess,
  onError,
  onBackToLogin,
  className
}) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const [resetSuccess, setResetSuccess] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
      verification_code: ''
    }
  })

  // 监听密码强度
  const password = watch('password')
  const getPasswordStrength = (pwd: string) => {
    if (!pwd) return { strength: 0, label: '', color: '' }
    
    let strength = 0
    if (pwd.length >= 8) strength++
    if (/[a-z]/.test(pwd)) strength++
    if (/[A-Z]/.test(pwd)) strength++
    if (/\d/.test(pwd)) strength++
    if (/[@$!%*?&]/.test(pwd)) strength++

    const levels = [
      { strength: 0, label: '', color: '' },
      { strength: 1, label: '很弱', color: 'bg-red-500' },
      { strength: 2, label: '弱', color: 'bg-orange-500' },
      { strength: 3, label: '一般', color: 'bg-yellow-500' },
      { strength: 4, label: '强', color: 'bg-blue-500' },
      { strength: 5, label: '很强', color: 'bg-green-500' }
    ]

    return levels[strength] || levels[0]
  }

  const passwordStrength = getPasswordStrength(password)

  const onSubmit = async (data: ResetPasswordFormData) => {
    if (!token) {
      onError?.('重置令牌无效，请重新申请密码重置')
      return
    }

    setIsLoading(true)

    try {
      const resetRequest = {
        token,
        new_password: data.password,
        verification_code: data.verification_code
      }

      await authApi.resetPassword(resetRequest)
      setResetSuccess(true)
      onSuccess?.()
    } catch (err: any) {
      const errorMessage = err.message || '重置密码失败，请重试'
      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  if (resetSuccess) {
    return (
      <div className={clsx('space-y-6 text-center', className)}>
        {/* 成功图标 */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, type: 'spring' }}
          className="flex justify-center"
        >
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </motion.div>

        {/* 成功信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-4"
        >
          <h3 className="text-xl font-semibold text-gray-900">
            密码重置成功
          </h3>
          <p className="text-gray-600">
            您的密码已成功重置，现在可以使用新密码登录了。
          </p>
        </motion.div>

        {/* 登录按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          {onBackToLogin && (
            <Button
              variant="primary"
              size="lg"
              onClick={onBackToLogin}
              className="w-full"
            >
              立即登录
            </Button>
          )}
        </motion.div>
      </div>
    )
  }

  return (
    <div className={clsx('space-y-6', className)}>
      {/* 表单头部说明 */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="text-center space-y-2"
      >
        <h3 className="text-lg font-semibold text-gray-900">
          设置新密码
        </h3>
        {email && (
          <p className="text-sm text-gray-600">
            为账户 <span className="font-medium">{email}</span> 设置新密码
          </p>
        )}
      </motion.div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* 验证码 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Input
            label="验证码"
            placeholder="请输入邮件中的验证码"
            error={errors.verification_code?.message}
            disabled={isLoading || isSubmitting}
            {...register('verification_code')}
          />
        </motion.div>

        {/* 新密码 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Input
            label="新密码"
            type="password"
            placeholder="请输入新密码"
            error={errors.password?.message}
            disabled={isLoading || isSubmitting}
            {...register('password')}
          />
          
          {/* 密码强度指示器 */}
          {password && (
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className={clsx('h-2 rounded-full transition-all duration-300', passwordStrength.color)}
                    style={{ width: `${(passwordStrength.strength / 5) * 100}%` }}
                  />
                </div>
                <span className="text-xs text-gray-600 min-w-[3rem]">
                  {passwordStrength.label}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                密码应包含大小写字母、数字和特殊字符，至少8位
              </p>
            </div>
          )}
        </motion.div>

        {/* 确认密码 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Input
            label="确认新密码"
            type="password"
            placeholder="请再次输入新密码"
            error={errors.confirmPassword?.message}
            disabled={isLoading || isSubmitting}
            {...register('confirmPassword')}
          />
        </motion.div>

        {/* 提交按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isLoading || isSubmitting}
            disabled={isLoading || isSubmitting}
            className="w-full"
          >
            {isLoading || isSubmitting ? '重置中...' : '重置密码'}
          </Button>
        </motion.div>
      </form>

      {/* 返回登录 */}
      {onBackToLogin && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="text-center"
        >
          <button
            type="button"
            onClick={onBackToLogin}
            className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
            disabled={isLoading || isSubmitting}
          >
            ← 返回登录
          </button>
        </motion.div>
      )}
    </div>
  )
}
