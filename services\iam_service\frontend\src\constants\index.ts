/**
 * @file 常量统一导出
 * @description 导出所有常量定义，提供统一的常量导入入口
 */

// 认证相关常量
export {
  AUTH_STORAGE_KEYS,
  AUTH_STATUS,
  FORM_STATUS,
  LOGIN_TYPES,
  AUTH_FORM_MODES,
  PASSWORD_STRENGTH,
  PASSWORD_STRENGTH_THRESHOLDS,
  DEFAULT_PASSWORD_POLICY,
  PASSWORD_PATTERNS,
  VALIDATION_ERROR_CODES,
  AUTH_ERROR_MESSAGES,
  SESSION_TIMEOUTS,
  LOGIN_ATTEMPT_LIMITS,
  TENANT_DETECTION_PRIORITY,
  TENANT_DETECTION_METHODS,
  EMAIL_DOMAIN_TENANT_MAP,
  SOCIAL_LOGIN_PROVIDERS,
  AUTH_EVENT_TYPES,
  AUTH_API_ENDPOINTS,
  HTTP_STATUS_CODES,
  AUTH_CSS_CLASSES,
  ANIMATION_DURATIONS
} from './auth'

// 主题相关常量
export {
  THEME_MODES,
  THEME_VARIANTS,
  COLOR_MODES,
  THEME_STORAGE_KEYS,
  DEFAULT_THEME_CONFIG,
  LIGHT_THEME_COLORS,
  DARK_THEME_COLORS,
  FONT_FAMILIES,
  FONT_SIZES,
  FONT_WEIGHTS,
  LINE_HEIGHTS,
  LETTER_SPACINGS,
  SPACING,
  ANIMATION_DURATIONS as THEME_ANIMATION_DURATIONS,
  ANIMATION_EASINGS,
  BREAKPOINTS,
  AUTH_LAYOUT_MODES,
  AUTH_LAYOUT_BREAKPOINTS,
  BRAND_SECTION_CONFIG,
  FORM_SECTION_CONFIG,
  CSS_VARIABLE_PREFIX,
  THEME_CSS_VARIABLES,
  THEME_TRANSITION
} from './theme'

// 路由相关常量
export {
  AUTH_ROUTES,
  APP_ROUTES,
  ADMIN_ROUTES,
  ERROR_ROUTES,
  ALL_ROUTES,
  PUBLIC_ROUTES,
  PROTECTED_ROUTES,
  ADMIN_ONLY_ROUTES,
  ROUTE_META,
  NAVIGATION_MENU,
  ADMIN_NAVIGATION_MENU,
  BREADCRUMB_CONFIG
} from './routes'

// 重新导出类型
export type {
  RouteParams,
  QueryParams
} from './routes'
