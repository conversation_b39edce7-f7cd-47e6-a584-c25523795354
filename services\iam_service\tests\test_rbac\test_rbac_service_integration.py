"""
RBAC服务集成测试

测试RBAC服务与路由的集成
"""

import pytest
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

from services.iam_service.services.rbac_service import RBACService
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission, AuditLog
)
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    BusinessError
)


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟Redis仓库"""
    redis_repo = AsyncMock()
    return redis_repo


@pytest.fixture
def rbac_service(mock_session, mock_redis_repo):
    """创建RBAC服务实例"""
    return RBACService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        audit_log_model=AuditLog
    )


class TestRBACServiceIntegration:
    """RBAC服务集成测试类"""

    @pytest.mark.asyncio
    async def test_complete_rbac_workflow(self, rbac_service, mock_session):
        """测试完整的RBAC工作流程"""
        # 准备测试数据
        tenant_id = "tenant_123"
        
        # 1. 创建角色
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            mock_tenant,  # 租户查询
            None,  # 角色代码唯一性检查
        ]
        
        role_result = await rbac_service.create_role(
            tenant_id=tenant_id,
            role_name="管理员",
            role_code="ADMIN",
            description="系统管理员角色",
            level=1,
            is_system=True,
            permissions=["user:read", "user:write"]
        )
        
        assert role_result["role_name"] == "管理员"
        assert role_result["role_code"] == "ADMIN"
        assert "role_id" in role_result
        
        # 2. 创建权限
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            None,  # 权限代码唯一性检查
        ]
        
        permission_result = await rbac_service.create_permission(
            tenant_id=tenant_id,
            permission_code="system:admin",
            permission_name="系统管理",
            description="系统管理权限",
            resource_type="system",
            action="admin",
            category="system",
            is_system=True
        )
        
        assert permission_result["permission_code"] == "system:admin"
        assert permission_result["permission_name"] == "系统管理"
        
        # 3. 分配角色权限
        rbac_service._validate_permissions_exist = AsyncMock(return_value=["system:admin"])
        rbac_service._add_permissions_to_role = AsyncMock(return_value=1)
        rbac_service._get_role_permissions = AsyncMock(return_value=["system:admin"])
        
        assign_result = await rbac_service.assign_role_permissions(
            tenant_id=tenant_id,
            role_id=role_result["role_id"],
            permission_codes=["system:admin"],
            operation="assign"
        )
        
        assert assign_result["affected_permissions"] == 1
        assert assign_result["operation"] == "assign"
        
        # 4. 分配用户角色
        mock_user = MagicMock()
        mock_user.tenant_id = tenant_id
        rbac_service._get_user_by_id = AsyncMock(return_value=mock_user)
        rbac_service._validate_roles_exist = AsyncMock(return_value=[role_result["role_id"]])
        rbac_service._add_roles_to_user = AsyncMock(return_value=1)
        rbac_service._get_user_roles = AsyncMock(return_value=[{
            "role_id": role_result["role_id"],
            "role_name": "管理员"
        }])
        
        user_role_result = await rbac_service.assign_user_roles(
            tenant_id=tenant_id,
            user_id="user_123",
            role_ids=[role_result["role_id"]],
            operation="assign"
        )
        
        assert user_role_result["affected_roles"] == 1
        assert user_role_result["operation"] == "assign"
        
        # 5. 权限检查
        rbac_service._get_user_permissions_from_cache = AsyncMock(return_value=None)
        rbac_service._calculate_user_permissions = AsyncMock(return_value={
            "permissions": ["system:admin"],
            "sources": {"system:admin": "管理员"}
        })
        rbac_service._cache_user_permissions = AsyncMock()
        
        check_result = await rbac_service.check_permission(
            tenant_id=tenant_id,
            user_id="user_123",
            permission_code="system:admin"
        )
        
        assert check_result["has_permission"] is True
        assert check_result["permission_source"] == "管理员"
        assert check_result["cache_hit"] is False
        
        # 验证所有数据库操作都被调用
        assert mock_session.add.call_count >= 2  # 至少添加了角色和权限
        assert mock_session.commit.call_count >= 2  # 至少提交了2次

    @pytest.mark.asyncio
    async def test_role_hierarchy_management(self, rbac_service, mock_session):
        """测试角色层级管理"""
        tenant_id = "tenant_123"
        
        # 模拟租户存在
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            mock_tenant,  # 租户查询
            None,  # 父角色代码唯一性检查
            mock_tenant,  # 租户查询
            None,  # 子角色代码唯一性检查
        ]
        
        # 创建父角色
        parent_role = await rbac_service.create_role(
            tenant_id=tenant_id,
            role_name="部门经理",
            role_code="DEPT_MANAGER",
            level=1
        )
        
        # 模拟父角色存在
        mock_parent_role = MagicMock()
        mock_parent_role.tenant_id = tenant_id
        mock_parent_role.level = 1
        rbac_service._get_role_by_id = AsyncMock(return_value=mock_parent_role)
        
        # 创建子角色
        child_role = await rbac_service.create_role(
            tenant_id=tenant_id,
            role_name="项目经理",
            role_code="PROJECT_MANAGER",
            parent_role_id=parent_role["role_id"],
            level=2
        )
        
        assert child_role["level"] == 2
        assert child_role["parent_role_id"] == parent_role["role_id"]

    @pytest.mark.asyncio
    async def test_permission_caching(self, rbac_service, mock_redis_repo):
        """测试权限缓存功能"""
        tenant_id = "tenant_123"
        user_id = "user_123"
        
        # 模拟缓存命中
        cached_permissions = {
            "user:read": True,
            "user:read_source": "管理员"
        }
        mock_redis_repo.get.return_value = cached_permissions
        
        # 执行权限检查
        result = await rbac_service.check_permission(
            tenant_id=tenant_id,
            user_id=user_id,
            permission_code="user:read"
        )
        
        # 验证缓存命中
        assert result["cache_hit"] is True
        assert result["has_permission"] is True
        assert result["permission_source"] == "管理员"
        
        # 验证Redis操作
        mock_redis_repo.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_role_deletion_with_user_transfer(self, rbac_service, mock_session):
        """测试角色删除时的用户转移"""
        tenant_id = "tenant_123"
        role_id = "role_to_delete"
        transfer_role_id = "transfer_target_role"
        
        # 模拟角色存在
        mock_role = MagicMock()
        mock_role.tenant_id = tenant_id
        mock_role.meta_data = {"is_system": False}
        mock_role.status = CommonStatus.ACTIVE
        rbac_service._get_role_by_id = AsyncMock(return_value=mock_role)
        
        # 模拟有用户关联
        rbac_service._get_role_user_count = AsyncMock(return_value=3)
        rbac_service._transfer_users_to_role = AsyncMock(return_value=3)
        rbac_service._remove_role_permissions = AsyncMock()
        
        # 执行删除
        result = await rbac_service.delete_role(
            tenant_id=tenant_id,
            role_id=role_id,
            force_delete=False,
            transfer_to_role_id=transfer_role_id
        )
        
        assert result["affected_users"] == 3
        assert result["transferred_to"] == transfer_role_id
        
        # 验证转移操作被调用
        rbac_service._transfer_users_to_role.assert_called_once_with(role_id, transfer_role_id)

    @pytest.mark.asyncio
    async def test_error_handling(self, rbac_service, mock_session):
        """测试错误处理"""
        tenant_id = "tenant_123"
        
        # 测试租户不存在的情况
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        with pytest.raises(NotFoundError, match="租户不存在"):
            await rbac_service.create_role(
                tenant_id=tenant_id,
                role_name="测试角色",
                role_code="TEST_ROLE"
            )
        
        # 测试重复角色代码
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        mock_existing_role = MagicMock()
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            mock_tenant,  # 租户查询
            mock_existing_role,  # 角色代码已存在
        ]
        
        with pytest.raises(DuplicateResourceError, match="角色代码已存在"):
            await rbac_service.create_role(
                tenant_id=tenant_id,
                role_name="测试角色",
                role_code="EXISTING_ROLE"
            )

    @pytest.mark.asyncio
    async def test_audit_logging(self, rbac_service, mock_session):
        """测试审计日志记录"""
        tenant_id = "tenant_123"
        
        # 模拟租户存在
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            mock_tenant,  # 租户查询
            None,  # 角色代码唯一性检查
        ]
        
        # 创建角色
        await rbac_service.create_role(
            tenant_id=tenant_id,
            role_name="测试角色",
            role_code="TEST_ROLE",
            description="测试角色描述"
        )
        
        # 验证审计日志被创建
        # 注意：由于审计日志是通过AuditLogBuilder创建的，
        # 我们主要验证session.add被调用了正确的次数
        assert mock_session.add.call_count >= 2  # 角色 + 审计日志


if __name__ == "__main__":
    pytest.main([__file__])
