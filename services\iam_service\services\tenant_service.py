"""
租户服务

提供租户管理的业务逻辑实现
"""
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Type
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    DatabaseError, BusinessError
)
from commonlib.core.logging.tsif_logging import app_logger

from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission,
    AuditLog, AuditLogBuilder
)
from security.security_utils import SecurityUtils




class TenantService:
    """租户服务类"""

    def __init__(
        self,
        session: AsyncSession,
        redis_repo: RedisRepository,
        user_model: Type[User],
        tenant_model: Type[Tenant],
        role_model: Type[Role],
        permission_model: Type[Permission],
        user_role_model: Type[UserRole],
        role_permission_model: Type[RolePermission],
        audit_log_model: Type[AuditLog]
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_repo = redis_repo

        # 核心业务模型
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.role_model = role_model
        self.permission_model = permission_model

        # 关联关系模型
        self.user_role_model = user_role_model
        self.role_permission_model = role_permission_model

        # 审计模型
        self.audit_log_model = audit_log_model

        # 安全工具
        self.security_utils = SecurityUtils()

        # 默认角色配置
        self.DEFAULT_ROLES = [
            {
                "role_code": "SUPER_ADMIN",
                "role_name": "超级管理员",
                "description": "租户内最高权限管理员",
                "level": 1
            },
            {
                "role_code": "ADMIN",
                "role_name": "管理员",
                "description": "租户管理员，负责用户和权限管理",
                "level": 2
            },
            {
                "role_code": "USER",
                "role_name": "普通用户",
                "description": "普通业务用户",
                "level": 3
            }
        ]

        # 默认权限配置
        self.DEFAULT_PERMISSIONS = [
            # 系统权限
            {"permission_code": "system", "permission_name": "系统管理", "resource": "system", "action": "manage", "level": 1},
            {"permission_code": "tenant:manage", "permission_name": "租户管理", "resource": "tenant", "action": "manage", "level": 2, "parent_code": "system"},
            {"permission_code": "user:manage", "permission_name": "用户管理", "resource": "user", "action": "manage", "level": 2, "parent_code": "system"},
            {"permission_code": "role:manage", "permission_name": "角色管理", "resource": "role", "action": "manage", "level": 2, "parent_code": "system"},
            {"permission_code": "permission:manage", "permission_name": "权限管理", "resource": "permission", "action": "manage", "level": 2, "parent_code": "system"},

            # 业务权限
            {"permission_code": "user:read", "permission_name": "查看用户", "resource": "user", "action": "read", "level": 3, "parent_code": "user:manage"},
            {"permission_code": "user:write", "permission_name": "编辑用户", "resource": "user", "action": "write", "level": 3, "parent_code": "user:manage"},
            {"permission_code": "role:read", "permission_name": "查看角色", "resource": "role", "action": "read", "level": 3, "parent_code": "role:manage"},
            {"permission_code": "role:write", "permission_name": "编辑角色", "resource": "role", "action": "write", "level": 3, "parent_code": "role:manage"},
        ]


    async def _check_tenant_uniqueness(self, tenant_name: str, tenant_code: str) -> None:
        """检查租户编码和名称唯一性"""
        # 检查租户编码唯一性
        stmt = select(self.tenant_model).where(
            and_(
                self.tenant_model.tenant_code == tenant_code,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("租户", "tenant_code", tenant_code)

        # 检查租户名称唯一性
        stmt = select(self.tenant_model).where(
            and_(
                self.tenant_model.tenant_name == tenant_name,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("租户", "tenant_name", tenant_name)

    async def create_tenant(
        self,
        tenant_name: str,
        tenant_code: str,
        description: Optional[str] = None,
        max_users: Optional[int] = None,
        settings: Optional[Dict[str, Any]] = None,
        admin_user: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """创建租户"""
        try:

            # 1. 检查租户编码和名称唯一性
            await self._check_tenant_uniqueness(tenant_name, tenant_code)

            # 2. 生成租户ID
            tenant_id = str(uuid.uuid4())

            # 3. 创建租户记录到数据库
            tenant = self.tenant_model(
                tenant_id=tenant_id,
                tenant_name=tenant_name.strip(),
                tenant_code=tenant_code.strip().upper(),
                description=description,
                max_users=max_users or 1000,
                settings=settings or {},
                status=CommonStatus.PENDING
            )

            self.session.add(tenant)
            await self.session.flush()  # 获取生成的ID

            # 5. 初始化默认角色和权限体系
            created_roles = await self._initialize_default_roles(tenant_id)
            created_permissions = await self._initialize_default_permissions(tenant_id)
            await self._assign_permissions_to_roles(tenant_id, created_roles, created_permissions)

            # 6. 创建默认管理员用户
            admin_user_info = None
            if admin_user:
                admin_user_info = await self._create_default_admin_user(
                    tenant_id, created_roles, admin_user
                )

            # 提交事务
            await self.session.commit()

            # 7. 记录租户创建审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                action="tenant:create",
                resource_type="tenant",
                resource_id=tenant_id,
                details={
                    "tenant_name": tenant_name,
                    "tenant_code": tenant_code,
                    "max_users": max_users or 1000,
                    "roles_created": len(created_roles),
                    "permissions_created": len(created_permissions)
                }
            )

            # 8. 缓存租户信息
            result = {
                "tenant_id": tenant_id,
                "tenant_name": tenant_name,
                "tenant_code": tenant_code.upper(),
                "description": description,
                "status": CommonStatus.PENDING,
                "max_users": max_users or 1000,
                "current_users": 1 if admin_user_info else 0,
                "settings": settings or {},
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            await self.redis_repo.set(f"tenant:{tenant_id}", json.dumps(result, ensure_ascii=False), ttl=3600)

            # 构建返回结果
            response = result.copy()
            if admin_user_info:
                response["admin_user"] = admin_user_info
            response["default_roles_created"] = [role["role_code"] for role in created_roles]
            response["default_permissions_created"] = len(created_permissions)

            return response

        except IntegrityError as e:
            await self.session.rollback()
            raise DatabaseError(f"租户创建失败：数据库约束错误 - {str(e)}")
        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (ValidationError, DuplicateResourceError)):
                raise
            raise DatabaseError(f"租户创建失败：{str(e)}")

    async def _initialize_default_roles(self, tenant_id: str) -> List[Dict[str, Any]]:
        """初始化默认角色"""
        created_roles = []

        for role_config in self.DEFAULT_ROLES:
            role_id = str(uuid.uuid4())
            role = self.role_model(
                role_id=role_id,
                tenant_id=tenant_id,
                role_name=role_config["role_name"],
                role_code=role_config["role_code"],
                description=role_config["description"],
                level=role_config["level"],
                status=CommonStatus.ACTIVE
            )

            self.session.add(role)
            created_roles.append({
                "role_id": role_id,
                "role_code": role_config["role_code"],
                "role_name": role_config["role_name"],
                "level": role_config["level"]
            })

        return created_roles

    async def _initialize_default_permissions(self, tenant_id: str) -> List[Dict[str, Any]]:
        """初始化默认权限"""
        created_permissions = []
        permission_map = {}  # 用于存储权限ID映射

        # 先创建所有权限
        for perm_config in self.DEFAULT_PERMISSIONS:
            permission_id = str(uuid.uuid4())
            permission = self.permission_model(
                permission_id=permission_id,
                tenant_id=tenant_id,
                permission_name=perm_config["permission_name"],
                permission_code=perm_config["permission_code"],
                resource=perm_config["resource"],
                action=perm_config["action"],
                level=perm_config["level"],
                status=CommonStatus.ACTIVE
            )

            self.session.add(permission)
            permission_map[perm_config["permission_code"]] = permission_id
            created_permissions.append({
                "permission_id": permission_id,
                "permission_code": perm_config["permission_code"],
                "permission_name": perm_config["permission_name"],
                "level": perm_config["level"]
            })

        # 设置父权限关系
        await self.session.flush()  # 确保权限已保存

        for perm_config in self.DEFAULT_PERMISSIONS:
            if "parent_code" in perm_config:
                parent_id = permission_map.get(perm_config["parent_code"])
                if parent_id:
                    permission_id = permission_map[perm_config["permission_code"]]
                    # 更新父权限ID
                    stmt = select(self.permission_model).where(
                        self.permission_model.permission_id == permission_id
                    )
                    result = await self.session.execute(stmt)
                    permission = result.scalar_one()
                    permission.parent_permission_id = parent_id

        return created_permissions

    async def _assign_permissions_to_roles(
        self,
        tenant_id: str,
        roles: List[Dict[str, Any]],
        permissions: List[Dict[str, Any]]
    ) -> None:
        """为角色分配权限"""
        # 角色权限分配规则
        role_permission_mapping = {
            "SUPER_ADMIN": [p["permission_code"] for p in permissions],  # 超级管理员拥有所有权限
            "ADMIN": ["user:manage", "user:read", "user:write", "role:read", "role:write"],  # 管理员权限
            "USER": ["user:read"]  # 普通用户权限
        }

        # 创建权限映射
        permission_map = {p["permission_code"]: p["permission_id"] for p in permissions}
        role_map = {r["role_code"]: r["role_id"] for r in roles}

        for role_code, permission_codes in role_permission_mapping.items():
            role_id = role_map.get(role_code)
            if not role_id:
                continue

            for permission_code in permission_codes:
                permission_id = permission_map.get(permission_code)
                if permission_id:
                    role_permission = self.role_permission_model(
                        tenant_id=tenant_id,
                        role_id=role_id,
                        permission_id=permission_id,
                        status=CommonStatus.ACTIVE,
                        assigned_at=datetime.now()
                    )
                    self.session.add(role_permission)

    async def _create_default_admin_user(
        self,
        tenant_id: str,
        roles: List[Dict[str, Any]],
        admin_user: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建默认管理员用户"""
        # 验证管理员用户数据
        required_fields = ["username", "email", "password"]
        for field in required_fields:
            if not admin_user.get(field):
                raise ValidationError(f"管理员用户缺少必填字段: {field}")

        # 生成用户ID
        user_id = str(uuid.uuid4())

        # 哈希密码
        password_hash = self.security_utils.hash_password(admin_user["password"])

        # 创建用户
        user = self.user_model(
            user_id=user_id,
            tenant_id=tenant_id,
            username=admin_user["username"],
            email=admin_user["email"],
            phone=admin_user.get("phone"),
            nickname=admin_user.get("nickname", admin_user["username"]),
            password_hash=password_hash,
            salt="",  # bcrypt已包含盐值
            status=CommonStatus.ACTIVE,
            password_changed_at=datetime.now()
        )

        self.session.add(user)

        # 分配超级管理员角色
        super_admin_role = next((r for r in roles if r["role_code"] == "SUPER_ADMIN"), None)
        if super_admin_role:
            user_role = self.user_role_model(
                tenant_id=tenant_id,
                user_id=user_id,
                role_id=super_admin_role["role_id"],
                assignment_type="permanent",
                status=CommonStatus.ACTIVE,
                assigned_at=datetime.now()
            )
            self.session.add(user_role)

        return {
            "user_id": user_id,
            "username": admin_user["username"],
            "email": admin_user["email"],
            "role": "SUPER_ADMIN"
        }

    async def list_tenants(
        self,
        limit: int = 20,
        search: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取租户列表"""
        try:
            # 构建查询条件
            conditions = []
            # 添加状态筛选
            if status:
                conditions.append(self.tenant_model.status == status)

            # 添加搜索条件
            if search:
                search_condition = or_(
                    self.tenant_model.tenant_name.ilike(f"%{search}%"),
                    self.tenant_model.tenant_code.ilike(f"%{search}%")
                )
                conditions.append(search_condition)

            # 构建查询
            stmt = select(self.tenant_model).where(and_(*conditions, self.tenant_model.status == CommonStatus.ACTIVE)).order_by(
                self.tenant_model.created_at.desc()
            ).limit(limit)

            result = await self.session.execute(stmt)
            tenants = result.scalars().all()

            # 获取租户统计信息
            tenant_items = []
            for tenant in tenants:
                # 统计用户数量
                user_count_stmt = select(func.count(self.user_model.user_id)).where(
                    and_(
                        self.user_model.tenant_id == tenant.tenant_id,
                        self.tenant_model.status == CommonStatus.ACTIVE
                    )
                )
                user_count_result = await self.session.execute(user_count_stmt)
                user_count = user_count_result.scalar() or 0

                tenant_items.append({
                    "tenant_id": tenant.tenant_id,
                    "tenant_name": tenant.tenant_name,
                    "tenant_code": tenant.tenant_code,
                    "description": tenant.description,
                    "status": tenant.status,
                    "max_users": tenant.max_users,
                    "current_users": user_count,
                    "settings": tenant.settings or {},
                    "created_at": tenant.created_at.isoformat(),
                    "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None
                })

            # 获取总数
            total_stmt = select(func.count(self.tenant_model.tenant_id)).where(
                and_(*conditions)
            )
            total_result = await self.session.execute(total_stmt)
            total = total_result.scalar() or 0

            return {
                "tenants": tenant_items,
                "total": total,
            }

        except Exception as e:
            raise DatabaseError(f"查询租户列表失败：{str(e)}")

    async def get_tenant_detail(self, tenant_id: str) -> Dict[str, Any]:
        """获取租户详情"""
        try:
            # 先从缓存获取
            cached_result = await self.redis_repo.get(f"tenant:{tenant_id}")
            if cached_result:
                try:
                    return json.loads(cached_result)
                except Exception as e:
                    # 缓存数据损坏，删除缓存
                    await self.redis_repo.delete(f"tenant:{tenant_id}")
                    app_logger().info(f"缓存数据损坏，已删除缓存: tenant:{tenant_id}, error:{str(e)}")

            # 从数据库获取租户详情
            stmt = select(self.tenant_model).where(
                and_(
                    self.tenant_model.tenant_id == tenant_id,
                    self.tenant_model.status != CommonStatus.DELETED
                )
            )
            result = await self.session.execute(stmt)
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise NotFoundError("租户", tenant_id)

            # 获取统计信息
            statistics = await self._get_tenant_statistics(tenant_id)

            # 构建结果
            tenant_detail = {
                "tenant_id": tenant.tenant_id,
                "tenant_name": tenant.tenant_name,
                "tenant_code": tenant.tenant_code,
                "description": tenant.description,
                "status": tenant.status,
                "max_users": tenant.max_users,
                "current_users": statistics["user_count"],
                "settings": tenant.settings or {},
                "statistics": statistics,
                "created_at": tenant.created_at.isoformat(),
                "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None
            }

            # 缓存结果
            await self.redis_repo.set(f"tenant:{tenant_id}", json.dumps(tenant_detail,ensure_ascii=False), ttl=3600)

            return tenant_detail

        except NotFoundError:
            raise
        except Exception as e:
            raise DatabaseError(f"获取租户详情失败：{str(e)}")

    async def _get_tenant_statistics(self, tenant_id: str) -> Dict[str, Any]:
        """获取租户统计信息"""
        # 用户数量统计
        user_count_stmt = select(func.count(self.user_model.user_id)).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        user_count_result = await self.session.execute(user_count_stmt)
        user_count = user_count_result.scalar() or 0

        # 活跃用户数量统计
        active_user_count_stmt = select(func.count(self.user_model.user_id)).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.status == CommonStatus.ACTIVE,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        active_user_count_result = await self.session.execute(active_user_count_stmt)
        active_user_count = active_user_count_result.scalar() or 0

        # 角色数量统计
        role_count_stmt = select(func.count(self.role_model.role_id)).where(
            and_(
                self.role_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        role_count_result = await self.session.execute(role_count_stmt)
        role_count = role_count_result.scalar() or 0

        # 权限数量统计
        permission_count_stmt = select(func.count(self.permission_model.permission_id)).where(
            and_(
                self.permission_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        permission_count_result = await self.session.execute(permission_count_stmt)
        permission_count = permission_count_result.scalar() or 0

        return {
            "user_count": user_count,
            "active_user_count": active_user_count,
            "role_count": role_count,
            "permission_count": permission_count,
            "last_updated": datetime.now().isoformat()
        }
    
    async def update_tenant(
        self,
        tenant_id: str,
        tenant_name: Optional[str] = None,
        description: Optional[str] = None,
        max_users: Optional[int] = None,
        settings: Optional[Dict[str, Any]] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """更新租户信息"""
        try:
            # 1. 验证租户存在
            stmt = select(self.tenant_model).where(
                and_(
                    self.tenant_model.tenant_id == tenant_id,
                    self.tenant_model.status != CommonStatus.DELETED
                )
            )
            result = await self.session.execute(stmt)
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise NotFoundError("租户", tenant_id)

            # 记录变更前的值
            old_values = {
                "tenant_name": tenant.tenant_name,
                "description": tenant.description,
                "max_users": tenant.max_users,
                "settings": tenant.settings,
                "status": tenant.status
            }

            # 2. 验证更新数据
            if tenant_name is not None:
                if not tenant_name or len(tenant_name.strip()) < 2:
                    raise ValidationError("租户名称不能为空且长度不能少于2个字符")
                if len(tenant_name) > 100:
                    raise ValidationError("租户名称长度不能超过100个字符")

                # 检查名称唯一性（排除当前租户）
                name_check_stmt = select(self.tenant_model).where(
                    and_(
                        self.tenant_model.tenant_name == tenant_name,
                        self.tenant_model.tenant_id != tenant_id,
                        self.tenant_model.status != CommonStatus.DELETED
                    )
                )
                name_check_result = await self.session.execute(name_check_stmt)
                if name_check_result.scalar_one_or_none():
                    raise DuplicateResourceError("租户", "tenant_name", tenant_name)

                tenant.tenant_name = tenant_name.strip()

            if description is not None:
                tenant.description = description

            if max_users is not None:
                if max_users < 1:
                    raise ValidationError("最大用户数必须大于0")
                tenant.max_users = max_users

            if settings is not None:
                tenant.settings = settings

            if status is not None:
                # 验证状态值
                valid_statuses = [CommonStatus.ACTIVE, CommonStatus.INACTIVE,
                                CommonStatus.PENDING, CommonStatus.SUSPENDED]
                if status not in valid_statuses:
                    raise ValidationError(f"无效的状态值：{status}")
                tenant.status = status

            # 更新时间戳
            tenant.updated_at = datetime.now()

            # 3. 更新数据库记录
            await self.session.commit()

            # 记录变更内容
            changes = {}
            for key, old_value in old_values.items():
                new_value = getattr(tenant, key)
                if old_value != new_value:
                    changes[key] = {"old": old_value, "new": new_value}

            # 4. 记录审计日志
            if changes:
                await self._create_audit_log(
                    tenant_id=tenant_id,
                    action="tenant:update",
                    resource_type="tenant",
                    resource_id=tenant_id,
                    details={"changes": changes}
                )

            # 5. 清理相关缓存
            await self.redis_repo.delete(f"tenant:{tenant_id}")
            # 统计用户数量
            user_count_stmt = select(func.count(self.user_model.user_id)).where(
                and_(
                    self.user_model.tenant_id == tenant.tenant_id,
                    self.tenant_model.status != CommonStatus.DELETED
                )
            )
            user_count_result = await self.session.execute(user_count_stmt)
            user_count = user_count_result.scalar() or 0
            # 构建返回结果
            result = {
                "tenant_id": tenant.tenant_id,
                "tenant_name": tenant.tenant_name,
                "tenant_code": tenant.tenant_code,
                "description": tenant.description,
                "status": tenant.status,
                "max_users": tenant.max_users,
                "current_users":user_count,
                "settings": tenant.settings or {},
                "created_at": tenant.created_at.isoformat(),
                "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else "",
                "changes": changes
            }

            return result

        except (NotFoundError, ValidationError, DuplicateResourceError):
            await self.session.rollback()
            raise
        except IntegrityError as e:
            await self.session.rollback()
            raise DatabaseError(f"租户更新失败：数据库约束错误 - {str(e)}")
        except Exception as e:
            await self.session.rollback()
            raise DatabaseError(f"租户更新失败：{str(e)}")
    
    async def delete_tenant(
        self,
        tenant_id: str,
    ) -> Dict[str, Any]:
        """删除租户"""
        try:
            # 1. 验证租户存在
            stmt = select(self.tenant_model).where(
                and_(
                    self.tenant_model.tenant_id == tenant_id,
                    self.tenant_model.status != CommonStatus.DELETED
                )
            )
            result = await self.session.execute(stmt)
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise NotFoundError("租户", tenant_id)

            # 2. 检查依赖关系
            dependency_check = await self._check_tenant_dependencies(tenant_id)
            if dependency_check["has_dependencies"]:
                raise BusinessError(
                    f"无法删除租户，存在依赖关系：{dependency_check['dependencies']}"
                )

            # 3. 获取删除前的统计信息
            cleanup_summary = await self._get_tenant_cleanup_summary(tenant_id)

            # 4. 执行删除操作
            # 软删除：标记删除时间
            tenant.deleted_at = datetime.now()
            tenant.status = CommonStatus.DELETED

            # 软删除相关数据
            await self._soft_delete_tenant_data(tenant_id)
            # if delete_type == "soft":


            # else:
            #     # 硬删除：物理删除数据
            #     if backup_before_delete:
            #         backup_info = await self._backup_tenant_data(tenant_id)
            #         cleanup_summary["backup_info"] = backup_info
            #
            #     await self._hard_delete_tenant_data(tenant_id)
            #     await self.session.delete(tenant)

            # 提交事务
            await self.session.commit()

            # 5. 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                action="tenant:delete",
                resource_type="tenant",
                resource_id=tenant_id,
                details={
                    "cleanup_summary": cleanup_summary
                }
            )

            # 6. 清理相关缓存
            await self._cleanup_tenant_cache(tenant_id)

            return {
                "tenant_id": tenant_id,
                "status": CommonStatus.DELETED,
                "deleted_at": datetime.now().isoformat(),
                "cleanup_summary": cleanup_summary
            }

        except (NotFoundError, BusinessError):
            await self.session.rollback()
            raise
        except Exception as e:
            await self.session.rollback()
            raise DatabaseError(f"租户删除失败：{str(e)}")

    async def _check_tenant_dependencies(self, tenant_id: str) -> Dict[str, Any]:
        """检查租户依赖关系"""
        dependencies = []

        # 检查活跃用户
        active_users_stmt = select(func.count(self.user_model.user_id)).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.status == CommonStatus.ACTIVE,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        active_users_result = await self.session.execute(active_users_stmt)
        active_users_count = active_users_result.scalar() or 0

        if active_users_count > 0:
            dependencies.append(f"活跃用户: {active_users_count}个")

        # 可以添加其他依赖检查，如：
        # - 正在进行的任务
        # - 未完成的订单
        # - 存储的文档等

        return {
            "has_dependencies": len(dependencies) > 0,
            "dependencies": dependencies
        }

    async def _get_tenant_cleanup_summary(self, tenant_id: str) -> Dict[str, Any]:
        """获取租户清理摘要"""
        # 统计用户数量
        users_count_stmt = select(func.count(self.user_model.user_id)).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        users_count_result = await self.session.execute(users_count_stmt)
        users_count = users_count_result.scalar() or 0

        # 统计角色数量
        roles_count_stmt = select(func.count(self.role_model.role_id)).where(
            and_(
                self.role_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        roles_count_result = await self.session.execute(roles_count_stmt)
        roles_count = roles_count_result.scalar() or 0

        # 统计权限数量
        permissions_count_stmt = select(func.count(self.permission_model.permission_id)).where(
            and_(
                self.permission_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        permissions_count_result = await self.session.execute(permissions_count_stmt)
        permissions_count = permissions_count_result.scalar() or 0

        return {
            "users_archived": users_count,
            "roles_removed": roles_count,
            "permissions_removed": permissions_count
        }

    async def _soft_delete_tenant_data(self, tenant_id: str) -> None:
        """软删除租户相关数据"""
        current_time = datetime.now()

        # 软删除用户
        users_stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        users_result = await self.session.execute(users_stmt)
        users = users_result.scalars().all()

        for user in users:
            user.deleted_at = current_time
            user.status = CommonStatus.DELETED

        # 软删除角色
        roles_stmt = select(self.role_model).where(
            and_(
                self.role_model.tenant_id == tenant_id,
                self.role_model.deleted_at.is_(None)
            )
        )
        roles_result = await self.session.execute(roles_stmt)
        roles = roles_result.scalars().all()

        for role in roles:
            role.deleted_at = current_time
            role.status = CommonStatus.DELETED

        # 软删除权限
        permissions_stmt = select(self.permission_model).where(
            and_(
                self.permission_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        permissions_result = await self.session.execute(permissions_stmt)
        permissions = permissions_result.scalars().all()

        for permission in permissions:
            permission.deleted_at = current_time
            permission.status = CommonStatus.DELETED

    # async def _hard_delete_tenant_data(self, tenant_id: str) -> None:
    #     """硬删除租户相关数据"""
    #     # 删除用户角色关联
    #     user_roles_stmt = select(self.user_role_model).where(
    #         self.user_role_model.tenant_id == tenant_id
    #     )
    #     user_roles_result = await self.session.execute(user_roles_stmt)
    #     user_roles = user_roles_result.scalars().all()
    #     for user_role in user_roles:
    #         await self.session.delete(user_role)
    #
    #     # 删除角色权限关联
    #     role_permissions_stmt = select(self.role_permission_model).where(
    #         self.role_permission_model.tenant_id == tenant_id
    #     )
    #     role_permissions_result = await self.session.execute(role_permissions_stmt)
    #     role_permissions = role_permissions_result.scalars().all()
    #     for role_permission in role_permissions:
    #         await self.session.delete(role_permission)
    #
    #     # 删除用户
    #     users_stmt = select(self.user_model).where(
    #         self.user_model.tenant_id == tenant_id
    #     )
    #     users_result = await self.session.execute(users_stmt)
    #     users = users_result.scalars().all()
    #     for user in users:
    #         await self.session.delete(user)
    #
    #     # 删除角色
    #     roles_stmt = select(self.role_model).where(
    #         self.role_model.tenant_id == tenant_id
    #     )
    #     roles_result = await self.session.execute(roles_stmt)
    #     roles = roles_result.scalars().all()
    #     for role in roles:
    #         await self.session.delete(role)
    #
    #     # 删除权限
    #     permissions_stmt = select(self.permission_model).where(
    #         self.permission_model.tenant_id == tenant_id
    #     )
    #     permissions_result = await self.session.execute(permissions_stmt)
    #     permissions = permissions_result.scalars().all()
    #     for permission in permissions:
    #         await self.session.delete(permission)

    # async def _backup_tenant_data(self, tenant_id: str) -> Dict[str, Any]:
    #     """备份租户数据"""
    #     # 这里应该实现实际的备份逻辑
    #     # 可以导出到文件、对象存储等
    #     backup_id = str(uuid.uuid4())
    #     backup_time = datetime.now()
    #
    #     # 模拟备份过程
    #     return {
    #         "backup_id": backup_id,
    #         "backup_time": backup_time.isoformat(),
    #         "backup_url": f"https://backups.example.com/tenants/{tenant_id}/{backup_id}.zip",
    #         "backup_size": "125MB"  # 模拟数据
    #     }

    async def _cleanup_tenant_cache(self, tenant_id: str) -> None:
        """清理租户相关缓存"""
        # 清理租户基本信息缓存
        await self.redis_repo.delete(f"tenant:{tenant_id}")

        # 清理租户统计缓存
        await self.redis_repo.delete(f"tenant_stats:{tenant_id}")

        # 清理用户权限缓存
        await self.redis_repo.delete_pattern(f"user_perms:{tenant_id}:*")

        # 清理角色权限缓存
        await self.redis_repo.delete_pattern(f"role_perms:{tenant_id}:*")

    # ================================
    # 2.2 租户配置管理功能
    # ================================

    # 默认租户配置模板
    DEFAULT_TENANT_SETTINGS = {
        "password_policy": {
            "min_length": 8,
            "max_length": 128,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_digits": True,
            "require_special_chars": True,
            "special_chars": "!@#$%^&*()_+-=[]{}|;:,.<>?",
            "password_history_count": 5,
        },
        "session_config": {
            "session_timeout": 7200,  # 2小时
            "max_concurrent_sessions": 5,
            "idle_timeout": 1800,  # 30分钟
            "remember_me_duration": 2592000  # 30天
        },
        "security_config": {
            "max_login_attempts": 5,
            "account_lockout_duration": 1800,  # 30分钟
            "require_2fa": False,
            "ip_whitelist_enabled": False,
            "ip_whitelist": []
        },
        "feature_modules": {
            "user_management": True,
            "role_management": True,
            "permission_management": True,
            "audit_logs": True,
            "data_export": True,
            "api_access": True
        },
        "storage_config": {
            "max_storage_mb": 1024,  # 1GB
            "max_file_size_mb": 100,
            "allowed_file_types": ["pdf", "doc", "docx", "xls", "xlsx", "jpg", "png", "gif"]
        },
        "notification_config": {
            "email_notifications": True,
            "sms_notifications": False,
            "system_notifications": True
        }
    }

    async def get_tenant_settings(self, tenant_id: str) -> Dict[str, Any]:
        """获取租户配置信息"""
        try:
            # 1. 验证租户存在
            stmt = select(self.tenant_model).where(
                and_(
                    self.tenant_model.tenant_id == tenant_id,
                    self.tenant_model.status != CommonStatus.DELETED
                )
            )
            result = await self.session.execute(stmt)
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise NotFoundError("租户", tenant_id)

            # 2. 获取当前配置
            current_settings = tenant.settings or {}

            # 3. 合并默认配置
            merged_settings = self._merge_settings_with_defaults(current_settings)

            # 4. 添加配置元数据
            settings_info = {
                "tenant_id": tenant_id,
                "settings": merged_settings,
                "last_updated": tenant.updated_at.isoformat() if tenant.updated_at else None,
                "settings_version": current_settings.get("_version", "1.0"),
                "is_default": len(current_settings) == 0
            }

            return settings_info

        except NotFoundError:
            raise
        except Exception as e:
            raise DatabaseError(f"获取租户配置失败：{str(e)}")

    async def update_tenant_settings(
        self,
        tenant_id: str,
        settings_update: Dict[str, Any],
        user_id: Optional[str] = None,
        merge_mode: str = "merge"  # merge, replace, patch
    ) -> Dict[str, Any]:
        """更新租户配置信息"""
        try:
            # 1. 验证租户存在
            stmt = select(self.tenant_model).where(
                and_(
                    self.tenant_model.tenant_id == tenant_id,
                    self.tenant_model.status != CommonStatus.DELETED
                )
            )
            result = await self.session.execute(stmt)
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise NotFoundError("租户", tenant_id)

            # 2. 记录变更前的配置
            old_settings = tenant.settings or {}

            # 3. 验证新配置
            validated_settings = await self.validate_tenant_settings(settings_update)

            # 4. 根据合并模式处理配置
            if merge_mode == "replace":
                # 完全替换
                new_settings = validated_settings
            elif merge_mode == "patch":
                # 深度合并，只更新指定的字段
                new_settings = self._deep_merge_settings(old_settings, validated_settings)
            else:  # merge
                # 合并模式，保留未指定的配置
                new_settings = {**old_settings, **validated_settings}

            # 5. 添加版本信息
            new_settings["_version"] = "1.0"
            new_settings["_updated_at"] = datetime.now().isoformat()
            new_settings["_updated_by"] = user_id

            # 6. 更新数据库
            tenant.settings = new_settings
            tenant.updated_at = datetime.now()
            await self.session.commit()

            # 7. 清理缓存
            await self.redis_repo.delete(f"tenant:{tenant_id}")
            await self.redis_repo.delete(f"tenant_settings:{tenant_id}")

            # 8. 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="tenant:settings:update",
                resource_type="tenant_settings",
                resource_id=tenant_id,
                details={
                    "merge_mode": merge_mode,
                    "updated_keys": list(validated_settings.keys()),
                    "old_settings_hash": hash(str(old_settings)),
                    "new_settings_hash": hash(str(new_settings))
                }
            )

            # 9. 返回更新后的配置
            return {
                "tenant_id": tenant_id,
                "settings": new_settings,
                "merge_mode": merge_mode,
                "updated_keys": list(validated_settings.keys()),
                "updated_at": datetime.now().isoformat()
            }

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.session.rollback()
            raise DatabaseError(f"更新租户配置失败：{str(e)}")

    async def validate_tenant_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """验证租户配置信息"""
        validated_settings = {}

        # 验证密码策略配置
        if "password_policy" in settings:
            validated_settings["password_policy"] = await self._validate_password_policy(
                settings["password_policy"]
            )

        # 验证会话配置
        if "session_config" in settings:
            validated_settings["session_config"] = await self._validate_session_config(
                settings["session_config"]
            )

        # 验证安全配置
        if "security_config" in settings:
            validated_settings["security_config"] = await self._validate_security_config(
                settings["security_config"]
            )

        # 验证功能模块配置
        if "feature_modules" in settings:
            validated_settings["feature_modules"] = await self._validate_feature_modules(
                settings["feature_modules"]
            )

        # 验证存储配置
        if "storage_config" in settings:
            validated_settings["storage_config"] = await self._validate_storage_config(
                settings["storage_config"]
            )

        # 验证通知配置
        if "notification_config" in settings:
            validated_settings["notification_config"] = await self._validate_notification_config(
                settings["notification_config"]
            )

        return validated_settings

    async def _create_audit_log(
        self,
        tenant_id: str,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        user_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """创建审计日志"""
        try:
            audit_log = AuditLogBuilder.create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=details or {},
                result="success",
                ip_address=ip_address,
                user_agent=user_agent
            )

            self.session.add(audit_log)
            await self.session.flush()  # 确保审计日志被保存

        except Exception as e:
            # 审计日志失败不应该影响主要业务流程
            # 但应该记录错误
            print(f"创建审计日志失败: {str(e)}")  # 在实际应用中应该使用日志系统

    def _merge_settings_with_defaults(self, current_settings: Dict[str, Any]) -> Dict[str, Any]:
        """将当前配置与默认配置合并"""
        merged = {}

        for key, default_value in self.DEFAULT_TENANT_SETTINGS.items():
            if key in current_settings:
                if isinstance(default_value, dict) and isinstance(current_settings[key], dict):
                    # 递归合并字典
                    merged[key] = {**default_value, **current_settings[key]}
                else:
                    merged[key] = current_settings[key]
            else:
                merged[key] = default_value

        # 添加当前配置中存在但默认配置中没有的键
        for key, value in current_settings.items():
            if key not in merged and not key.startswith("_"):
                merged[key] = value

        return merged

    def _deep_merge_settings(self, old_settings: Dict[str, Any], new_settings: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并配置"""
        result = old_settings.copy()

        for key, value in new_settings.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge_settings(result[key], value)
            else:
                result[key] = value

        return result

    async def _validate_password_policy(self, policy: Dict[str, Any]) -> Dict[str, Any]:
        """验证密码策略配置"""
        validated = {}

        # 验证密码长度
        min_length = policy.get("min_length", 8)
        max_length = policy.get("max_length", 128)

        if not isinstance(min_length, int) or min_length < 4:
            raise ValidationError("密码最小长度必须是大于等于4的整数")
        if not isinstance(max_length, int) or max_length > 256:
            raise ValidationError("密码最大长度必须是小于等于256的整数")
        if min_length >= max_length:
            raise ValidationError("密码最小长度必须小于最大长度")

        validated["min_length"] = min_length
        validated["max_length"] = max_length

        # 验证密码复杂性要求
        for key in ["require_uppercase", "require_lowercase", "require_digits", "require_special_chars"]:
            validated[key] = bool(policy.get(key, True))

        # 验证特殊字符集
        special_chars = policy.get("special_chars", "!@#$%^&*()_+-=[]{}|;:,.<>?")
        if not isinstance(special_chars, str) or len(special_chars) == 0:
            raise ValidationError("特殊字符集不能为空")
        validated["special_chars"] = special_chars

        # 验证密码历史记录数量
        history_count = policy.get("password_history_count", 5)
        if not isinstance(history_count, int) or history_count < 0 or history_count > 20:
            raise ValidationError("密码历史记录数量必须是0-20之间的整数")
        validated["password_history_count"] = history_count



        return validated

    async def _validate_session_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证会话配置"""
        validated = {}

        # 验证会话超时时间
        session_timeout = config.get("session_timeout", 7200)
        if not isinstance(session_timeout, int) or session_timeout < 300 or session_timeout > 86400:
            raise ValidationError("会话超时时间必须是300-86400秒之间的整数")
        validated["session_timeout"] = session_timeout

        # 验证最大并发会话数
        max_sessions = config.get("max_concurrent_sessions", 5)
        if not isinstance(max_sessions, int) or max_sessions < 1 or max_sessions > 50:
            raise ValidationError("最大并发会话数必须是1-50之间的整数")
        validated["max_concurrent_sessions"] = max_sessions

        # 验证空闲超时时间
        idle_timeout = config.get("idle_timeout", 1800)
        if not isinstance(idle_timeout, int) or idle_timeout < 60 or idle_timeout > session_timeout:
            raise ValidationError("空闲超时时间必须是60秒到会话超时时间之间的整数")
        validated["idle_timeout"] = idle_timeout

        # 验证记住我持续时间
        remember_duration = config.get("remember_me_duration", 2592000)
        if not isinstance(remember_duration, int) or remember_duration < 86400 or remember_duration > 7776000:
            raise ValidationError("记住我持续时间必须是1-90天之间")
        validated["remember_me_duration"] = remember_duration

        return validated

    async def _validate_security_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证安全配置"""
        validated = {}

        # 验证最大登录尝试次数
        max_attempts = config.get("max_login_attempts", 5)
        if not isinstance(max_attempts, int) or max_attempts < 3 or max_attempts > 20:
            raise ValidationError("最大登录尝试次数必须是3-20之间的整数")
        validated["max_login_attempts"] = max_attempts

        # 验证账户锁定时长
        lockout_duration = config.get("account_lockout_duration", 1800)
        if not isinstance(lockout_duration, int) or lockout_duration < 300 or lockout_duration > 86400:
            raise ValidationError("账户锁定时长必须是300-86400秒之间的整数")
        validated["account_lockout_duration"] = lockout_duration

        # 验证双因子认证设置
        validated["require_2fa"] = bool(config.get("require_2fa", False))

        # 验证IP白名单设置
        validated["ip_whitelist_enabled"] = bool(config.get("ip_whitelist_enabled", False))

        ip_whitelist = config.get("ip_whitelist", [])
        if not isinstance(ip_whitelist, list):
            raise ValidationError("IP白名单必须是数组格式")

        # 验证IP地址格式
        import ipaddress
        validated_ips = []
        for ip in ip_whitelist:
            try:
                ipaddress.ip_network(ip, strict=False)
                validated_ips.append(ip)
            except ValueError:
                raise ValidationError(f"无效的IP地址或网段：{ip}")

        validated["ip_whitelist"] = validated_ips

        return validated

    async def _validate_feature_modules(self, modules: Dict[str, Any]) -> Dict[str, Any]:
        """验证功能模块配置"""
        validated = {}

        # 定义可用的功能模块
        available_modules = [
            "user_management", "role_management", "permission_management",
            "audit_logs", "data_export", "api_access", "reporting",
            "integration", "backup", "monitoring"
        ]

        for module in available_modules:
            validated[module] = bool(modules.get(module, True))

        # 验证核心模块不能全部禁用
        core_modules = ["user_management", "role_management", "permission_management"]
        if not any(validated[module] for module in core_modules):
            raise ValidationError("至少需要启用一个核心管理模块")

        return validated

    async def _validate_storage_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证存储配置"""
        validated = {}

        # 验证最大存储空间（MB）
        max_storage = config.get("max_storage_mb", 1024)
        if not isinstance(max_storage, int) or max_storage < 100 or max_storage > 102400:
            raise ValidationError("最大存储空间必须是100MB-100GB之间的整数")
        validated["max_storage_mb"] = max_storage

        # 验证最大文件大小（MB）
        max_file_size = config.get("max_file_size_mb", 100)
        if not isinstance(max_file_size, int) or max_file_size < 1 or max_file_size > 1024:
            raise ValidationError("最大文件大小必须是1MB-1GB之间的整数")
        validated["max_file_size_mb"] = max_file_size

        # 验证允许的文件类型
        allowed_types = config.get("allowed_file_types", [])
        if not isinstance(allowed_types, list):
            raise ValidationError("允许的文件类型必须是数组格式")

        # 定义支持的文件类型
        supported_types = [
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            "txt", "csv", "json", "xml",
            "jpg", "jpeg", "png", "gif", "bmp", "svg",
            "mp4", "avi", "mov", "wmv",
            "zip", "rar", "7z", "tar", "gz"
        ]

        validated_types = []
        for file_type in allowed_types:
            if file_type.lower() in supported_types:
                validated_types.append(file_type.lower())
            else:
                raise ValidationError(f"不支持的文件类型：{file_type}")

        validated["allowed_file_types"] = validated_types

        return validated

    async def _validate_notification_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证通知配置"""
        validated = {}

        # 验证各种通知开关
        validated["email_notifications"] = bool(config.get("email_notifications", True))
        validated["sms_notifications"] = bool(config.get("sms_notifications", False))
        validated["system_notifications"] = bool(config.get("system_notifications", True))
        validated["push_notifications"] = bool(config.get("push_notifications", False))

        # 验证通知频率限制
        notification_limits = config.get("notification_limits", {})
        if isinstance(notification_limits, dict):
            validated_limits = {}

            # 每小时最大通知数
            max_per_hour = notification_limits.get("max_per_hour", 100)
            if not isinstance(max_per_hour, int) or max_per_hour < 1 or max_per_hour > 1000:
                raise ValidationError("每小时最大通知数必须是1-1000之间的整数")
            validated_limits["max_per_hour"] = max_per_hour

            # 每天最大通知数
            max_per_day = notification_limits.get("max_per_day", 1000)
            if not isinstance(max_per_day, int) or max_per_day < 10 or max_per_day > 10000:
                raise ValidationError("每天最大通知数必须是10-10000之间的整数")
            validated_limits["max_per_day"] = max_per_day

            validated["notification_limits"] = validated_limits

        return validated

    # ================================
    # 租户状态管理功能
    # ================================

    # 状态转换规则定义
    STATUS_TRANSITION_RULES = {
        CommonStatus.PENDING: [CommonStatus.ACTIVE, CommonStatus.SUSPENDED, CommonStatus.DELETED],
        CommonStatus.ACTIVE: [CommonStatus.SUSPENDED, CommonStatus.INACTIVE, CommonStatus.DELETED],
        CommonStatus.INACTIVE: [CommonStatus.ACTIVE, CommonStatus.SUSPENDED, CommonStatus.DELETED],
        CommonStatus.SUSPENDED: [CommonStatus.ACTIVE, CommonStatus.INACTIVE, CommonStatus.DELETED],
        CommonStatus.DELETED: []  # 删除状态不能转换到其他状态
    }

    # 状态转换原因枚举
    STATUS_CHANGE_REASONS = {
        "admin_action": "管理员操作",
        "quota_exceeded": "配额超限",
        "payment_overdue": "付款逾期",
        "security_violation": "安全违规",
        "maintenance": "系统维护",
        "user_request": "用户请求",
        "auto_management": "自动管理",
        "compliance_issue": "合规问题"
    }

    async def change_tenant_status(
        self,
        tenant_id: str,
        new_status: str,
        reason: str = "admin_action",
        comment: Optional[str] = None,
        user_id: Optional[str] = None,
        effective_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """变更租户状态"""
        try:
            # 1. 验证租户存在
            stmt = select(self.tenant_model).where(
                and_(
                    self.tenant_model.tenant_id == tenant_id,
                    self.tenant_model.status != CommonStatus.DELETED
                )
            )
            result = await self.session.execute(stmt)
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise NotFoundError("租户", tenant_id)

            # 2. 验证状态转换
            old_status = tenant.status
            await self.validate_status_transition(old_status, new_status, reason)

            # 3. 检查状态变更的前置条件
            await self._check_status_change_prerequisites(tenant_id, new_status, reason)

            # 4. 执行状态变更
            tenant.status = new_status
            tenant.updated_at = effective_time or datetime.now()

            # 5. 处理状态变更的副作用
            await self._handle_status_change_effects(tenant_id, old_status, new_status, reason)

            # 6. 提交数据库变更
            await self.session.commit()

            # 7. 清理相关缓存
            await self.redis_repo.delete(f"tenant:{tenant_id}")
            await self.redis_repo.delete(f"tenant_stats:{tenant_id}")

            # 8. 记录状态变更审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="tenant:status:change",
                resource_type="tenant",
                resource_id=tenant_id,
                details={
                    "old_status": old_status,
                    "new_status": new_status,
                    "reason": reason,
                    "comment": comment,
                    "effective_time": (effective_time or datetime.now()).isoformat()
                }
            )

            # 9. 发送状态变更通知
            await self._send_status_change_notification(tenant_id, old_status, new_status, reason)

            return {
                "tenant_id": tenant_id,
                "old_status": old_status,
                "new_status": new_status,
                "reason": reason,
                "comment": comment,
                "changed_at": (effective_time or datetime.now()).isoformat(),
                "changed_by": user_id
            }

        except (NotFoundError, ValidationError, BusinessError):
            raise
        except Exception as e:
            await self.session.rollback()
            raise DatabaseError(f"租户状态变更失败：{str(e)}")

    async def validate_status_transition(
        self,
        current_status: str,
        target_status: str,
        reason: str = "admin_action"
    ) -> bool:
        """验证状态转换是否合法"""

        # 1. 验证状态值有效性
        valid_statuses = [status for status in CommonStatus.all_status]
        if current_status not in valid_statuses:
            raise ValidationError(f"无效的当前状态：{current_status}")
        if target_status not in valid_statuses:
            raise ValidationError(f"无效的目标状态：{target_status}")

        # 2. 检查状态是否相同
        if current_status == target_status:
            raise ValidationError("目标状态与当前状态相同")

        # 3. 检查转换规则
        allowed_transitions = self.STATUS_TRANSITION_RULES.get(current_status, [])
        if target_status not in allowed_transitions:
            raise ValidationError(
                f"不允许从状态 '{current_status}' 转换到 '{target_status}'"
            )

        # 4. 验证变更原因
        if reason not in self.STATUS_CHANGE_REASONS:
            raise ValidationError(f"无效的状态变更原因：{reason}")

        # 5. 特殊状态转换的额外验证
        if target_status == CommonStatus.DELETED:
            # 转换到删除状态需要特殊权限验证
            if reason not in ["admin_action", "compliance_issue"]:
                raise ValidationError("删除租户需要管理员操作或合规要求")

        return True

    def get_status_transition_rules(self) -> Dict[str, Any]:
        """获取状态转换规则"""
        return {
            "transition_rules": self.STATUS_TRANSITION_RULES,
            "change_reasons": self.STATUS_CHANGE_REASONS,
            "status_descriptions": {
                CommonStatus.PENDING: "待激活 - 租户已创建但尚未激活",
                CommonStatus.ACTIVE: "正常 - 租户正常运行中",
                CommonStatus.INACTIVE: "非激活 - 租户暂时停用但可恢复",
                CommonStatus.SUSPENDED: "暂停 - 租户因违规或其他原因被暂停",
                CommonStatus.DELETED: "已删除 - 租户已被删除且不可恢复"
            }
        }

    async def _check_status_change_prerequisites(
        self,
        tenant_id: str,
        new_status: str,
        reason: str
    ) -> None:
        """检查状态变更的前置条件"""

        if new_status == CommonStatus.ACTIVE:
            # 激活租户前的检查
            await self._check_activation_prerequisites(tenant_id)

        elif new_status == CommonStatus.SUSPENDED:
            # 暂停租户前的检查
            await self._check_suspension_prerequisites(tenant_id, reason)

        elif new_status == CommonStatus.DELETED:
            # 删除租户前的检查
            await self._check_deletion_prerequisites(tenant_id, reason)

    async def _check_activation_prerequisites(self, tenant_id: str) -> None:
        """检查租户激活的前置条件"""
        # 检查是否有管理员用户
        admin_count_stmt = select(func.count(self.user_model.user_id)).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED,
                self.user_model.status == CommonStatus.ACTIVE
            )
        )
        admin_count_result = await self.session.execute(admin_count_stmt)
        admin_count = admin_count_result.scalar() or 0

        if admin_count == 0:
            raise BusinessError("租户激活失败：没有可用的管理员用户")

    async def _check_suspension_prerequisites(self, tenant_id: str, reason: str) -> None:
        """检查租户暂停的前置条件"""
        # 根据暂停原因进行不同的检查
        if reason == "quota_exceeded":
            # 检查配额使用情况
            quota_status = await self._check_tenant_quota_status(tenant_id)
            if not quota_status["exceeded"]:
                raise BusinessError("租户暂停失败：配额未超限")

    async def _check_deletion_prerequisites(self, tenant_id: str, reason: str) -> None:
        """检查租户删除的前置条件"""
        # 检查是否有活跃的业务数据
        if reason != "compliance_issue":  # 合规问题可以强制删除
            dependency_check = await self._check_tenant_dependencies(tenant_id)
            if dependency_check["has_dependencies"]:
                raise BusinessError(
                    f"租户删除失败：存在依赖关系 - {', '.join(dependency_check['dependencies'])}"
                )

    async def _handle_status_change_effects(
        self,
        tenant_id: str,
        old_status: str,
        new_status: str,
        reason: str
    ) -> None:
        """处理状态变更的副作用"""

        if new_status == CommonStatus.SUSPENDED:
            # 暂停租户时的处理
            await self._handle_tenant_suspension(tenant_id, reason)

        elif new_status == CommonStatus.ACTIVE and old_status in [CommonStatus.SUSPENDED, CommonStatus.INACTIVE]:
            # 恢复租户时的处理
            await self._handle_tenant_reactivation(tenant_id)

        elif new_status == CommonStatus.DELETED:
            # 删除租户时的处理
            await self._handle_tenant_deletion(tenant_id, reason)

    async def _handle_tenant_suspension(self, tenant_id: str, reason: str) -> None:
        """处理租户暂停"""
        # 1. 暂停所有用户会话
        # 这里应该调用会话管理服务来终止所有活跃会话

        # 2. 禁用API访问
        # 这里应该更新API访问控制

        # 3. 记录暂停时间和原因
        suspension_info = {
            "suspended_at": datetime.now().isoformat(),
            "suspension_reason": reason,
            "auto_resume_enabled": reason in ["maintenance", "quota_exceeded"]
        }

        # 缓存暂停信息
        await self.redis_repo.set(
            f"tenant_suspension:{tenant_id}",
            json.dumps(suspension_info, ensure_ascii=False),
            ttl=86400 * 30  # 30天
        )

    async def _handle_tenant_reactivation(self, tenant_id: str) -> None:
        """处理租户重新激活"""
        # 1. 清理暂停信息
        await self.redis_repo.delete(f"tenant_suspension:{tenant_id}")

        # 2. 恢复API访问
        # 这里应该恢复API访问权限

        # 3. 发送激活通知
        # 这里应该发送租户激活通知

    async def _handle_tenant_deletion(self, tenant_id: str, reason: str) -> None:
        """处理租户删除"""
        # 1. 标记删除时间
        deletion_info = {
            "deleted_at": datetime.now().isoformat(),
            "deletion_reason": reason,
            "can_recover": reason != "compliance_issue"
        }

        # 2. 缓存删除信息
        await self.redis_repo.set(
            f"tenant_deletion:{tenant_id}",
            json.dumps(deletion_info, ensure_ascii=False),
            ttl=86400 * 90  # 90天后彻底清理
        )

    async def _send_status_change_notification(
        self,
        tenant_id: str,
        old_status: str,
        new_status: str,
        reason: str
    ) -> None:
        """发送状态变更通知"""
        # 这里应该实现通知发送逻辑
        # 可以发送邮件、短信、系统通知等

        notification_data = {
            "tenant_id": tenant_id,
            "status_change": {
                "from": old_status,
                "to": new_status,
                "reason": self.STATUS_CHANGE_REASONS.get(reason, reason),
                "timestamp": datetime.now().isoformat()
            }
        }

        # 缓存通知信息，供通知服务处理
        await self.redis_repo.lpush(
            "tenant_status_notifications",
            json.dumps(notification_data, ensure_ascii=False)
        )

    # ================================
    # 自动状态管理功能
    # ================================

    async def auto_manage_tenant_status(self, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """自动管理租户状态"""
        try:
            results = {
                "processed_tenants": 0,
                "status_changes": [],
                "quota_violations": [],
                "errors": []
            }

            # 如果指定了租户ID，只处理该租户
            if tenant_id:
                tenants_to_check = [tenant_id]
            else:
                # 获取所有需要检查的租户
                tenants_to_check = await self._get_tenants_for_auto_management()

            for tid in tenants_to_check:
                try:
                    # 检查租户配额状态
                    quota_status = await self.check_tenant_quotas(tid)

                    # 检查是否需要状态变更
                    status_change = await self._determine_auto_status_change(tid, quota_status)

                    if status_change:
                        # 执行自动状态变更
                        change_result = await self.change_tenant_status(
                            tenant_id=tid,
                            new_status=status_change["new_status"],
                            reason=status_change["reason"],
                            comment=status_change["comment"],
                            user_id="system"
                        )
                        results["status_changes"].append(change_result)

                    # 记录配额违规情况
                    if quota_status["has_violations"]:
                        results["quota_violations"].append({
                            "tenant_id": tid,
                            "violations": quota_status["violations"]
                        })

                    results["processed_tenants"] += 1

                except Exception as e:
                    results["errors"].append({
                        "tenant_id": tid,
                        "error": str(e)
                    })

            return results

        except Exception as e:
            raise DatabaseError(f"自动状态管理失败：{str(e)}")

    async def check_tenant_quotas(self, tenant_id: str) -> Dict[str, Any]:
        """检查租户配额状态"""
        try:
            # 1. 获取租户信息
            stmt = select(self.tenant_model).where(
                and_(
                    self.tenant_model.tenant_id == tenant_id,
                    self.tenant_model.status != CommonStatus.DELETED
                )
            )
            result = await self.session.execute(stmt)
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise NotFoundError("租户", tenant_id)

            # 2. 获取租户配置
            settings = tenant.settings or {}

            # 3. 检查各项配额
            quota_status = {
                "tenant_id": tenant_id,
                "has_violations": False,
                "violations": [],
                "warnings": [],
                "quotas": {}
            }

            # 检查用户数量配额
            user_quota_result = await self._check_user_quota(tenant_id, tenant.max_users)
            quota_status["quotas"]["users"] = user_quota_result
            if user_quota_result["exceeded"]:
                quota_status["has_violations"] = True
                quota_status["violations"].append("用户数量超限")

            # 检查存储配额
            storage_config = settings.get("storage_config", {})
            if storage_config:
                storage_quota_result = await self._check_storage_quota(tenant_id, storage_config)
                quota_status["quotas"]["storage"] = storage_quota_result
                if storage_quota_result["exceeded"]:
                    quota_status["has_violations"] = True
                    quota_status["violations"].append("存储空间超限")

            # 检查API调用配额
            api_quota_result = await self._check_api_quota(tenant_id, settings)
            quota_status["quotas"]["api_calls"] = api_quota_result
            if api_quota_result["exceeded"]:
                quota_status["has_violations"] = True
                quota_status["violations"].append("API调用次数超限")

            # 检查会话配额
            session_config = settings.get("session_config", {})
            if session_config:
                session_quota_result = await self._check_session_quota(tenant_id, session_config)
                quota_status["quotas"]["sessions"] = session_quota_result
                if session_quota_result["exceeded"]:
                    quota_status["has_violations"] = True
                    quota_status["violations"].append("并发会话数超限")

            return quota_status

        except NotFoundError:
            raise
        except Exception as e:
            raise DatabaseError(f"检查租户配额失败：{str(e)}")

    async def handle_quota_exceeded(
        self,
        tenant_id: str,
        quota_type: str,
        action: str = "suspend"
    ) -> Dict[str, Any]:
        """处理配额超限情况"""
        try:
            # 1. 记录配额超限事件
            await self._create_audit_log(
                tenant_id=tenant_id,
                action="tenant:quota:exceeded",
                resource_type="tenant",
                resource_id=tenant_id,
                details={
                    "quota_type": quota_type,
                    "action": action,
                    "timestamp": datetime.now().isoformat()
                }
            )

            # 2. 根据配额类型和动作执行相应处理
            if action == "suspend":
                # 暂停租户
                result = await self.change_tenant_status(
                    tenant_id=tenant_id,
                    new_status=CommonStatus.SUSPENDED,
                    reason="quota_exceeded",
                    comment=f"配额超限：{quota_type}",
                    user_id="system"
                )
            elif action == "notify":
                # 发送通知
                await self._send_quota_exceeded_notification(tenant_id, quota_type)
                result = {"action": "notification_sent", "quota_type": quota_type}
            elif action == "limit":
                # 限制功能
                await self._apply_quota_limits(tenant_id, quota_type)
                result = {"action": "limits_applied", "quota_type": quota_type}
            else:
                raise ValidationError(f"无效的配额处理动作：{action}")

            return result

        except Exception as e:
            raise DatabaseError(f"处理配额超限失败：{str(e)}")

    async def _get_tenants_for_auto_management(self) -> List[str]:
        """获取需要自动管理的租户列表"""
        # 获取所有活跃和暂停的租户
        stmt = select(self.tenant_model.tenant_id).where(
            and_(
                self.tenant_model.status.in_([CommonStatus.ACTIVE, CommonStatus.SUSPENDED]),
                self.tenant_model.status != CommonStatus.DELETED
            )
        )
        result = await self.session.execute(stmt)
        return [row[0] for row in result.fetchall()]

    async def _determine_auto_status_change(
        self,
        tenant_id: str,
        quota_status: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """确定是否需要自动状态变更"""

        # 获取当前租户状态
        stmt = select(self.tenant_model.status).where(
            self.tenant_model.tenant_id == tenant_id
        )
        result = await self.session.execute(stmt)
        current_status = result.scalar_one()

        # 如果有配额违规且当前状态为活跃，则暂停
        if quota_status["has_violations"] and current_status == CommonStatus.ACTIVE:
            return {
                "new_status": CommonStatus.SUSPENDED,
                "reason": "quota_exceeded",
                "comment": f"自动暂停：{', '.join(quota_status['violations'])}"
            }

        # 如果没有配额违规且当前状态为暂停（因配额问题），则恢复
        if not quota_status["has_violations"] and current_status == CommonStatus.SUSPENDED:
            # 检查是否是因为配额问题被暂停的
            suspension_info = await self.redis_repo.get(f"tenant_suspension:{tenant_id}")
            if suspension_info:
                suspension_data = json.loads(suspension_info)
                if suspension_data.get("suspension_reason") == "quota_exceeded":
                    return {
                        "new_status": CommonStatus.ACTIVE,
                        "reason": "auto_management",
                        "comment": "自动恢复：配额问题已解决"
                    }

        return None

    async def _check_user_quota(self, tenant_id: str, max_users: int) -> Dict[str, Any]:
        """检查用户数量配额"""
        # 统计当前用户数
        current_users_stmt = select(func.count(self.user_model.user_id)).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.tenant_model.status != CommonStatus.DELETED,
                self.user_model.status.in_([CommonStatus.ACTIVE, CommonStatus.INACTIVE])
            )
        )
        result = await self.session.execute(current_users_stmt)
        current_count = result.scalar() or 0

        usage_percentage = (current_count / max_users * 100) if max_users > 0 else 0

        return {
            "quota_type": "users",
            "limit": max_users,
            "current": current_count,
            "available": max(0, max_users - current_count),
            "usage_percentage": round(usage_percentage, 2),
            "exceeded": current_count > max_users,
            "warning": usage_percentage >= 80  # 80%以上发出警告
        }

    async def _check_storage_quota(self, tenant_id: str, storage_config: Dict[str, Any]) -> Dict[str, Any]:
        """检查存储配额"""
        max_storage_mb = storage_config.get("max_storage_mb", 1024)

        # 这里应该实现实际的存储使用量统计
        # 目前返回模拟数据
        current_storage_mb = 0  # 实际应该查询文件存储系统

        usage_percentage = (current_storage_mb / max_storage_mb * 100) if max_storage_mb > 0 else 0

        return {
            "quota_type": "storage",
            "limit": max_storage_mb,
            "current": current_storage_mb,
            "available": max(0, max_storage_mb - current_storage_mb),
            "usage_percentage": round(usage_percentage, 2),
            "exceeded": current_storage_mb > max_storage_mb,
            "warning": usage_percentage >= 80,
            "unit": "MB"
        }

    async def _check_api_quota(self, tenant_id: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """检查API调用配额"""
        # 从配置中获取API配额限制
        api_config = settings.get("api_config", {})
        daily_limit = api_config.get("daily_api_calls", 10000)

        # 这里应该实现实际的API调用统计
        # 目前返回模拟数据
        current_calls = 0  # 实际应该查询API调用日志

        usage_percentage = (current_calls / daily_limit * 100) if daily_limit > 0 else 0

        return {
            "quota_type": "api_calls",
            "limit": daily_limit,
            "current": current_calls,
            "available": max(0, daily_limit - current_calls),
            "usage_percentage": round(usage_percentage, 2),
            "exceeded": current_calls > daily_limit,
            "warning": usage_percentage >= 80,
            "period": "daily"
        }

    async def _check_session_quota(self, tenant_id: str, session_config: Dict[str, Any]) -> Dict[str, Any]:
        """检查会话配额"""
        max_sessions = session_config.get("max_concurrent_sessions", 5)

        # 这里应该实现实际的会话统计
        # 目前返回模拟数据
        current_sessions = 0  # 实际应该查询会话管理系统

        usage_percentage = (current_sessions / max_sessions * 100) if max_sessions > 0 else 0

        return {
            "quota_type": "sessions",
            "limit": max_sessions,
            "current": current_sessions,
            "available": max(0, max_sessions - current_sessions),
            "usage_percentage": round(usage_percentage, 2),
            "exceeded": current_sessions > max_sessions,
            "warning": usage_percentage >= 80
        }

    async def _check_tenant_quota_status(self, tenant_id: str) -> Dict[str, Any]:
        """检查租户配额状态（简化版本）"""
        quota_status = await self.check_tenant_quotas(tenant_id)
        return {
            "exceeded": quota_status["has_violations"],
            "violations": quota_status["violations"]
        }

    async def _send_quota_exceeded_notification(self, tenant_id: str, quota_type: str) -> None:
        """发送配额超限通知"""
        notification_data = {
            "tenant_id": tenant_id,
            "type": "quota_exceeded",
            "quota_type": quota_type,
            "timestamp": datetime.now().isoformat(),
            "message": f"租户 {tenant_id} 的 {quota_type} 配额已超限"
        }

        # 将通知加入队列
        await self.redis_repo.lpush(
            "quota_notifications",
            json.dumps(notification_data, ensure_ascii=False)
        )

    async def _apply_quota_limits(self, tenant_id: str, quota_type: str) -> None:
        """应用配额限制"""
        # 根据配额类型应用相应的限制
        limit_info = {
            "tenant_id": tenant_id,
            "quota_type": quota_type,
            "limited_at": datetime.now().isoformat(),
            "restrictions": []
        }

        if quota_type == "users":
            # 限制新用户创建
            limit_info["restrictions"].append("禁止创建新用户")
        elif quota_type == "storage":
            # 限制文件上传
            limit_info["restrictions"].append("禁止文件上传")
        elif quota_type == "api_calls":
            # 限制API调用频率
            limit_info["restrictions"].append("限制API调用频率")
        elif quota_type == "sessions":
            # 限制新会话创建
            limit_info["restrictions"].append("限制新会话创建")

        # 缓存限制信息
        await self.redis_repo.set(
            f"tenant_limits:{tenant_id}:{quota_type}",
            json.dumps(limit_info, ensure_ascii=False),
            ttl=86400  # 24小时
        )
