/**
 * @file 开发调试工具
 * @description 提供开发环境下的调试面板和工具
 * @status 框架文件 - 完成
 */

import React, { useState, useEffect } from 'react'
import { useEventBus } from './EventBus'
import { moduleRegistry } from './ModuleLoader'

interface DebugEvent {
  id: string
  type: string
  data: any
  timestamp: number
}

interface DevToolsProps {
  enabled?: boolean
}

export function DevTools({ enabled = process.env.NODE_ENV === 'development' }: DevToolsProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<'events' | 'modules' | 'performance'>('events')
  const [events, setEvents] = useState<DebugEvent[]>([])
  const { emit } = useEventBus()

  // 监听所有事件
  useEffect(() => {
    if (!enabled) return

    const originalEmit = emit
    const wrappedEmit = (type: string, data: any) => {
      const event: DebugEvent = {
        id: Math.random().toString(36).substr(2, 9),
        type,
        data,
        timestamp: Date.now(),
      }
      
      setEvents(prev => [event, ...prev.slice(0, 99)]) // 保留最近100个事件
      return originalEmit(type, data)
    }

    // 这里需要更复杂的事件拦截逻辑
    // 简化版本，实际使用时需要更完善的实现
  }, [emit, enabled])

  if (!enabled) return null

  return (
    <>
      {/* 调试按钮 */}
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
          title="开发者工具"
        >
          🛠️
        </button>
      </div>

      {/* 调试面板 */}
      {isOpen && (
        <div className="fixed inset-0 z-40 bg-black bg-opacity-50" onClick={() => setIsOpen(false)}>
          <div
            className="fixed bottom-0 right-0 w-full md:w-96 h-96 bg-white border-l border-t shadow-xl"
            onClick={e => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="font-semibold">开发者工具</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            {/* 标签页 */}
            <div className="flex border-b">
              {[
                { key: 'events', label: '事件' },
                { key: 'modules', label: '模块' },
                { key: 'performance', label: '性能' },
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`px-4 py-2 text-sm font-medium ${
                    activeTab === tab.key
                      ? 'text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* 内容区域 */}
            <div className="flex-1 overflow-auto p-4">
              {activeTab === 'events' && <EventsPanel events={events} />}
              {activeTab === 'modules' && <ModulesPanel />}
              {activeTab === 'performance' && <PerformancePanel />}
            </div>
          </div>
        </div>
      )}
    </>
  )
}

// 事件面板
function EventsPanel({ events }: { events: DebugEvent[] }) {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">事件日志</h4>
        <span className="text-sm text-gray-500">{events.length} 个事件</span>
      </div>
      
      <div className="space-y-1 max-h-64 overflow-auto">
        {events.map(event => (
          <div key={event.id} className="p-2 bg-gray-50 rounded text-xs">
            <div className="flex items-center justify-between">
              <span className="font-mono text-blue-600">{event.type}</span>
              <span className="text-gray-500">
                {new Date(event.timestamp).toLocaleTimeString()}
              </span>
            </div>
            <pre className="mt-1 text-gray-700 whitespace-pre-wrap">
              {JSON.stringify(event.data, null, 2)}
            </pre>
          </div>
        ))}
      </div>
    </div>
  )
}

// 模块面板
function ModulesPanel() {
  const modules = moduleRegistry.getAll()

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">已注册模块</h4>
        <span className="text-sm text-gray-500">{modules.length} 个模块</span>
      </div>
      
      <div className="space-y-2">
        {modules.map(module => (
          <div key={module.config.id} className="p-3 bg-gray-50 rounded">
            <div className="flex items-center justify-between">
              <span className="font-medium">{module.config.name}</span>
              <span className={`px-2 py-1 text-xs rounded ${
                moduleRegistry.isLoaded(module.config.id)
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {moduleRegistry.isLoaded(module.config.id) ? '已加载' : '未加载'}
              </span>
            </div>
            <div className="text-sm text-gray-600 mt-1">
              v{module.config.version} - {module.config.description}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// 性能面板
function PerformancePanel() {
  const [performanceData, setPerformanceData] = useState<any>(null)

  useEffect(() => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      setPerformanceData({
        domContentLoaded: Math.round(navigation.domContentLoadedEventEnd - navigation.navigationStart),
        loadComplete: Math.round(navigation.loadEventEnd - navigation.navigationStart),
        firstPaint: Math.round(performance.getEntriesByName('first-paint')[0]?.startTime || 0),
        firstContentfulPaint: Math.round(performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0),
      })
    }
  }, [])

  return (
    <div className="space-y-2">
      <h4 className="font-medium">性能指标</h4>
      
      {performanceData && (
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>DOM 内容加载:</span>
            <span className="font-mono">{performanceData.domContentLoaded}ms</span>
          </div>
          <div className="flex justify-between">
            <span>页面加载完成:</span>
            <span className="font-mono">{performanceData.loadComplete}ms</span>
          </div>
          <div className="flex justify-between">
            <span>首次绘制:</span>
            <span className="font-mono">{performanceData.firstPaint}ms</span>
          </div>
          <div className="flex justify-between">
            <span>首次内容绘制:</span>
            <span className="font-mono">{performanceData.firstContentfulPaint}ms</span>
          </div>
        </div>
      )}
    </div>
  )
}
