import importlib
from typing import Dict, Type

from commonlib.configs.services.connector_priority.connector_priority_config import \
    ConnectorPriorityConfig
from commonlib.core.logging.tsif_logging import app_logger
from commonlib.storages.base import BaseConnector

_CONNECTOR_MAPPING = {
    "redis": "commonlib.storages.persistence.redis.client.RedisConnector",
    "mysql": "commonlib.storages.persistence.mysql.client.MySQLConnector",
    "mongodb": "commonlib.storages.persistence.mongodb.client.MongoConnector",
    "postgres": "commonlib.storages.persistence.postgres.client.PostgresConnector",
    "rabbitmq": "commonlib.storages.queue.rabbitmq.client.RabbitMQConnector",
}


class ConnectorLoadError(Exception):
    """连接器加载异常"""

    def __init__(self, db_type: str, path: str, original_error: Exception):
        self.db_type = db_type
        self.path = path
        self.original_error = original_error
        super().__init__(
            f"Failed to load connector {db_type} from {path}: {original_error}"
        )


def _load_connector(db_type: str, path: str) -> Type[BaseConnector]:
    """
    动态加载连接器类

    Args:
        db_type: 数据库类型标识
        path: 连接器类的模块路径

    Returns:
        Type[BaseConnector]: 连接器类

    Raises:
        ConnectorLoadError: 连接器加载失败时抛出
    """
    try:
        module_name, class_name = path.rsplit(".", 1)
        app_logger.debug(
            f"Loading connector class",
            db_type=db_type,
            module=module_name,
            class_name=class_name,
        )

        module = importlib.import_module(module_name)
        connector_class = getattr(module, class_name)

        # 类型安全检查
        if not issubclass(connector_class, BaseConnector):
            raise TypeError(f"Class {class_name} is not a subclass of BaseConnector")

        app_logger.debug(
            f"Successfully loaded connector", db_type=db_type, class_name=class_name
        )
        return connector_class

    except ValueError as e:
        app_logger.error(
            f"Invalid connector path format", db_type=db_type, path=path, error=str(e)
        )
        raise ConnectorLoadError(db_type, path, e)
    except ImportError as e:
        app_logger.error(
            f"Failed to import connector module",
            db_type=db_type,
            path=path,
            error=str(e),
        )
        raise ConnectorLoadError(db_type, path, e)
    except AttributeError as e:
        app_logger.error(
            f"Connector class not found in module",
            db_type=db_type,
            path=path,
            error=str(e),
        )
        raise ConnectorLoadError(db_type, path, e)
    except TypeError as e:
        app_logger.error(
            f"Invalid connector class type", db_type=db_type, path=path, error=str(e)
        )
        raise ConnectorLoadError(db_type, path, e)
    except Exception as e:
        app_logger.error(
            f"Unexpected error loading connector",
            db_type=db_type,
            path=path,
            error=str(e),
            exception=True,
        )
        raise ConnectorLoadError(db_type, path, e)


def populate_connectors(config: ConnectorPriorityConfig) -> Dict[str, dict]:
    """
    按配置动态注册连接器到容器

    Args:
        config: 连接器优先级配置

    Returns:
        Dict[str, dict]: 连接器注册表，包含连接器类和优先级信息

    Raises:
        ConnectorLoadError: 连接器加载失败时抛出
    """
    registry = {}
    active_connectors = config.active_connectors()

    app_logger.info(
        f"Populating connectors",
        active_count=len(active_connectors),
        available_types=list(_CONNECTOR_MAPPING.keys()),
    )

    for db_type, priority in active_connectors.items():
        if db_type not in _CONNECTOR_MAPPING:
            app_logger.warning(
                f"Connector type not supported, skipping",
                db_type=db_type,
                supported_types=list(_CONNECTOR_MAPPING.keys()),
            )
            continue

        try:
            connector_class = _load_connector(db_type, _CONNECTOR_MAPPING[db_type])
            registry[db_type] = {
                "class": connector_class,
                "priority": priority,
            }
            app_logger.info(
                f"Connector registered successfully",
                db_type=db_type,
                class_name=connector_class.__name__,
                connect_priority=priority.connect_priority,
                shutdown_priority=priority.shutdown_priority,
            )
        except ConnectorLoadError:
            # 重新抛出，让调用方决定如何处理
            raise
        except Exception as e:
            # 包装未预期的异常
            app_logger.error(
                f"Unexpected error registering connector",
                db_type=db_type,
                error=str(e),
                exception=True,
            )
            raise ConnectorLoadError(db_type, _CONNECTOR_MAPPING[db_type], e)

    app_logger.info(
        f"Connectors population completed",
        registered_count=len(registry),
        registered_types=list(registry.keys()),
    )
    return registry
