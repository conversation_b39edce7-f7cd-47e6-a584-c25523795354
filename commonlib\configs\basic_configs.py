import json
from pathlib import Path

from commonlib.configs.frameworks.fastapi_config import FastApiSettings
from commonlib.configs.services.connector_priority.connector_priority_config import \
    ConnectorPriorityConfig
from commonlib.configs.services.middleware.middleware_configs import \
    MiddlewareConfig
from commonlib.configs.storages.persistence.mongodb_config import MongoDBConfig
from commonlib.configs.storages.persistence.mysql_config import MySQLConfig
from commonlib.configs.storages.persistence.postgres_config import \
    PostgresConfig
from commonlib.configs.storages.persistence.redis_config import RedisConfig
from commonlib.configs.storages.queue.celery_config import CeleryConfig
from commonlib.configs.storages.queue.rabbitmq_config import RabbitMQConfig
from loguru import logger
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class PersistenceConfig(BaseSettings):
    redis: RedisConfig = Field(default_factory=lambda: RedisConfig())
    mysql: MySQLConfig = Field(default_factory=lambda: MySQLConfig())
    postgres: PostgresConfig = Field(default_factory=lambda: PostgresConfig())
    mongodb: MongoDBConfig = Field(default_factory=lambda: MongoDBConfig())


class MessagingConfig(BaseSettings):
    rabbitmq: RabbitMQConfig = Field(default_factory=lambda: RabbitMQConfig())
    celery: CeleryConfig = Field(default_factory=lambda: CeleryConfig())


class BasicConfig(BaseSettings):
    """Main application configuration with integrated feature flags."""

    # 应用配置组
    application: FastApiSettings = Field(
        default_factory=FastApiSettings, description="FastAPI application settings"
    )
    middleware: MiddlewareConfig = Field(
        default_factory=MiddlewareConfig, description="Middleware configurations"
    )
    # 连接优先级配置
    connection_priority: ConnectorPriorityConfig = Field(
        default_factory=ConnectorPriorityConfig,
        description="ConnectorPriorityConfig configurations",
    )
    # 持久化配置组
    persistence: PersistenceConfig = Field(
        default_factory=PersistenceConfig,
        description="Database and cache configurations",
    )

    # 消息系统配置组
    messaging: MessagingConfig = Field(
        default_factory=MessagingConfig,
        description="Message queue and task queue configurations",
    )

    def export_to_file(self, file_path: Path) -> None:
        """Safely export configs to JSON file."""
        if not file_path.parent.exists():
            file_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            file_path.write_text(self.model_dump_json(indent=2, exclude_unset=True))
            logger.info(f"Configuration exported to {file_path}")
        except Exception as e:
            logger.error(f"Config export failed: {e}")
            raise

    def from_file(self, file_path: Path) -> "BasicConfig":
        """Load configuration from file with validation."""
        path = Path(file_path) if isinstance(file_path, str) else file_path

        if not path.exists():
            raise FileNotFoundError(f"配置文件不存在: {path}")
        try:
            self.__dict__.clear()
            data = json.loads(file_path.read_text(encoding="utf-8"))
            validated = self.model_validate(data)
            self.__dict__.update(validated.__dict__)
            logger.info(f"成功从JSON文件加载配置: {path}")
            return self
        except (IOError, json.JSONDecodeError) as e:
            logger.error(f"JSON文件格式错误 {path}: {e}")
            raise

        except Exception as e:
            logger.error(f"配置加载失败 {path}: {e}")
            raise ValueError(f"配置验证失败: {e}") from e

    model_config = SettingsConfigDict(
        extra="allow",
    )
