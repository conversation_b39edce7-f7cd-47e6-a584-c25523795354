"""
ServiceContainer单元测试

测试IAM服务容器的各项功能
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from dependency_injector import providers
from container import ServiceContainer
from services.tenant_service import TenantService
from services.user_service import UserService
from services.auth_service import AuthService
from security.jwt_manager import JWTManager
from security.session_manager import SessionManager
from security.security_utils import SecurityUtils


class TestServiceContainer:
    """ServiceContainer测试类"""

    @pytest.mark.unit
    def test_container_initialization(self):
        """测试容器初始化"""
        # Mock依赖容器
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证容器具有必要的providers
        assert hasattr(container, 'config')
        assert hasattr(container, 'infra')
        assert hasattr(container, 'session')
        assert hasattr(container, 'redis_repo')
        
        # 验证业务服务providers
        assert hasattr(container, 'tenant_service')
        assert hasattr(container, 'user_service')
        assert hasattr(container, 'auth_service')
        assert hasattr(container, 'role_service')
        assert hasattr(container, 'permission_service')

    @pytest.mark.unit
    def test_dependencies_containers(self):
        """测试依赖容器"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证config和infra是DependenciesContainer
        assert isinstance(container.config, providers.DependenciesContainer)
        assert isinstance(container.infra, providers.DependenciesContainer)

    @pytest.mark.unit
    def test_session_provider(self):
        """测试会话provider"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证session是Factory
        assert isinstance(container.session, providers.Factory)

    @pytest.mark.unit
    def test_redis_repo_provider(self):
        """测试Redis仓库provider"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证redis_repo是Factory
        assert isinstance(container.redis_repo, providers.Factory)

    @pytest.mark.unit
    def test_model_attributes(self):
        """测试模型属性"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证核心模型存在
        assert hasattr(container, 'user_model')
        assert hasattr(container, 'tenant_model')
        assert hasattr(container, 'role_model')
        assert hasattr(container, 'permission_model')
        
        # 验证关联关系模型
        assert hasattr(container, 'user_role_model')
        assert hasattr(container, 'role_permission_model')
        
        # 验证认证安全模型
        assert hasattr(container, 'user_session_history_model')
        assert hasattr(container, 'user_mfa_model')
        assert hasattr(container, 'verification_code_model')
        assert hasattr(container, 'password_history_model')

    @pytest.mark.unit
    def test_security_providers(self):
        """测试安全providers"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证安全providers都是Factory
        assert isinstance(container.jwt_manager, providers.Factory)
        assert isinstance(container.session_manager, providers.Factory)
        assert isinstance(container.cache_manager, providers.Factory)
        assert isinstance(container.security_utils, providers.Factory)

    @pytest.mark.unit
    def test_business_service_providers(self):
        """测试业务服务providers"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证业务服务都是Factory
        assert isinstance(container.tenant_service, providers.Factory)
        assert isinstance(container.user_service, providers.Factory)
        assert isinstance(container.auth_service, providers.Factory)
        assert isinstance(container.role_service, providers.Factory)
        assert isinstance(container.permission_service, providers.Factory)
        assert isinstance(container.rbac_service, providers.Factory)
        assert isinstance(container.audit_service, providers.Factory)

    @pytest.mark.unit
    def test_external_service_providers(self):
        """测试外部服务providers"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证外部服务providers
        assert isinstance(container.sms_provider, providers.Singleton)
        assert isinstance(container.sms_service, providers.Singleton)
        assert isinstance(container.email_service, providers.Singleton)
        assert isinstance(container.verification_service, providers.Singleton)

    @pytest.mark.unit
    def test_container_with_mocked_dependencies(self):
        """测试带Mock依赖的容器"""
        # Mock配置和基础设施容器
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_iam_service"
        
        mock_infra = MagicMock()
        mock_infra.postgres_session_provider.return_value = AsyncMock()
        mock_infra.redis_clint = AsyncMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock会话和Redis仓库
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        
        # 测试服务创建
        with patch.object(TenantService, '__init__', return_value=None) as mock_tenant_init:
            tenant_service = container.tenant_service()
            mock_tenant_init.assert_called_once()

    @pytest.mark.unit
    def test_jwt_manager_creation(self):
        """测试JWT管理器创建"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.redis_repo.override(mock_redis_repo)
        
        # Mock安全配置
        with patch('container.security_config_manager') as mock_security_config:
            mock_security_config.jwt_config.private_key = "test_private_key"
            mock_security_config.jwt_config.public_key = "test_public_key"
            mock_security_config.jwt_config.access_token_expire_minutes = 30
            mock_security_config.jwt_config.refresh_token_expire_days = 7
            mock_security_config.jwt_config.algorithm = "RS256"
            
            with patch.object(JWTManager, '__init__', return_value=None) as mock_init:
                jwt_manager = container.jwt_manager()
                mock_init.assert_called_once()

    @pytest.mark.unit
    def test_session_manager_creation(self):
        """测试会话管理器创建"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.redis_repo.override(mock_redis_repo)
        
        # Mock安全配置
        with patch('container.security_config_manager') as mock_security_config:
            mock_security_config.session_config.session_timeout_minutes = 60
            mock_security_config.session_config.max_concurrent_sessions = 5
            mock_security_config.session_config.enable_device_tracking = True
            
            with patch.object(SessionManager, '__init__', return_value=None) as mock_init:
                session_manager = container.session_manager()
                mock_init.assert_called_once()

    @pytest.mark.unit
    def test_user_service_creation(self):
        """测试用户服务创建"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_security_utils = MagicMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        container.security_utils.override(mock_security_utils)
        
        # 测试用户服务创建
        with patch.object(UserService, '__init__', return_value=None) as mock_init:
            user_service = container.user_service()
            mock_init.assert_called_once()

    @pytest.mark.unit
    def test_auth_service_creation(self):
        """测试认证服务创建"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_jwt_manager = MagicMock()
        mock_session_manager = AsyncMock()
        mock_security_utils = MagicMock()
        
        container.session.override(mock_session)
        container.redis_repo.override(mock_redis_repo)
        container.jwt_manager.override(mock_jwt_manager)
        container.session_manager.override(mock_session_manager)
        container.security_utils.override(mock_security_utils)
        
        # 测试认证服务创建
        with patch.object(AuthService, '__init__', return_value=None) as mock_init:
            auth_service = container.auth_service()
            mock_init.assert_called_once()

    @pytest.mark.unit
    def test_cross_container_dependencies(self):
        """测试跨容器依赖"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_iam_service"
        
        mock_infra = MagicMock()
        mock_infra.redis_clint = AsyncMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证Redis仓库依赖跨容器的redis_clint
        redis_repo_provider = container.redis_repo
        assert isinstance(redis_repo_provider, providers.Factory)
        
        # 验证会话依赖跨容器的postgres_session_provider
        session_provider = container.session
        assert isinstance(session_provider, providers.Factory)

    @pytest.mark.unit
    def test_service_dependency_injection(self):
        """测试服务依赖注入"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 验证租户服务的依赖注入配置
        tenant_service_provider = container.tenant_service
        assert isinstance(tenant_service_provider, providers.Factory)
        
        # 验证认证服务的复杂依赖注入配置
        auth_service_provider = container.auth_service
        assert isinstance(auth_service_provider, providers.Factory)
