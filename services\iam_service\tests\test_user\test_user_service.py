"""
用户服务测试

测试用户服务的核心功能
"""
import pytest
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

from services.iam_service.services.user_service import UserService
from domain_common.models.iam_models import User, Tenant, Role, UserRole, AuditLog
from domain_common.models import CommonStatus
from commonlib.exceptions.exceptions import ValidationError, NotFoundError, DuplicateResourceError


@pytest.fixture
async def user_service():
    """创建用户服务实例"""
    session = AsyncMock()
    redis_repo = AsyncMock()
    security_utils = MagicMock()
    security_utils.hash_password.return_value = "hashed_password"
    
    service = UserService(
        session=session,
        redis_repo=redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=MagicMock(),
        user_role_model=UserRole,
        role_permission_model=MagicMock(),
        audit_log_model=AuditLog,
        security_utils=security_utils
    )
    return service


@pytest.fixture
def sample_tenant():
    """示例租户"""
    return Tenant(
        tenant_id="tenant_123",
        tenant_name="测试租户",
        tenant_code="test_tenant",
        status=CommonStatus.ACTIVE
    )


@pytest.fixture
def sample_user():
    """示例用户"""
    return User(
        user_id="user_123",
        tenant_id="tenant_123",
        username="testuser",
        email="<EMAIL>",
        phone="13800138000",
        nickname="测试用户",
        password_hash="hashed_password",
        status=CommonStatus.ACTIVE,
        profile={"department": "IT"},
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


class TestUserCreation:
    """测试用户创建功能"""
    
    async def test_create_user_success(self, user_service, sample_tenant):
        """测试成功创建用户"""
        # Mock dependencies
        user_service._get_tenant_by_id = AsyncMock(return_value=sample_tenant)
        user_service._validate_user_uniqueness = AsyncMock()
        user_service._validate_password_policy = AsyncMock()
        user_service._assign_default_roles = AsyncMock()
        user_service._generate_activation_token = AsyncMock(return_value="token_123")
        user_service._send_welcome_email = AsyncMock()
        user_service._create_audit_log = AsyncMock()
        user_service._cache_user_info = AsyncMock()
        user_service.session.flush = AsyncMock()
        user_service.session.commit = AsyncMock()
        
        # Execute
        result = await user_service.create_user(
            tenant_id="tenant_123",
            username="newuser",
            email="<EMAIL>",
            password="Password123!",
            phone="13900139000",
            nickname="新用户"
        )
        
        # Verify
        assert result["username"] == "newuser"
        assert result["email"] == "<EMAIL>"
        assert result["status"] == CommonStatus.PENDING
        assert "user_id" in result
        user_service._validate_user_uniqueness.assert_called_once()
        user_service._validate_password_policy.assert_called_once()
        user_service.session.commit.assert_called_once()

    async def test_create_user_duplicate_username(self, user_service, sample_tenant):
        """测试创建重复用户名的用户"""
        # Mock dependencies
        user_service._get_tenant_by_id = AsyncMock(return_value=sample_tenant)
        user_service._validate_user_uniqueness = AsyncMock(
            side_effect=DuplicateResourceError("用户名已存在", "username", "testuser")
        )
        
        # Execute and verify exception
        with pytest.raises(DuplicateResourceError):
            await user_service.create_user(
                tenant_id="tenant_123",
                username="testuser",
                email="<EMAIL>",
                password="Password123!"
            )


class TestUserRegistration:
    """测试用户注册功能"""
    
    async def test_register_user_success(self, user_service, sample_tenant):
        """测试成功注册用户"""
        # Mock dependencies
        user_service._get_tenant_by_code = AsyncMock(return_value=sample_tenant)
        user_service._verify_code = AsyncMock()
        user_service._validate_user_uniqueness = AsyncMock()
        user_service._validate_password_policy = AsyncMock()
        user_service._generate_activation_token = AsyncMock(return_value="token_123")
        user_service._send_activation_email = AsyncMock()
        user_service._create_audit_log = AsyncMock()
        user_service.session.flush = AsyncMock()
        user_service.session.commit = AsyncMock()
        
        # Execute
        result = await user_service.register_user(
            tenant_code="test_tenant",
            username="newuser",
            email="<EMAIL>",
            password="Password123!",
            verification_code="123456",
            code_id="code_123"
        )
        
        # Verify
        assert result["username"] == "newuser"
        assert result["status"] == CommonStatus.PENDING
        assert result["activation_required"] is True
        user_service._verify_code.assert_called_once_with("code_123", "123456", "register")


class TestUserActivation:
    """测试用户激活功能"""
    
    async def test_activate_user_success(self, user_service, sample_user):
        """测试成功激活用户"""
        # Setup user in pending status
        sample_user.status = CommonStatus.PENDING
        
        # Mock dependencies
        user_service._verify_activation_token = AsyncMock(return_value="user_123")
        user_service._get_user_by_id_only = AsyncMock(return_value=sample_user)
        user_service._assign_default_roles = AsyncMock()
        user_service._delete_activation_token = AsyncMock()
        user_service._cache_user_info = AsyncMock()
        user_service._create_audit_log = AsyncMock()
        user_service.session.commit = AsyncMock()
        
        # Execute
        result = await user_service.activate_user("token_123")
        
        # Verify
        assert result["user_id"] == "user_123"
        assert result["status"] == CommonStatus.ACTIVE
        assert sample_user.status == CommonStatus.ACTIVE
        user_service._assign_default_roles.assert_called_once()
        user_service._delete_activation_token.assert_called_once()


class TestUserQuery:
    """测试用户查询功能"""
    
    async def test_get_user_detail_success(self, user_service, sample_user):
        """测试获取用户详情"""
        # Mock dependencies
        user_service._get_user_by_id = AsyncMock(return_value=sample_user)
        user_service._get_user_all_roles = AsyncMock(return_value=[])
        user_service._get_user_security_info = AsyncMock(return_value={
            "mfa_enabled": False,
            "password_changed_at": datetime.now().isoformat()
        })
        
        # Execute
        result = await user_service.get_user_detail(
            tenant_id="tenant_123",
            user_id="user_123"
        )
        
        # Verify
        assert result["user_id"] == "user_123"
        assert result["username"] == "testuser"
        assert result["email"] == "<EMAIL>"
        assert "security_settings" in result


class TestUserUpdate:
    """测试用户更新功能"""
    
    async def test_update_user_success(self, user_service, sample_user):
        """测试更新用户信息"""
        # Mock dependencies
        user_service._get_user_by_id = AsyncMock(return_value=sample_user)
        user_service._check_email_uniqueness = AsyncMock()
        user_service._validate_status_transition = AsyncMock()
        user_service._clear_user_cache = AsyncMock()
        user_service._get_user_all_roles = AsyncMock(return_value=[])
        user_service._create_audit_log = AsyncMock()
        user_service.session.commit = AsyncMock()
        
        # Execute
        result = await user_service.update_user(
            tenant_id="tenant_123",
            user_id="user_123",
            nickname="新昵称",
            email="<EMAIL>"
        )
        
        # Verify
        assert result["user_id"] == "user_123"
        assert sample_user.nickname == "新昵称"
        assert sample_user.email == "<EMAIL>"
        user_service._check_email_uniqueness.assert_called_once()
        user_service.session.commit.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
