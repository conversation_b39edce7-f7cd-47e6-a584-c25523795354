/**
 * @file 品牌展示区组件
 * @description 认证页面左侧的品牌展示区域，支持租户自定义
 */

import React from 'react'
import { motion } from 'framer-motion'
import clsx from 'clsx'
import { TenantInfo } from '@/types'
import { Logo } from '@/components/ui'

export interface BrandSectionProps {
  tenant?: TenantInfo
  showIllustration?: boolean
  customContent?: React.ReactNode
  className?: string
}

export const BrandSection: React.FC<BrandSectionProps> = ({
  tenant,
  showIllustration = true,
  customContent,
  className
}) => {
  // 获取品牌颜色
  const brandColors = tenant?.theme ? {
    primary: tenant.theme.primary_color,
    secondary: tenant.theme.secondary_color
  } : {
    primary: '#3b82f6',
    secondary: '#1e40af'
  }

  // 渐变背景样式
  const backgroundStyle = {
    background: tenant?.theme?.background_image 
      ? `url(${tenant.theme.background_image}) center/cover`
      : `linear-gradient(135deg, ${brandColors.primary} 0%, ${brandColors.secondary} 100%)`
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
      className={clsx(
        'relative flex flex-col items-center justify-center',
        'min-h-screen p-8 text-white',
        'bg-gradient-to-br from-blue-600 to-blue-800',
        className
      )}
      style={backgroundStyle}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-black bg-opacity-20" />
      
      {/* 内容容器 */}
      <div className="relative z-10 max-w-md text-center">
        {/* Logo区域 */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="mb-8"
        >
          <Logo
            src={tenant?.logo}
            alt={tenant?.display_name || '品牌Logo'}
            size="lg"
            variant="white"
            className="mx-auto mb-4"
          />
          
          {/* 租户名称 */}
          <h1 className="text-3xl font-bold mb-2">
            {tenant?.display_name || 'TSIF Micro'}
          </h1>
          
          {/* 副标题 */}
          <p className="text-lg opacity-90">
            {tenant?.name ? `${tenant.name} 工作区` : '微服务身份认证平台'}
          </p>
        </motion.div>

        {/* 产品特色说明 */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="mb-8"
        >
          <h2 className="text-xl font-semibold mb-4">
            安全 · 高效 · 可靠
          </h2>
          <ul className="space-y-2 text-sm opacity-90">
            <li className="flex items-center">
              <svg className="w-4 h-4 mr-2 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              企业级安全认证
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 mr-2 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              多租户架构支持
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 mr-2 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              灵活的权限管理
            </li>
          </ul>
        </motion.div>

        {/* 插画区域 */}
        {showIllustration && (
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="mb-8"
          >
            <div className="w-48 h-48 mx-auto bg-white bg-opacity-10 rounded-full flex items-center justify-center">
              <svg className="w-24 h-24 text-white opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
          </motion.div>
        )}

        {/* 自定义内容 */}
        {customContent && (
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            {customContent}
          </motion.div>
        )}

        {/* 底部信息 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.5 }}
          className="text-xs opacity-70"
        >
          <p>© 2024 TSIF Micro. All rights reserved.</p>
        </motion.div>
      </div>

      {/* 装饰性几何图形 */}
      <div className="absolute top-10 left-10 w-20 h-20 border border-white opacity-20 rounded-full" />
      <div className="absolute bottom-20 right-10 w-16 h-16 border border-white opacity-20 rounded-full" />
      <div className="absolute top-1/3 right-20 w-12 h-12 bg-white opacity-10 rounded-full" />
    </motion.div>
  )
}
