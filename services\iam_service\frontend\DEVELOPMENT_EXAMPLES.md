# IAM Service Frontend 开发示例

本文档提供了基于项目架构的具体开发示例，帮助开发者快速上手。

## 📋 目录
- [组件开发示例](#组件开发示例)
- [模块开发示例](#模块开发示例)
- [API 集成示例](#api-集成示例)
- [状态管理示例](#状态管理示例)
- [测试示例](#测试示例)

## 🧩 组件开发示例

### 1. 基础 UI 组件示例

#### 1.1 创建一个 Select 组件

```typescript
// src/components/ui/Select.tsx
/**
 * @file 选择器组件
 * @description 可复用的下拉选择组件，支持搜索和多选
 * @status 开发中
 */

import React, { forwardRef, useState, useRef, useEffect } from 'react'
import { cn } from '@/utils/common'

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

export interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {
  options: SelectOption[]
  placeholder?: string
  searchable?: boolean
  multiple?: boolean
  value?: string | string[]
  onChange?: (value: string | string[]) => void
  error?: string
  loading?: boolean
}

const Select = forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      className,
      options,
      placeholder = '请选择',
      searchable = false,
      multiple = false,
      value,
      onChange,
      error,
      loading = false,
      disabled,
      ...props
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false)
    const [searchTerm, setSearchTerm] = useState('')
    const containerRef = useRef<HTMLDivElement>(null)

    // 过滤选项
    const filteredOptions = options.filter(option =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase())
    )

    // 处理选择
    const handleSelect = (optionValue: string) => {
      if (multiple) {
        const currentValues = Array.isArray(value) ? value : []
        const newValues = currentValues.includes(optionValue)
          ? currentValues.filter(v => v !== optionValue)
          : [...currentValues, optionValue]
        onChange?.(newValues)
      } else {
        onChange?.(optionValue)
        setIsOpen(false)
      }
    }

    // 点击外部关闭
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
          setIsOpen(false)
        }
      }

      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }, [])

    // 键盘导航
    const handleKeyDown = (e: React.KeyboardEvent) => {
      switch (e.key) {
        case 'Enter':
        case ' ':
          e.preventDefault()
          setIsOpen(!isOpen)
          break
        case 'Escape':
          setIsOpen(false)
          break
      }
    }

    const selectedOptions = multiple && Array.isArray(value) 
      ? options.filter(opt => value.includes(opt.value))
      : options.find(opt => opt.value === value)

    return (
      <div ref={containerRef} className="relative">
        {/* 隐藏的原生 select 用于表单提交 */}
        <select
          ref={ref}
          value={value}
          multiple={multiple}
          className="sr-only"
          {...props}
        >
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        {/* 自定义选择器 */}
        <button
          type="button"
          className={cn(
            'relative w-full cursor-default rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm',
            'focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500',
            'disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500',
            error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            className
          )}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          onKeyDown={handleKeyDown}
          disabled={disabled || loading}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
        >
          <span className="block truncate">
            {loading ? (
              '加载中...'
            ) : multiple && Array.isArray(selectedOptions) ? (
              selectedOptions.length > 0 
                ? `已选择 ${selectedOptions.length} 项`
                : placeholder
            ) : selectedOptions ? (
              (selectedOptions as SelectOption).label
            ) : (
              placeholder
            )}
          </span>
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
            <svg
              className={cn(
                'h-5 w-5 text-gray-400 transition-transform',
                isOpen && 'rotate-180'
              )}
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </span>
        </button>

        {/* 错误信息 */}
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}

        {/* 下拉选项 */}
        {isOpen && (
          <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            {searchable && (
              <div className="sticky top-0 bg-white p-2">
                <input
                  type="text"
                  className="w-full rounded border border-gray-300 px-3 py-1 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                  placeholder="搜索选项..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            )}
            
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500">
                {searchTerm ? '未找到匹配选项' : '暂无选项'}
              </div>
            ) : (
              filteredOptions.map((option) => {
                const isSelected = multiple && Array.isArray(value)
                  ? value.includes(option.value)
                  : value === option.value

                return (
                  <button
                    key={option.value}
                    type="button"
                    className={cn(
                      'relative w-full cursor-default select-none py-2 pl-3 pr-9 text-left',
                      'hover:bg-primary-50 focus:bg-primary-50 focus:outline-none',
                      isSelected && 'bg-primary-100 text-primary-900',
                      option.disabled && 'cursor-not-allowed opacity-50'
                    )}
                    onClick={() => !option.disabled && handleSelect(option.value)}
                    disabled={option.disabled}
                  >
                    <span className="block truncate">{option.label}</span>
                    {isSelected && (
                      <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-primary-600">
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </span>
                    )}
                  </button>
                )
              })
            )}
          </div>
        )}
      </div>
    )
  }
)

Select.displayName = 'Select'

export { Select }
```

#### 1.2 Select 组件的 Storybook 故事

```typescript
// src/components/ui/Select.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import { Select } from './Select'

const meta: Meta<typeof Select> = {
  title: 'UI/Select',
  component: Select,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onChange: { action: 'changed' },
  },
}

export default meta
type Story = StoryObj<typeof meta>

const options = [
  { value: 'apple', label: '苹果' },
  { value: 'banana', label: '香蕉' },
  { value: 'orange', label: '橙子' },
  { value: 'grape', label: '葡萄', disabled: true },
]

export const Default: Story = {
  args: {
    options,
    placeholder: '请选择水果',
  },
}

export const WithSearch: Story = {
  args: {
    options,
    searchable: true,
    placeholder: '搜索并选择水果',
  },
}

export const Multiple: Story = {
  args: {
    options,
    multiple: true,
    placeholder: '选择多个水果',
  },
}

export const WithError: Story = {
  args: {
    options,
    error: '请选择一个选项',
    placeholder: '请选择水果',
  },
}

export const Loading: Story = {
  args: {
    options: [],
    loading: true,
  },
}
```

#### 1.3 Select 组件的单元测试

```typescript
// src/components/ui/__tests__/Select.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Select } from '../Select'

const mockOptions = [
  { value: 'apple', label: '苹果' },
  { value: 'banana', label: '香蕉' },
  { value: 'orange', label: '橙子' },
]

describe('Select', () => {
  it('renders correctly', () => {
    render(<Select options={mockOptions} placeholder="请选择" />)
    expect(screen.getByText('请选择')).toBeInTheDocument()
  })

  it('opens dropdown when clicked', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} />)
    
    const button = screen.getByRole('button')
    await user.click(button)
    
    expect(screen.getByText('苹果')).toBeInTheDocument()
    expect(screen.getByText('香蕉')).toBeInTheDocument()
  })

  it('selects option when clicked', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Select options={mockOptions} onChange={handleChange} />)
    
    const button = screen.getByRole('button')
    await user.click(button)
    
    const option = screen.getByText('苹果')
    await user.click(option)
    
    expect(handleChange).toHaveBeenCalledWith('apple')
  })

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} />)
    
    const button = screen.getByRole('button')
    button.focus()
    
    await user.keyboard('{Enter}')
    expect(screen.getByText('苹果')).toBeInTheDocument()
    
    await user.keyboard('{Escape}')
    await waitFor(() => {
      expect(screen.queryByText('苹果')).not.toBeInTheDocument()
    })
  })

  it('filters options when searchable', async () => {
    const user = userEvent.setup()
    render(<Select options={mockOptions} searchable />)
    
    const button = screen.getByRole('button')
    await user.click(button)
    
    const searchInput = screen.getByPlaceholderText('搜索选项...')
    await user.type(searchInput, '苹')
    
    expect(screen.getByText('苹果')).toBeInTheDocument()
    expect(screen.queryByText('香蕉')).not.toBeInTheDocument()
  })

  it('handles multiple selection', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Select options={mockOptions} multiple onChange={handleChange} />)
    
    const button = screen.getByRole('button')
    await user.click(button)
    
    await user.click(screen.getByText('苹果'))
    expect(handleChange).toHaveBeenCalledWith(['apple'])
    
    await user.click(screen.getByText('香蕉'))
    expect(handleChange).toHaveBeenCalledWith(['apple', 'banana'])
  })

  it('shows error state', () => {
    render(<Select options={mockOptions} error="请选择一个选项" />)
    expect(screen.getByText('请选择一个选项')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    render(<Select options={[]} loading />)
    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })
})
```

### 2. 业务组件示例

#### 2.1 用户选择器组件

```typescript
// src/components/common/UserSelector.tsx
/**
 * @file 用户选择器组件
 * @description 基于 Select 组件的用户选择器，支持搜索和异步加载
 * @status 开发中
 */

import React, { useState, useEffect, useMemo } from 'react'
import { Select, SelectProps } from '@/components/ui/Select'
import { useApi } from '@/hooks/useApi'
import { User } from '@/api/types'

interface UserSelectorProps extends Omit<SelectProps, 'options' | 'loading'> {
  roleFilter?: string[]
  departmentFilter?: string[]
  excludeUsers?: string[]
}

export const UserSelector: React.FC<UserSelectorProps> = ({
  roleFilter,
  departmentFilter,
  excludeUsers = [],
  ...selectProps
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const { data: users, loading, error } = useApi<User[]>('/api/users', {
    params: {
      search: searchTerm,
      roles: roleFilter?.join(','),
      departments: departmentFilter?.join(','),
    },
  })

  // 转换用户数据为选项格式
  const options = useMemo(() => {
    if (!users) return []
    
    return users
      .filter(user => !excludeUsers.includes(user.id))
      .map(user => ({
        value: user.id,
        label: `${user.name} (${user.email})`,
        disabled: !user.active,
      }))
  }, [users, excludeUsers])

  if (error) {
    return (
      <div className="text-red-600 text-sm">
        加载用户列表失败: {error.message}
      </div>
    )
  }

  return (
    <Select
      {...selectProps}
      options={options}
      loading={loading}
      searchable
      placeholder="搜索并选择用户..."
    />
  )
}
```

## 🔧 模块开发示例

### 1. 用户管理模块

```typescript
// src/modules/user-management/index.tsx
/**
 * @file 用户管理模块
 * @description 用户管理功能模块，包含用户列表、创建、编辑等功能
 * @status 开发中
 */

import React, { lazy } from 'react'
import { RouteObject } from 'react-router-dom'
import { Module, ModuleConfig } from '@/utils/ModuleLoader'

// 懒加载页面组件
const UserListPage = lazy(() => import('./pages/UserListPage'))
const UserDetailPage = lazy(() => import('./pages/UserDetailPage'))
const UserCreatePage = lazy(() => import('./pages/UserCreatePage'))

// 模块配置
const config: ModuleConfig = {
  id: 'user-management',
  name: '用户管理',
  version: '1.0.0',
  description: '用户账户管理和权限控制',
  enabled: true,
  lazy: true,
  dependencies: [],
  permissions: ['user:read', 'user:write'],
}

// 路由配置
const routes: RouteObject[] = [
  {
    path: '/users',
    element: <UserListPage />,
  },
  {
    path: '/users/create',
    element: <UserCreatePage />,
  },
  {
    path: '/users/:id',
    element: <UserDetailPage />,
  },
]

// 模块初始化
const initialize = async () => {
  console.log('User management module initialized')
  
  // 预加载关键数据
  try {
    // 可以在这里预加载用户角色、部门等数据
  } catch (error) {
    console.error('Failed to preload user management data:', error)
  }
}

// 模块销毁
const destroy = async () => {
  console.log('User management module destroyed')
  // 清理资源
}

// 模块定义
export const userManagementModule: Module = {
  config,
  routes,
  initialize,
  destroy,
}
```

### 2. 用户列表页面

```typescript
// src/modules/user-management/pages/UserListPage.tsx
import React, { useState, useMemo } from 'react'
import { Link } from 'react-router-dom'
import { Button, Card, CardHeader, CardTitle, CardContent } from '@/components/ui'
import { UserSelector } from '@/components/common/UserSelector'
import { useApi } from '@/hooks/useApi'
import { User } from '@/api/types'

const UserListPage: React.FC = () => {
  const [filters, setFilters] = useState({
    role: '',
    department: '',
    status: 'all',
  })
  const [searchTerm, setSearchTerm] = useState('')

  const { data: users, loading, error, refetch } = useApi<User[]>('/api/users', {
    params: {
      ...filters,
      search: searchTerm,
    },
  })

  // 过滤和排序用户
  const filteredUsers = useMemo(() => {
    if (!users) return []
    
    return users.filter(user => {
      if (filters.status !== 'all' && user.status !== filters.status) {
        return false
      }
      return true
    })
  }, [users, filters])

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('确定要删除这个用户吗？')) return
    
    try {
      await fetch(`/api/users/${userId}`, { method: 'DELETE' })
      refetch()
    } catch (error) {
      console.error('Delete user failed:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">加载用户列表失败</p>
        <Button onClick={() => refetch()}>重试</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
          <p className="text-gray-600">管理系统用户账户和权限</p>
        </div>
        <Link to="/users/create">
          <Button>创建用户</Button>
        </Link>
      </div>

      {/* 筛选器 */}
      <Card>
        <CardHeader>
          <CardTitle>筛选条件</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                搜索
              </label>
              <input
                type="text"
                className="w-full rounded-md border border-gray-300 px-3 py-2"
                placeholder="搜索用户名或邮箱..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                角色
              </label>
              <select
                className="w-full rounded-md border border-gray-300 px-3 py-2"
                value={filters.role}
                onChange={(e) => setFilters(prev => ({ ...prev, role: e.target.value }))}
              >
                <option value="">全部角色</option>
                <option value="admin">管理员</option>
                <option value="user">普通用户</option>
                <option value="guest">访客</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                状态
              </label>
              <select
                className="w-full rounded-md border border-gray-300 px-3 py-2"
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              >
                <option value="all">全部状态</option>
                <option value="active">活跃</option>
                <option value="inactive">非活跃</option>
                <option value="suspended">已暂停</option>
              </select>
            </div>
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setFilters({ role: '', department: '', status: 'all' })
                  setSearchTerm('')
                }}
              >
                重置筛选
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 用户列表 */}
      <Card>
        <CardHeader>
          <CardTitle>用户列表 ({filteredUsers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredUsers.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              暂无用户数据
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用户
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      角色
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      创建时间
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <img
                              className="h-10 w-10 rounded-full"
                              src={user.avatar || '/default-avatar.png'}
                              alt={user.name}
                            />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {user.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {user.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          {user.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            user.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : user.status === 'inactive'
                              ? 'bg-gray-100 text-gray-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {user.status === 'active' ? '活跃' : user.status === 'inactive' ? '非活跃' : '已暂停'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(user.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                        <Link
                          to={`/users/${user.id}`}
                          className="text-primary-600 hover:text-primary-900"
                        >
                          查看
                        </Link>
                        <button
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default UserListPage
```

---

这个开发示例文档展示了：

1. **完整的 UI 组件开发流程** - 从 TypeScript 类型定义到实现、测试和文档
2. **业务组件的封装模式** - 如何基于基础组件构建业务特定的组件
3. **模块化架构的实践** - 如何创建可插拔的功能模块
4. **页面组件的最佳实践** - 包含状态管理、错误处理、加载状态等

这些示例遵循了项目的架构规范，可以作为开发者的参考模板。
