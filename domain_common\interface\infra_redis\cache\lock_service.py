"""
RedisLockService - Redis分布式锁服务封装

提供基于Redis的分布式锁功能，包括可重入锁、读写锁、信号量等
"""

import asyncio
import uuid
import time
from typing import Optional, Dict, Any, AsyncContextManager
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

from commonlib.storages.persistence.redis.repository import RedisRepository
from .key_builder import RedisKeyBuilder, KeyType


class LockType(Enum):
    """锁类型"""
    EXCLUSIVE = "exclusive"     # 排他锁
    REENTRANT = "reentrant"     # 可重入锁
    READ_WRITE = "read_write"   # 读写锁
    SEMAPHORE = "semaphore"     # 信号量


@dataclass
class LockInfo:
    """锁信息"""
    lock_id: str
    owner_id: str
    acquired_at: datetime
    expires_at: datetime
    lock_type: LockType
    reentrant_count: int = 0
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LockConfig:
    """锁配置"""
    default_timeout: int = 30           # 默认锁超时时间（秒）
    max_wait_time: int = 60            # 最大等待时间（秒）
    retry_interval: float = 0.1        # 重试间隔（秒）
    auto_renewal: bool = True          # 是否自动续期
    renewal_interval: int = 10         # 续期间隔（秒）


class RedisLockService:
    """Redis分布式锁服务"""
    
    def __init__(
        self,
        redis_repo: RedisRepository,
        key_builder: RedisKeyBuilder,
        config: Optional[LockConfig] = None
    ):
        """
        初始化锁服务
        
        Args:
            redis_repo: Redis仓库实例
            key_builder: 键构建器实例
            config: 锁配置
        """
        self.redis_repo = redis_repo
        self.key_builder = key_builder
        self.config = config or LockConfig()
        self._renewal_tasks: Dict[str, asyncio.Task] = {}
    
    # ==================== 基础锁操作 ====================
    
    async def acquire_lock(
        self,
        resource: str,
        owner_id: Optional[str] = None,
        timeout: Optional[int] = None,
        wait_timeout: Optional[int] = None,
        lock_type: LockType = LockType.EXCLUSIVE,
        tenant_id: Optional[str] = None
    ) -> Optional[str]:
        """
        获取锁
        
        Args:
            resource: 资源标识
            owner_id: 锁拥有者ID
            timeout: 锁超时时间（秒）
            wait_timeout: 等待超时时间（秒）
            lock_type: 锁类型
            tenant_id: 租户ID
            
        Returns:
            锁ID，获取失败返回None
        """
        try:
            # 生成锁ID和拥有者ID
            lock_id = str(uuid.uuid4())
            owner_id = owner_id or str(uuid.uuid4())
            timeout = timeout or self.config.default_timeout
            wait_timeout = wait_timeout or self.config.max_wait_time
            
            # 构建锁键
            lock_key = self._build_lock_key(resource, lock_type, tenant_id)
            
            # 尝试获取锁
            start_time = time.time()
            while time.time() - start_time < wait_timeout:
                if await self._try_acquire_lock(lock_key, lock_id, owner_id, timeout, lock_type):
                    # 启动自动续期任务
                    if self.config.auto_renewal:
                        await self._start_renewal_task(lock_key, lock_id, owner_id, timeout)
                    
                    return lock_id
                
                # 等待重试
                await asyncio.sleep(self.config.retry_interval)
            
            return None
            
        except Exception as e:
            print(f"Lock acquire error for resource {resource}: {e}")
            return None
    
    async def release_lock(
        self,
        resource: str,
        lock_id: str,
        owner_id: str,
        lock_type: LockType = LockType.EXCLUSIVE,
        tenant_id: Optional[str] = None
    ) -> bool:
        """
        释放锁
        
        Args:
            resource: 资源标识
            lock_id: 锁ID
            owner_id: 锁拥有者ID
            lock_type: 锁类型
            tenant_id: 租户ID
            
        Returns:
            是否释放成功
        """
        try:
            lock_key = self._build_lock_key(resource, lock_type, tenant_id)
            
            # 停止续期任务
            await self._stop_renewal_task(lock_key)
            
            # 验证锁拥有者并释放
            return await self._try_release_lock(lock_key, lock_id, owner_id, lock_type)
            
        except Exception as e:
            print(f"Lock release error for resource {resource}: {e}")
            return False
    
    async def extend_lock(
        self,
        resource: str,
        lock_id: str,
        owner_id: str,
        extend_time: int,
        lock_type: LockType = LockType.EXCLUSIVE,
        tenant_id: Optional[str] = None
    ) -> bool:
        """
        延长锁时间
        
        Args:
            resource: 资源标识
            lock_id: 锁ID
            owner_id: 锁拥有者ID
            extend_time: 延长时间（秒）
            lock_type: 锁类型
            tenant_id: 租户ID
            
        Returns:
            是否延长成功
        """
        try:
            lock_key = self._build_lock_key(resource, lock_type, tenant_id)
            
            # 获取当前锁信息
            lock_info = await self._get_lock_info(lock_key)
            if not lock_info or lock_info.lock_id != lock_id or lock_info.owner_id != owner_id:
                return False
            
            # 更新过期时间
            new_expires_at = datetime.now() + timedelta(seconds=extend_time)
            lock_info.expires_at = new_expires_at
            
            # 保存更新后的锁信息
            serialized_info = json.dumps(self._serialize_lock_info(lock_info))
            await self.redis_repo.set(lock_key, serialized_info, ttl=extend_time)
            
            return True
            
        except Exception as e:
            print(f"Lock extend error for resource {resource}: {e}")
            return False
    
    async def is_locked(
        self,
        resource: str,
        lock_type: LockType = LockType.EXCLUSIVE,
        tenant_id: Optional[str] = None
    ) -> bool:
        """
        检查资源是否被锁定
        
        Args:
            resource: 资源标识
            lock_type: 锁类型
            tenant_id: 租户ID
            
        Returns:
            是否被锁定
        """
        try:
            lock_key = self._build_lock_key(resource, lock_type, tenant_id)
            return await self.redis_repo.exists(lock_key)
        except Exception as e:
            print(f"Lock check error for resource {resource}: {e}")
            return False
    
    async def get_lock_info(
        self,
        resource: str,
        lock_type: LockType = LockType.EXCLUSIVE,
        tenant_id: Optional[str] = None
    ) -> Optional[LockInfo]:
        """
        获取锁信息
        
        Args:
            resource: 资源标识
            lock_type: 锁类型
            tenant_id: 租户ID
            
        Returns:
            锁信息或None
        """
        try:
            lock_key = self._build_lock_key(resource, lock_type, tenant_id)
            return await self._get_lock_info(lock_key)
        except Exception as e:
            print(f"Get lock info error for resource {resource}: {e}")
            return None
    
    # ==================== 上下文管理器 ====================
    
    def lock(
        self,
        resource: str,
        owner_id: Optional[str] = None,
        timeout: Optional[int] = None,
        wait_timeout: Optional[int] = None,
        lock_type: LockType = LockType.EXCLUSIVE,
        tenant_id: Optional[str] = None
    ) -> AsyncContextManager:
        """
        锁上下文管理器
        
        Args:
            resource: 资源标识
            owner_id: 锁拥有者ID
            timeout: 锁超时时间
            wait_timeout: 等待超时时间
            lock_type: 锁类型
            tenant_id: 租户ID
            
        Returns:
            异步上下文管理器
        """
        return LockContextManager(
            self, resource, owner_id, timeout, wait_timeout, lock_type, tenant_id
        )
    
    # ==================== 特殊锁类型 ====================
    
    async def acquire_semaphore(
        self,
        resource: str,
        max_permits: int,
        owner_id: Optional[str] = None,
        timeout: Optional[int] = None,
        wait_timeout: Optional[int] = None,
        tenant_id: Optional[str] = None
    ) -> Optional[str]:
        """
        获取信号量
        
        Args:
            resource: 资源标识
            max_permits: 最大许可数
            owner_id: 拥有者ID
            timeout: 超时时间
            wait_timeout: 等待超时时间
            tenant_id: 租户ID
            
        Returns:
            许可ID或None
        """
        try:
            permit_id = str(uuid.uuid4())
            owner_id = owner_id or str(uuid.uuid4())
            timeout = timeout or self.config.default_timeout
            wait_timeout = wait_timeout or self.config.max_wait_time
            
            semaphore_key = self._build_lock_key(resource, LockType.SEMAPHORE, tenant_id)
            
            start_time = time.time()
            while time.time() - start_time < wait_timeout:
                # 尝试获取信号量
                current_count = await self.redis_repo.scard(semaphore_key)
                if current_count < max_permits:
                    # 添加许可
                    permit_info = {
                        "permit_id": permit_id,
                        "owner_id": owner_id,
                        "acquired_at": datetime.now().isoformat(),
                        "expires_at": (datetime.now() + timedelta(seconds=timeout)).isoformat()
                    }
                    
                    success = await self.redis_repo.sadd(
                        semaphore_key, 
                        json.dumps(permit_info)
                    )
                    
                    if success:
                        # 设置过期时间
                        await self.redis_repo.expire(semaphore_key, timeout)
                        return permit_id
                
                await asyncio.sleep(self.config.retry_interval)
            
            return None
            
        except Exception as e:
            print(f"Semaphore acquire error for resource {resource}: {e}")
            return None
    
    async def release_semaphore(
        self,
        resource: str,
        permit_id: str,
        owner_id: str,
        tenant_id: Optional[str] = None
    ) -> bool:
        """
        释放信号量
        
        Args:
            resource: 资源标识
            permit_id: 许可ID
            owner_id: 拥有者ID
            tenant_id: 租户ID
            
        Returns:
            是否释放成功
        """
        try:
            semaphore_key = self._build_lock_key(resource, LockType.SEMAPHORE, tenant_id)
            
            # 获取所有许可
            permits = await self.redis_repo.smembers(semaphore_key)
            
            # 查找并移除匹配的许可
            for permit_data in permits:
                try:
                    permit_info = json.loads(permit_data)
                    if (permit_info.get("permit_id") == permit_id and 
                        permit_info.get("owner_id") == owner_id):
                        await self.redis_repo.srem(semaphore_key, permit_data)
                        return True
                except json.JSONDecodeError:
                    continue
            
            return False
            
        except Exception as e:
            print(f"Semaphore release error for resource {resource}: {e}")
            return False
    
    # ==================== 内部方法 ====================
    
    def _build_lock_key(
        self,
        resource: str,
        lock_type: LockType,
        tenant_id: Optional[str] = None
    ) -> str:
        """构建锁键"""
        return self.key_builder.build_key(
            KeyType.LOCK,
            lock_type.value,
            resource,
            tenant_id=tenant_id
        )
    
    async def _try_acquire_lock(
        self,
        lock_key: str,
        lock_id: str,
        owner_id: str,
        timeout: int,
        lock_type: LockType
    ) -> bool:
        """尝试获取锁"""
        try:
            # 检查是否是可重入锁
            if lock_type == LockType.REENTRANT:
                existing_lock = await self._get_lock_info(lock_key)
                if existing_lock and existing_lock.owner_id == owner_id:
                    # 增加重入计数
                    existing_lock.reentrant_count += 1
                    existing_lock.expires_at = datetime.now() + timedelta(seconds=timeout)
                    
                    serialized_info = json.dumps(self._serialize_lock_info(existing_lock))
                    await self.redis_repo.set(lock_key, serialized_info, ttl=timeout)
                    return True
            
            # 创建新锁
            lock_info = LockInfo(
                lock_id=lock_id,
                owner_id=owner_id,
                acquired_at=datetime.now(),
                expires_at=datetime.now() + timedelta(seconds=timeout),
                lock_type=lock_type
            )
            
            serialized_info = json.dumps(self._serialize_lock_info(lock_info))
            
            # 使用NX选项确保原子性
            return await self.redis_repo.set(lock_key, serialized_info, ttl=timeout, nx=True)
            
        except Exception as e:
            print(f"Try acquire lock error: {e}")
            return False
    
    async def _try_release_lock(
        self,
        lock_key: str,
        lock_id: str,
        owner_id: str,
        lock_type: LockType
    ) -> bool:
        """尝试释放锁"""
        try:
            lock_info = await self._get_lock_info(lock_key)
            if not lock_info:
                return False
            
            # 验证锁拥有者
            if lock_info.lock_id != lock_id or lock_info.owner_id != owner_id:
                return False
            
            # 处理可重入锁
            if lock_type == LockType.REENTRANT and lock_info.reentrant_count > 0:
                lock_info.reentrant_count -= 1
                if lock_info.reentrant_count > 0:
                    # 更新锁信息而不删除
                    serialized_info = json.dumps(self._serialize_lock_info(lock_info))
                    ttl = int((lock_info.expires_at - datetime.now()).total_seconds())
                    await self.redis_repo.set(lock_key, serialized_info, ttl=max(ttl, 1))
                    return True
            
            # 删除锁
            return await self.redis_repo.delete(lock_key)
            
        except Exception as e:
            print(f"Try release lock error: {e}")
            return False
    
    async def _get_lock_info(self, lock_key: str) -> Optional[LockInfo]:
        """获取锁信息"""
        try:
            serialized_info = await self.redis_repo.get(lock_key)
            if not serialized_info:
                return None
            
            data = json.loads(serialized_info)
            return self._deserialize_lock_info(data)
            
        except Exception as e:
            print(f"Get lock info error: {e}")
            return None
    
    def _serialize_lock_info(self, lock_info: LockInfo) -> Dict[str, Any]:
        """序列化锁信息"""
        return {
            "lock_id": lock_info.lock_id,
            "owner_id": lock_info.owner_id,
            "acquired_at": lock_info.acquired_at.isoformat(),
            "expires_at": lock_info.expires_at.isoformat(),
            "lock_type": lock_info.lock_type.value,
            "reentrant_count": lock_info.reentrant_count,
            "metadata": lock_info.metadata
        }
    
    def _deserialize_lock_info(self, data: Dict[str, Any]) -> LockInfo:
        """反序列化锁信息"""
        return LockInfo(
            lock_id=data["lock_id"],
            owner_id=data["owner_id"],
            acquired_at=datetime.fromisoformat(data["acquired_at"]),
            expires_at=datetime.fromisoformat(data["expires_at"]),
            lock_type=LockType(data["lock_type"]),
            reentrant_count=data.get("reentrant_count", 0),
            metadata=data.get("metadata")
        )
    
    async def _start_renewal_task(
        self,
        lock_key: str,
        lock_id: str,
        owner_id: str,
        timeout: int
    ):
        """启动锁续期任务"""
        async def renewal_task():
            try:
                while True:
                    await asyncio.sleep(self.config.renewal_interval)
                    
                    # 检查锁是否仍然存在
                    lock_info = await self._get_lock_info(lock_key)
                    if not lock_info or lock_info.lock_id != lock_id:
                        break
                    
                    # 续期
                    new_expires_at = datetime.now() + timedelta(seconds=timeout)
                    lock_info.expires_at = new_expires_at
                    
                    serialized_info = json.dumps(self._serialize_lock_info(lock_info))
                    await self.redis_repo.set(lock_key, serialized_info, ttl=timeout)
                    
            except asyncio.CancelledError:
                pass
            except Exception as e:
                print(f"Lock renewal task error: {e}")
        
        task = asyncio.create_task(renewal_task())
        self._renewal_tasks[lock_key] = task
    
    async def _stop_renewal_task(self, lock_key: str):
        """停止锁续期任务"""
        task = self._renewal_tasks.pop(lock_key, None)
        if task and not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass


class LockContextManager:
    """锁上下文管理器"""
    
    def __init__(
        self,
        lock_service: RedisLockService,
        resource: str,
        owner_id: Optional[str],
        timeout: Optional[int],
        wait_timeout: Optional[int],
        lock_type: LockType,
        tenant_id: Optional[str]
    ):
        self.lock_service = lock_service
        self.resource = resource
        self.owner_id = owner_id or str(uuid.uuid4())
        self.timeout = timeout
        self.wait_timeout = wait_timeout
        self.lock_type = lock_type
        self.tenant_id = tenant_id
        self.lock_id: Optional[str] = None
    
    async def __aenter__(self):
        self.lock_id = await self.lock_service.acquire_lock(
            self.resource,
            self.owner_id,
            self.timeout,
            self.wait_timeout,
            self.lock_type,
            self.tenant_id
        )
        
        if self.lock_id is None:
            raise TimeoutError(f"Failed to acquire lock for resource: {self.resource}")
        
        return self.lock_id
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.lock_id:
            await self.lock_service.release_lock(
                self.resource,
                self.lock_id,
                self.owner_id,
                self.lock_type,
                self.tenant_id
            )
