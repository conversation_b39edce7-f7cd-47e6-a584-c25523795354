/**
 * @file 认证API Mock处理器
 * @description 模拟认证相关的API响应
 * @status 框架文件 - 完成
 */

import { http, HttpResponse } from 'msw'
import { mockUsers } from '../data/users'
import { ApiResponse, LoginRequest, LoginResponse, User } from '@/api/types'

export const authHandlers = [
  // 用户登录
  http.post('/api/auth/login', async ({ request }) => {
    const { username, password } = await request.json() as LoginRequest

    // 简单的用户验证
    const user = mockUsers.find(u => u.username === username)

    if (!user || password !== 'password') {
      return HttpResponse.json({
        success: false,
        message: '用户名或密码错误',
        code: 'INVALID_CREDENTIALS',
      }, { status: 401 })
    }

    const response: ApiResponse<LoginResponse> = {
      success: true,
      data: {
        user,
        accessToken: 'mock-access-token-' + user.id,
        refreshToken: 'mock-refresh-token-' + user.id,
        expiresIn: 3600,
      },
      message: '登录成功',
    }

    return HttpResponse.json(response)
  }),

  // 刷新token
  http.post('/api/auth/refresh', async ({ request }) => {
    const { refreshToken } = await request.json() as any

    if (!refreshToken || !refreshToken.startsWith('mock-refresh-token-')) {
      return HttpResponse.json({
        success: false,
        message: '无效的刷新令牌',
        code: 'INVALID_REFRESH_TOKEN',
      }, { status: 401 })
    }

    const userId = refreshToken.replace('mock-refresh-token-', '')
    const user = mockUsers.find(u => u.id === userId)

    if (!user) {
      return HttpResponse.json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND',
      }, { status: 401 })
    }

    const response: ApiResponse<{ accessToken: string }> = {
      success: true,
      data: {
        accessToken: 'mock-access-token-' + user.id + '-refreshed',
      },
      message: 'Token刷新成功',
    }

    return HttpResponse.json(response)
  }),

  // 获取当前用户信息
  http.get('/api/auth/me', async ({ request }) => {
    const authHeader = request.headers.get('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer mock-access-token-')) {
      return HttpResponse.json({
        success: false,
        message: '未授权访问',
        code: 'UNAUTHORIZED',
      }, { status: 401 })
    }

    // 从token中提取用户ID
    const token = authHeader.replace('Bearer ', '')
    const userId = token.split('-').pop()?.replace('-refreshed', '')
    const user = mockUsers.find(u => u.id === userId)

    if (!user) {
      return HttpResponse.json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND',
      }, { status: 401 })
    }

    const response: ApiResponse<User> = {
      success: true,
      data: user,
      message: '获取用户信息成功',
    }

    return HttpResponse.json(response)
  }),

  // 用户登出
  http.post('/api/auth/logout', async () => {
    const response: ApiResponse<null> = {
      success: true,
      data: null,
      message: '登出成功',
    }

    return HttpResponse.json(response)
  }),
]