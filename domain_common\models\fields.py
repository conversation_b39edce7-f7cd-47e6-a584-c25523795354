"""
数据库字段工厂模块。

此模块提供统一的字段创建工厂类，确保整个应用程序中字段定义的一致性。
使用Fields类作为唯一的字段创建入口，避免API混乱。
"""
from datetime import datetime, timezone
from typing import Any, Dict, Optional, List, Union

from sqlalchemy import JSON, Integer, String, DateTime, func, Text
from sqlalchemy.orm import Mapped, mapped_column


from .constants import CommonStatus, FieldLengths, JSONType, SecurityStatus


class Fields:
    """字段工厂类，提供统一的字段创建方法。
    
    此类是创建数据库字段的唯一入口，提供简洁一致的API。
    所有字段创建都应该通过此类的静态方法完成。
    """

    @staticmethod
    def created_at() -> Mapped[datetime]:
        return mapped_column(
            DateTime(timezone=True),
            default=lambda: datetime.now(timezone.utc),
            server_default=func.current_timestamp(),
            doc="记录创建时间",
        )

    @staticmethod
    def updated_at() -> Mapped[datetime]:
        return mapped_column(
            DateTime(timezone=True),
            default=lambda: datetime.now(timezone.utc),
            server_default=func.current_timestamp(),
            onupdate=func.current_timestamp(),
            doc="最后更新时间",
        )

    @staticmethod
    def deleted_at() -> Mapped[Optional[datetime]]:
        return mapped_column(
            DateTime(timezone=True),
            nullable=True,
            default=None,
            doc="软删除时间戳"
        )

    @staticmethod
    def tenant(required: bool = True) -> Mapped[str]:
        """创建租户字段。
        
        Args:
            required: 是否为必填字段

        Returns:
            配置好的租户ID字段
        """
        return mapped_column(
            String(FieldLengths.ID_LENGTH),
            nullable=not required,
            doc="租户ID，用于多租户数据隔离"
        )

    @staticmethod
    def user(required: bool = False) -> Mapped[Optional[str]]:
        """创建用户字段。
        
        Args:
            required: 是否为必填字段
            
        Returns:
            配置好的用户ID字段
        """
        return mapped_column(
            String(FieldLengths.ID_LENGTH),
            nullable=not required,
            doc="用户ID"
        )

    @staticmethod
    def status(default: str = CommonStatus.PENDING) -> Mapped[str]:
        """创建状态字段。
        
        Args:
            default: 默认状态值
            
        Returns:
            配置好的状态字段
        """
        return mapped_column(
            String(FieldLengths.STATUS_LENGTH),
            nullable=False,
            default=default,
            doc="状态"
        )

    @staticmethod
    def security_status(default: str = SecurityStatus.PENDING) -> Mapped[str]:
        """创建状态字段。

        Args:
            default: 默认状态值

        Returns:
            配置好的状态字段
        """
        return mapped_column(
            String(FieldLengths.STATUS_LENGTH),
            nullable=False,
            default=default,
            doc="状态"
        )
    @staticmethod
    def json_field(
            default: Union[Dict[str, Any], List[Any]] = None,
            doc: str = "JSON数据"
    ) -> Mapped[Optional[JSONType]]:
        """创建JSON字段。
        
        Args:
            default: 默认值
            doc: 字段文档
            
        Returns:
            配置好的JSON字段
        """
        return mapped_column(
            JSON,
            nullable=True,
            default=default or {},
            doc=doc
        )

    @staticmethod
    def created_by() -> Mapped[Optional[str]]:
        """创建创建者字段。

        Returns:
            创建者用户ID字段
        """
        return mapped_column(
            String(FieldLengths.ID_LENGTH),
            nullable=True,
            doc="创建者用户ID"
        )

    @staticmethod
    def updated_by() -> Mapped[Optional[str]]:
        """创建更新者字段。
        
        Returns:
            更新者用户ID字段
        """
        return mapped_column(
            String(FieldLengths.ID_LENGTH),
            nullable=True,
            doc="最后更新者用户ID"
        )

    @staticmethod
    def name(
            max_length: int = FieldLengths.NAME_LENGTH,
            required: bool = True,
            doc: str = "名称"
    ) -> Mapped[str]:
        """创建名称字段。
        
        Args:
            max_length: 最大长度
            required: 是否为必填字段
            doc: 字段文档
            
        Returns:
            配置好的名称字段
        """
        return mapped_column(
            String(max_length),
            nullable=not required,
            doc=doc
        )

    @staticmethod
    def code(
            max_length: int = FieldLengths.CODE_LENGTH,
            required: bool = True,
            unique: bool = False,
            doc: str = "编码"
    ) -> Mapped[str]:
        """创建编码字段。

        Args:
            max_length: 最大长度
            required: 是否为必填字段
            unique: 是否唯一
            doc: 字段文档

        Returns:
            配置好的编码字段
        """
        return mapped_column(
            String(max_length),
            nullable=not required,
            unique=unique,
            doc=doc
        )

    @staticmethod
    def bigint_id(doc: str = "主键ID") -> Mapped[int]:
        """创建大整数主键字段。

        Args:
            doc: 字段文档

        Returns:
            配置好的大整数主键字段
        """
        return mapped_column(
            Integer,
            primary_key=True,
            autoincrement=True,
            doc=doc
        )

    @staticmethod
    def email(required: bool = True, unique: bool = True) -> Mapped[Optional[str]]:
        """创建邮箱字段。

        Args:
            required: 是否为必填字段
            unique: 是否唯一

        Returns:
            配置好的邮箱字段
        """
        return mapped_column(
            String(FieldLengths.EMAIL_LENGTH),
            nullable=not required,
            unique=unique,
            doc="邮箱地址"
        )

    @staticmethod
    def phone(required: bool = False) -> Mapped[Optional[str]]:
        """创建电话字段。

        Args:
            required: 是否为必填字段

        Returns:
            配置好的电话字段
        """
        return mapped_column(
            String(FieldLengths.PHONE_LENGTH),
            nullable=not required,
            doc="电话号码"
        )

    @staticmethod
    def ip_address() -> Mapped[Optional[str]]:
        """创建IP地址字段（数据库兼容）。

        Returns:
            配置好的IP地址字段
        """
        # 使用VARCHAR而不是INET以兼容MySQL
        return mapped_column(
            String(45),  # IPv6最大长度
            nullable=True,
            doc="IP地址"
        )

    @staticmethod
    def long_text(doc: str = "长文本") -> Mapped[Optional[str]]:
        """创建长文本字段。

        Args:
            doc: 字段文档

        Returns:
            配置好的长文本字段
        """
        return mapped_column(
            Text,
            nullable=True,
            doc=doc
        )

    @staticmethod
    def uuid_primary_key(doc: str = "主键ID") -> Mapped[str]:
        """创建UUID主键字段（数据库兼容）。

        Args:
            doc: 字段文档

        Returns:
            配置好的UUID主键字段
        """
        return mapped_column(
            String(64),
            primary_key=True,
            doc=doc
        )

    @staticmethod
    def session_id(
            nullable: bool = True,
            doc: str = "会话ID"
    ) -> Mapped[Optional[str]]:
        """创建会话ID字段。

        Args:
            nullable: 是否可空
            doc: 字段文档

        Returns:
            配置好的会话ID字段
        """
        return mapped_column(
            String(FieldLengths.ID_LENGTH),
            nullable=nullable,
            doc=doc
        )
