/**
 * @file 租户相关类型定义
 * @description 租户管理的TypeScript类型定义
 */

// 租户状态枚举
export type TenantStatus = 'active' | 'inactive' | 'suspended' | 'pending'

// 租户类型枚举
export type TenantType = 'enterprise' | 'team' | 'personal' | 'trial'

// 租户计划枚举
export type TenantPlan = 'free' | 'basic' | 'pro' | 'enterprise'

// 租户配置接口
export interface TenantConfig {
  id: string
  name: string
  display_name: string
  type: TenantType
  plan: TenantPlan
  status: TenantStatus
  domain?: string
  subdomain?: string
  logo?: TenantLogo
  branding: TenantBranding
  settings: TenantSettings
  features: TenantFeatures
  limits: TenantLimits
  contact: TenantContact
  created_at: string
  updated_at: string
}

// 租户Logo接口
export interface TenantLogo {
  light: string
  dark: string
  favicon?: string
  width?: number
  height?: number
}

// 租户品牌接口
export interface TenantBranding {
  primary_color: string
  secondary_color: string
  accent_color?: string
  background_color?: string
  text_color?: string
  logo_position?: 'left' | 'center' | 'right'
  tagline?: string
  description?: string
  custom_css?: string
  fonts?: {
    primary: string
    secondary?: string
  }
}

// 租户设置接口
export interface TenantSettings {
  // 认证设置
  auth: {
    allow_registration: boolean
    require_email_verification: boolean
    allow_social_login: boolean
    enable_sso: boolean
    password_policy: PasswordPolicy
    session_timeout: number
    max_login_attempts: number
    lockout_duration: number
  }
  
  // 安全设置
  security: {
    enable_2fa: boolean
    require_2fa: boolean
    allowed_domains: string[]
    ip_whitelist: string[]
    enable_audit_log: boolean
  }
  
  // 通知设置
  notifications: {
    email_enabled: boolean
    sms_enabled: boolean
    webhook_enabled: boolean
    notification_preferences: NotificationPreferences
  }
  
  // 集成设置
  integrations: {
    enabled_providers: string[]
    api_settings: Record<string, any>
    webhook_urls: string[]
  }
}

// 密码策略接口
export interface PasswordPolicy {
  min_length: number
  max_length?: number
  require_uppercase: boolean
  require_lowercase: boolean
  require_numbers: boolean
  require_symbols: boolean
  forbidden_patterns: string[]
  max_age_days?: number
  history_count?: number
}

// 通知偏好接口
export interface NotificationPreferences {
  login_alerts: boolean
  security_alerts: boolean
  system_updates: boolean
  marketing_emails: boolean
  digest_frequency: 'daily' | 'weekly' | 'monthly' | 'never'
}

// 租户功能接口
export interface TenantFeatures {
  max_users: number
  max_roles: number
  max_permissions: number
  custom_branding: boolean
  api_access: boolean
  sso_enabled: boolean
  audit_logs: boolean
  advanced_security: boolean
  priority_support: boolean
  custom_integrations: boolean
}

// 租户限制接口
export interface TenantLimits {
  storage_gb: number
  api_calls_per_month: number
  concurrent_sessions: number
  data_retention_days: number
  backup_frequency: 'daily' | 'weekly' | 'monthly'
}

// 租户联系信息接口
export interface TenantContact {
  admin_email: string
  admin_name: string
  admin_phone?: string
  billing_email?: string
  support_email?: string
  address?: TenantAddress
}

// 租户地址接口
export interface TenantAddress {
  street: string
  city: string
  state: string
  country: string
  postal_code: string
}

// 租户统计接口
export interface TenantStats {
  total_users: number
  active_users: number
  total_sessions: number
  storage_used_gb: number
  api_calls_this_month: number
  last_activity: string
}

// 租户选择器选项接口
export interface TenantOption {
  id: string
  name: string
  display_name: string
  logo?: string
  domain?: string
  recent: boolean
  favorite: boolean
}

// 租户检测配置接口
export interface TenantDetectionConfig {
  enable_email_domain_detection: boolean
  enable_subdomain_detection: boolean
  enable_url_parameter_detection: boolean
  default_tenant_id?: string
  detection_priority: ('email' | 'subdomain' | 'url')[]
}

// 租户切换事件接口
export interface TenantSwitchEvent {
  from_tenant_id?: string
  to_tenant_id: string
  method: 'manual' | 'auto_detected' | 'url_parameter'
  timestamp: string
}

// 租户验证结果接口
export interface TenantValidationResult {
  valid: boolean
  tenant?: TenantConfig
  error?: string
  suggestions?: TenantOption[]
}

// 租户搜索结果接口
export interface TenantSearchResult {
  tenants: TenantOption[]
  total: number
  has_more: boolean
}

// 租户邀请接口
export interface TenantInvitation {
  id: string
  tenant_id: string
  email: string
  role: string
  invited_by: string
  expires_at: string
  status: 'pending' | 'accepted' | 'expired' | 'revoked'
  created_at: string
}

// 租户状态接口
export interface TenantState {
  current?: TenantConfig
  available: TenantOption[]
  recent: TenantOption[]
  favorites: TenantOption[]
  isLoading: boolean
  error?: string
  detection_config: TenantDetectionConfig
}

// 租户上下文接口
export interface TenantContextValue {
  tenant: TenantState
  switchTenant: (tenantId: string) => Promise<void>
  detectTenant: (email?: string) => Promise<TenantValidationResult>
  searchTenants: (query: string) => Promise<TenantSearchResult>
  addToFavorites: (tenantId: string) => void
  removeFromFavorites: (tenantId: string) => void
  clearRecentTenants: () => void
}
