"""
配置测试Fixtures

提供测试配置相关的fixtures
"""

import pytest
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

from commonlib.configs.base_setting import AppSettings
from commonlib.configs.config_loader import ConfigLoader


@pytest.fixture
def test_config_dict():
    """测试配置字典"""
    return {
        "app_name": "iam-service",
        "config_path": "./config/config_prd.json",
        "basic_log_dir": "../../../log",
        "debug": True,
        "application": {
            "debug": True,
            "title": "IAM Service API",
            "project_name": "IAM Service",
            "description": "用户与权限管理服务",
            "secret_key": "your-production-secret-key-here",
            "docs_url": "/docs",
            "openapi_url": "/openapi.json",
            "redoc_url": "/redoc"
        },
        "connection_priority": {
            "redis": {
                "enabled": True,
                "connect_priority": 1,
                "shutdown_priority": 5
            },
            "postgres": {
                "enabled": True,
                "connect_priority": 2,
                "shutdown_priority": 2
            },
            "mysql": {
                "enabled": False,
                "connect_priority": 3,
                "shutdown_priority": 3
            }
        },
        "persistence": {
            "mysql": {
                "MYSQL_HOST": "**************",
                "MYSQL_PORT": 3306,
                "MYSQL_USERNAME": "root",
                "MYSQL_PASSWORD": "thismore@123456",
                "MYSQL_DATABASE": "thingsmore",
                "MYSQL_SCHEME": "mysql+aiomysql",
                "MYSQL_POOL_SIZE": 30,
                "MYSQL_TIMEOUT": 10,
                "MYSQL_POOL_RECYCLE": 3600,
                "MYSQL_POOL_PRE_PING": True,
                "MYSQL_ECHO": False
            },
            "postgres": {
                "POSTGRES_HOST": "**************",
                "POSTGRES_PORT": 5432,
                "POSTGRES_USER": "postgres",
                "POSTGRES_PASSWORD": "thismore@123456",
                "POSTGRES_DATABASE": "thingsmore",
                "POSTGRES_SCHEMA": "public",
                "POSTGRES_POOL_SIZE": 30,
                "POSTGRES_POOL_TIMEOUT": 10,
                "POSTGRES_POOL_RECYCLE": 3600,
                "POSTGRES_POOL_PRE_PING": True,
                "POSTGRES_ECHO": False
            },
            "redis": {
                "REDIS_HOST": "**************",
                "REDIS_PORT": 6378,
                "REDIS_DB": 0,
                "REDIS_USERNAME": "",
                "REDIS_PASSWORD": "thismore@123456",
                "REDIS_SSL": False,
                "REDIS_POOL_SIZE": 10,
                "REDIS_MAX_CONNECTIONS": 10,
                "REDIS_POOL_TIMEOUT": 5,
                "REDIS_POOL_RECYCLE": 3600,
                "REDIS_RETRY_ON_TIMEOUT": True,
                "REDIS_POOL_PRE_PING": True,
                "REDIS_DECODE_RESPONSE": True
            },
            "mongodb": {
                "MONGODB_HOST": "**************",
                "MONGODB_PORT": 27017,
                "MONGODB_USERNAME": "root",
                "MONGODB_PASSWORD": "thismore@123456",
                "MONGODB_DATABASE": "thingsmore",
                "MONGODB_AUTH_SOURCE": "admin",
                "MONGODB_AUTH_MECHANISM": "SCRAM-SHA-256",
                "MONGODB_MIN_POOL_SIZE": 10,
                "MONGODB_MAX_POOL_SIZE": 50,
                "MONGODB_MAX_IDLE_TIME_MS": 10000,
                "MONGODB_CONNECT_TIMEOUT_MS": 20000
            }
        },
        "middleware": {
            "cors": {
                "AllowOrigins": [
                    "*"
                ],
                "AllowMethods": [
                    "*"
                ],
                "AllowHeaders": [
                    "*"
                ],
                "AllowCredentials": True,
                "ExposeHeaders": [],
                "MaxAge": 600
            },
            "security": {
                "SSLRedirect": False,
                "ForceSSL": False,
                "FrameDeny": True,
                "ContentTypeNosniff": True,
                "BrowserXSSFilter": True,
                "HSTS": {
                    "IncludeSubdomains": True,
                    "Preload": False,
                    "MaxAge": 31536000
                }
            },
            "compression": {
                "Enabled": True,
                "Level": 9,
                "MinimumSize": 500
            }
        },
        "iam_config": {
            "security": {
                "jwt_secret_key": "your-secret-key-here",
                "jwt_algorithm": "HS256",
                "access_token_expire_minutes": 120,
                "refresh_token_expire_days": 7,
                "password_hash_rounds": 12
            },
            "cache": {
                "permissions_ttl": 3600,
                "roles_ttl": 7200,
                "policies_ttl": 1800,
                "user_info_ttl": 1800
            },
            "rate_limit": {
                "login_per_minute": 5,
                "api_per_second": 100,
                "batch_per_hour": 10
            }
        }
    }





@pytest.fixture
def temp_config_file(test_config_dict):
    """临时配置文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_config_dict, f, indent=2)
        temp_path = f.name

    yield temp_path

    # 清理
    if os.path.exists(temp_path):
        os.unlink(temp_path)


@pytest.fixture
def test_app_settings():
    """测试应用设置"""
    return AppSettings(
        app_name="test_iam_service",
        debug=True,
    )


@pytest.fixture
def mock_config_loader():
    """Mock配置加载器"""
    loader = MagicMock(spec=ConfigLoader)

    # Mock配置对象
    mock_config = MagicMock()
    mock_config.application.project_name = "test_iam_service"
    mock_config.debug = True
    mock_config.log_dir = "/tmp/test_logs"

    # Mock persistence配置
    mock_config.persistence = MagicMock()
    mock_config.persistence.redis = MagicMock()
    mock_config.persistence.mysql = MagicMock()
    mock_config.persistence.postgres = MagicMock()

    # Mock connection_priority配置
    mock_config.connection_priority = MagicMock()

    loader.get_config.return_value = mock_config
    loader.load = MagicMock()

    return loader


@pytest.fixture
def config_environment_variables():
    """配置环境变量"""
    env_vars = {
        "CONFIG_FILE_PATH": "/tmp/test_config.json",
        "DEBUG": "True",
        "LOG_LEVEL": "DEBUG",
        "REDIS_HOST": "localhost",
        "REDIS_PORT": "6379",
        "REDIS_DB": "15",
        "POSTGRES_HOST": "localhost",
        "POSTGRES_PORT": "5432",
        "POSTGRES_DB": "test_iam"
    }

    # 设置环境变量
    original_env = {}
    for key, value in env_vars.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value

    yield env_vars

    # 恢复原始环境变量
    for key, original_value in original_env.items():
        if original_value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = original_value


@pytest.fixture
def config_search_paths(temp_config_file):
    """配置搜索路径"""
    temp_dir = Path(temp_config_file).parent
    return [
        temp_config_file,
        temp_dir / "config.json",
        temp_dir / "configs" / "config.json"
    ]


@pytest.fixture
def mock_dotenv_load():
    """Mock dotenv加载"""
    with patch('commonlib.core.containers.config_container.load_dotenv') as mock_load:
        yield mock_load


@pytest.fixture
def mock_logger_initialize():
    """Mock日志初始化"""
    with patch('commonlib.core.logging.tsif_logging.app_logger.initialize') as mock_init:
        yield mock_init
