"""
租户管理性能测试

测试租户管理功能的性能表现
"""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import AsyncMock, MagicMock

from services.tenant_service import TenantService
from domain_common.models.iam_models import Tenant, User, Role, Permission, UserRole, RolePermission, AuditLog
from commonlib.storages.persistence.redis.repository import RedisRepository


class TestTenantPerformance:
    """租户管理性能测试类"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        session = AsyncMock()
        return session

    @pytest.fixture
    def mock_redis_repo(self):
        """模拟Redis仓储"""
        redis_repo = AsyncMock(spec=RedisRepository)
        return redis_repo

    @pytest.fixture
    def tenant_service(self, mock_session, mock_redis_repo):
        """创建租户服务实例"""
        return TenantService(
            session=mock_session,
            redis_repo=mock_redis_repo,
            user_model=User,
            tenant_model=Tenant,
            role_model=Role,
            permission_model=Permission,
            user_role_model=UserRole,
            role_permission_model=RolePermission,
            audit_log_model=AuditLog
        )

    @pytest.mark.asyncio
    async def test_create_tenant_performance(self, tenant_service, mock_session, mock_redis_repo):
        """测试创建租户的性能"""
        # 模拟数据库操作
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        mock_session.commit = AsyncMock()
        mock_session.flush = AsyncMock()
        mock_session.add = MagicMock()

        # 测试单次创建性能
        start_time = time.time()
        
        result = await tenant_service.create_tenant(
            tenant_name="性能测试企业",
            tenant_code="PERF_TEST",
            description="性能测试用租户",
            max_users=1000
        )
        
        end_time = time.time()
        execution_time = end_time - start_time

        # 验证结果
        assert "tenant_id" in result
        assert execution_time < 1.0  # 单次创建应在1秒内完成

        print(f"创建租户耗时: {execution_time:.3f}秒")

    @pytest.mark.asyncio
    async def test_batch_create_tenants_performance(self, tenant_service, mock_session, mock_redis_repo):
        """测试批量创建租户的性能"""
        # 模拟数据库操作
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        mock_session.commit = AsyncMock()
        mock_session.flush = AsyncMock()
        mock_session.add = MagicMock()

        # 批量创建租户
        batch_size = 10
        start_time = time.time()

        tasks = []
        for i in range(batch_size):
            task = tenant_service.create_tenant(
                tenant_name=f"批量测试企业{i}",
                tenant_code=f"BATCH_TEST_{i}",
                description=f"批量测试用租户{i}",
                max_users=500
            )
            tasks.append(task)

        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        execution_time = end_time - start_time

        # 验证结果
        assert len(results) == batch_size
        assert execution_time < 5.0  # 批量创建应在5秒内完成

        print(f"批量创建{batch_size}个租户耗时: {execution_time:.3f}秒")
        print(f"平均每个租户耗时: {execution_time/batch_size:.3f}秒")

    @pytest.mark.asyncio
    async def test_list_tenants_performance(self, tenant_service, mock_session):
        """测试查询租户列表的性能"""
        # 模拟大量租户数据
        mock_tenants = []
        for i in range(100):
            mock_tenant = MagicMock()
            mock_tenant.tenant_id = f"tenant_{i}"
            mock_tenant.tenant_name = f"测试企业{i}"
            mock_tenant.tenant_code = f"TEST_{i}"
            mock_tenant.description = f"测试描述{i}"
            mock_tenant.status = "active"
            mock_tenant.max_users = 1000
            mock_tenant.settings = {}
            mock_tenant.created_at = time.time()
            mock_tenant.updated_at = time.time()
            mock_tenants.append(mock_tenant)

        # 模拟数据库查询
        mock_session.execute.return_value.scalars.return_value.all.return_value = mock_tenants[:20]  # 分页返回20条
        mock_session.execute.return_value.scalar.return_value = 5  # 用户数量统计

        # 测试查询性能
        start_time = time.time()
        
        result = await tenant_service.list_tenants(limit=20)
        
        end_time = time.time()
        execution_time = end_time - start_time

        # 验证结果
        assert "tenants" in result
        assert len(result["tenants"]) == 20
        assert execution_time < 0.5  # 查询应在0.5秒内完成

        print(f"查询租户列表耗时: {execution_time:.3f}秒")

    @pytest.mark.asyncio
    async def test_get_tenant_detail_cache_performance(self, tenant_service, mock_redis_repo):
        """测试租户详情缓存性能"""
        tenant_id = "tenant_123"
        
        # 模拟缓存命中
        cached_data = {
            "tenant_id": tenant_id,
            "tenant_name": "缓存测试企业",
            "tenant_code": "CACHE_TEST",
            "status": "active"
        }
        mock_redis_repo.get.return_value = cached_data

        # 测试缓存命中性能
        start_time = time.time()
        
        result = await tenant_service.get_tenant_detail(tenant_id)
        
        end_time = time.time()
        execution_time = end_time - start_time

        # 验证结果
        assert result == cached_data
        assert execution_time < 0.01  # 缓存命中应在0.01秒内完成

        print(f"缓存命中查询耗时: {execution_time:.6f}秒")

    @pytest.mark.asyncio
    async def test_concurrent_operations_performance(self, tenant_service, mock_session, mock_redis_repo):
        """测试并发操作性能"""
        # 模拟数据库和缓存操作
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        mock_session.commit = AsyncMock()
        mock_session.flush = AsyncMock()
        mock_session.add = MagicMock()
        
        mock_tenant = MagicMock()
        mock_tenant.tenant_id = "tenant_123"
        mock_tenant.tenant_name = "并发测试企业"
        mock_tenant.tenant_code = "CONCURRENT_TEST"
        mock_tenant.description = "并发测试"
        mock_tenant.status = "active"
        mock_tenant.max_users = 1000
        mock_tenant.settings = {}
        mock_tenant.created_at = time.time()
        mock_tenant.updated_at = time.time()

        # 模拟不同操作的数据库返回
        def mock_execute_side_effect(*args, **kwargs):
            mock_result = MagicMock()
            mock_result.scalar_one_or_none.return_value = mock_tenant
            mock_result.scalars.return_value.all.return_value = [mock_tenant]
            mock_result.scalar.return_value = 5
            return mock_result

        mock_session.execute.side_effect = mock_execute_side_effect

        # 并发执行不同操作
        concurrent_tasks = []
        
        # 创建操作
        for i in range(5):
            task = tenant_service.create_tenant(
                tenant_name=f"并发创建{i}",
                tenant_code=f"CONCURRENT_CREATE_{i}"
            )
            concurrent_tasks.append(task)
        
        # 查询操作
        for i in range(10):
            task = tenant_service.list_tenants(limit=10)
            concurrent_tasks.append(task)
        
        # 详情查询操作
        for i in range(15):
            task = tenant_service.get_tenant_detail(f"tenant_{i}")
            concurrent_tasks.append(task)

        # 执行并发操作
        start_time = time.time()
        
        results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        
        end_time = time.time()
        execution_time = end_time - start_time

        # 验证结果
        successful_operations = sum(1 for result in results if not isinstance(result, Exception))
        total_operations = len(concurrent_tasks)
        
        assert successful_operations > 0
        assert execution_time < 10.0  # 并发操作应在10秒内完成

        print(f"并发执行{total_operations}个操作耗时: {execution_time:.3f}秒")
        print(f"成功操作数: {successful_operations}/{total_operations}")
        print(f"平均每个操作耗时: {execution_time/total_operations:.3f}秒")

    @pytest.mark.asyncio
    async def test_memory_usage_during_operations(self, tenant_service, mock_session, mock_redis_repo):
        """测试操作过程中的内存使用情况"""
        import psutil
        import os

        # 获取当前进程
        process = psutil.Process(os.getpid())
        
        # 记录初始内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 模拟数据库操作
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        mock_session.commit = AsyncMock()
        mock_session.flush = AsyncMock()
        mock_session.add = MagicMock()

        # 执行大量操作
        for i in range(50):
            await tenant_service.create_tenant(
                tenant_name=f"内存测试企业{i}",
                tenant_code=f"MEMORY_TEST_{i}"
            )

        # 记录操作后内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        print(f"初始内存使用: {initial_memory:.2f}MB")
        print(f"操作后内存使用: {final_memory:.2f}MB")
        print(f"内存增长: {memory_increase:.2f}MB")

        # 验证内存增长在合理范围内
        assert memory_increase < 100  # 内存增长应小于100MB

    def test_database_connection_pool_performance(self):
        """测试数据库连接池性能"""
        # 这个测试需要真实的数据库连接池
        # 测试连接池的获取和释放性能
        # 在实际环境中实现
        pass

    def test_redis_connection_performance(self):
        """测试Redis连接性能"""
        # 这个测试需要真实的Redis连接
        # 测试Redis操作的响应时间
        # 在实际环境中实现
        pass
