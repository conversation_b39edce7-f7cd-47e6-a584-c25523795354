import os
import re
import shutil
from pathlib import Path


def create_new_service(template_dir: str, new_service_name: str, output_dir: str = "."):
    """
    从模板复制生成新的微服务项目
    :param template_dir: 模板目录路径（如当前微服务的目录）
    :param new_service_name: 新服务名称（英文，如 "order_service"）
    :param output_dir: 新项目的输出目录（默认为当前目录）
    """
    # 规范名称格式（确保是合法的Python模块名）
    new_service_name = new_service_name.lower().replace("-", "_")
    if not re.match(r"^[a-z_]+$", new_service_name):
        raise ValueError("服务名只能包含小写字母和下划线")

    # 定义目标路径
    target_dir = Path(output_dir) / new_service_name
    if target_dir.exists():
        raise FileExistsError(f"目录已存在: {target_dir}")

    # 复制模板目录
    print(f"正在生成服务: {new_service_name}...")
    shutil.copytree(template_dir, target_dir)

    # 重命名关键文件/文件夹（假设模板中有 'example_service' 目录）
    example_dirs = list(target_dir.glob("**/example_service"))
    for dir_path in example_dirs:
        new_dir = dir_path.parent / new_service_name
        dir_path.rename(new_dir)

    # 替换文件内容中的模板变量
    for file_path in target_dir.glob("**/*.py"):
        if file_path.is_file():
            with open(file_path, "r+", encoding="utf-8") as f:
                content = f.read()
                content = content.replace("example_service", new_service_name)
                content = content.replace(
                    "ExampleService", new_service_name.title().replace("_", "")
                )
                f.seek(0)
                f.write(content)
                f.truncate()

    print(f"服务生成成功！路径: {target_dir}")
    print(f"下一步: 进入目录并修改业务代码 -> cd {target_dir}")


if __name__ == "__main__":
    # 获取当前脚本所在目录（模板目录）
    template_dir = os.path.join(
        os.path.dirname(os.path.abspath(__file__)), "example_service"
    )

    # 用户输入新服务名
    new_service_name = input("请输入新服务名称（英文，如 order_service）: ").strip()

    # 生成服务
    create_new_service(template_dir, new_service_name)
