# Redis Decorators

This module provides a set of decorators for common Redis-based patterns such as caching, distributed locking, and rate limiting.

## Available Decorators

### 1. Cache Decorator

Caches function results in Redis with configurable TTL and other options.

#### Usage Options:

**Option 1: Using the InfraContainer helper (Recommended)**

```python
from commonlib.core.containers.infra_container import InfraContainer

@InfraContainer.with_cache(
    ttl_seconds=300,  # Cache for 5 minutes
    key_prefix="my_prefix",  # Optional prefix for cache keys
    ignore_errors=True,  # Whether to ignore Redis errors
    need_hash=True  # Whether to hash the cache key
)
async def my_function(param1, param2):
    # Function implementation
    return result
```

**Option 2: Using the decorator directly**

```python
from domain_common.interface.infra_redis.rd_decorator import redis_cache_decorator


@redis_cache_decorator(
    ttl=300,  # Cache for 5 minutes
    key_prefix="my_prefix",  # Optional prefix for cache keys
    ignore_errors=True,  # Whether to ignore Redis errors
    need_hash=True  # Whether to hash the cache key
)
async def my_function(param1, param2):
    # Function implementation
    return result
```

### 2. Distributed Lock Decorator

Ensures a function is only executed once across multiple instances of the application at the same time.

#### Usage Options:

**Option 1: Using the InfraContainer helper (Recommended)**

```python
from commonlib.core.containers.infra_container import InfraContainer

@InfraContainer.with_distributed_lock(
    name="my_task",  # Optional name for the lock
    lock_ttl_ms=5000,  # Lock TTL in milliseconds
    nx=True  # Whether to use NX mode
)
async def my_task():
    # Task implementation
    return result
```

**Option 2: Using the decorator directly**

```python
from domain_common.interface.infra_redis.rd_decorator import redis_distributed_task_preemption_decorator


@redis_distributed_task_preemption_decorator(
    name="my_task",  # Optional name for the lock
    px=5000,  # Lock TTL in milliseconds
    nx=True  # Whether to use NX mode
)
async def my_task():
    # Task implementation
    return result
```

### 3. Rate Limiting Decorator

Limits the rate at which a function can be called using a token bucket algorithm.

```python
from commonlib.core.containers.infra_container import InfraContainer

@InfraContainer.with_rate_limit(
    tokens_per_second=1.0,  # Rate at which tokens are added to the bucket
    burst=5,  # Maximum number of tokens that can be accumulated
    timeout=30,  # Time to wait for a token in seconds
    key_prefix="token_bucket"  # Prefix for the Redis key
)
async def rate_limited_function(param1, param2):
    # Function implementation
    return result
```

## Combining Decorators

These decorators can be combined to achieve more complex functionality. For example:

```python
@scheduler_tasks_mgm.add(trigger="interval", minutes=5)
@InfraContainer.with_distributed_lock(name="example_cached_task")
@InfraContainer.with_cache(ttl_seconds=300, key_prefix="example")
@inject
async def example_cached_task(param1: str = "default"):
    """Example task that uses both distributed lock and cache decorators."""
    # Task implementation
    return result
```

## Important Notes

1. The order of decorators matters. In general, apply them in this order:
   - Scheduler decorators (if applicable)
   - Distributed lock decorators
   - Cache decorators
   - Rate limiting decorators
   - Dependency injection decorators

2. All decorators require dependency injection to be set up correctly. Make sure your application is properly wired with the `InfraContainer`.

3. For more examples, see the `decorator_examples.py` file in the example service.