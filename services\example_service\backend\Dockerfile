# 第一阶段：构建依赖
FROM python:3.12-slim as builder

# 设置APT和PIP镜像源
RUN set -eux; \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main contrib non-free non-free-firmware" > /etc/apt/sources.list; \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list; \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 配置PIP源
RUN mkdir -p /root/.configs/pip && \
    echo "[global]\n\
index-url = https://pypi.tuna.tsinghua.edu.cn/simple\n\
trusted-host = pypi.tuna.tsinghua.edu.cn" > /root/.configs/pip/pip.conf

WORKDIR /app

# 复制依赖文件
COPY pyproject.toml setup.py README.md ./
COPY requirements/prod.txt .
COPY services/example_service/requirements/prod.txt ./requirements/
# 安装依赖
RUN pip install --upgrade pip && \
    pip install --user -r prod.txt && \
    ([ ! -f requirements/prod.txt ] || pip install --user -r requirements/prod.txt)

# 复制项目代码
COPY common_service/ ./common_service/
COPY commonlib/ ./commonlib/

# 第二阶段：运行镜像
FROM python:3.12-slim

# 基础配置
WORKDIR /app
RUN echo "PATH=/root/.local/bin:\$PATH\n\
PYTHONPATH=/app\n\
DEBUG=true\n\
PROJECT_ROOT=/app\n\
LOG_DIR=/app/logs\n\
CONFIG_FILE_PATH=/app/configs/config_prd.json" > /app/.env && \
    chmod 644 /app/.env

# 从构建阶段复制内容
COPY --from=builder /root/.local /root/.local
COPY --from=builder /backend/app /app
COPY configs/config.json ./configs/
COPY services/example_service ./example_service/

# 设置权限
RUN chmod +x /app/example_service/start-server.sh

EXPOSE 8082
CMD ["python", "-m", "example_service.app.main"]