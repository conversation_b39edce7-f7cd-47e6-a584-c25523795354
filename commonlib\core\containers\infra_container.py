from typing import TypeVar

from commonlib.storages.connect_manager import (ConnectionManager,
                                                managed_connector)
from commonlib.storages.persistence.mongodb.repository import MongoRepository
from commonlib.storages.persistence.mysql.client import provide_mysql_session
from commonlib.storages.persistence.postgres import provide_postgres_session
from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.storages.populate_connectors import populate_connectors
from dependency_injector import containers, providers
from domain_common.interface.infra_redis.decorators.cache import \
    RedisCacheDecorator
from domain_common.interface.infra_redis.decorators.token_bucket import \
    TokenBucketDecorator
from domain_common.interface.infra_redis.scripts.script import \
    RedisScriptManager

T = TypeVar("T")


class InfraContainer(containers.DeclarativeContainer):
    """Infrastructure container for managing database connections and related components.

    This container provides access to database clients, repositories, and other
    infrastructure components needed by the application.
    """

    config = providers.DependenciesContainer()

    # Connection management
    connector_registry = providers.Callable(
        populate_connectors, config=config.connection_priority_config
    )

    connection_manager = providers.Singleton(
        ConnectionManager,
        config=config.persistence_config,
        connector_registry=connector_registry,
    )
    _ = providers.Resource(managed_connector, manager=connection_manager)

    # Database clients
    # ----------------
    # Redis client
    redis_client = providers.Callable(
        lambda infra: infra.get_connector("redis"), infra=connection_manager
    )
    # MySQL client
    mysql_client = providers.Callable(
        lambda infra: infra.get_connector("mysql"), infra=connection_manager
    )
    # PgSQL client
    postgres_client = providers.Callable(
        lambda infra: infra.get_connector("postgres"), infra=connection_manager
    )
    # MongoDB client
    mongodb_client = providers.Callable(
        lambda infra: infra.get_connector("mongodb"), infra=connection_manager
    )
    # RabbitMQ client
    rabbitmq_client = providers.Callable(
        lambda infra: infra.get_connector("rabbitmq"), infra=connection_manager
    )

    # Aliases for backward compatibility
    redis_clint = redis_client
    mysql_clint = mysql_client
    mongodb_clint = mongodb_client
    rabbitmq_clint = rabbitmq_client

    # Database repositories and extensions
    # ---------------------------------

    # Redis repository and extensions
    decorator_redis_repo = providers.Factory(
        RedisRepository,
        key_prefix=config.app_name.provider,
        redis_connector=redis_client,
    )

    # MongoDB repository
    mongodb_repo_factory = providers.Factory(
        MongoRepository,
        client=mongodb_client,
    )

    # Redis script management
    redis_script_manager = providers.Factory(
        RedisScriptManager,
        redis_repo=decorator_redis_repo,
    )

    # Redis cache decorator
    redis_cache_decorator = providers.Factory(
        RedisCacheDecorator,
        script_manager=redis_script_manager,
    )

    # Redis token bucket for rate limiting
    redis_token_bucket_decorator = providers.Factory(
        TokenBucketDecorator,
        script_manager=redis_script_manager,
    )

    # MySQL session provider
    # 注意: lambda connector: (async with connector.session(): yield session),  # 方式不合法
    mysql_session_provider = providers.Callable(
        provide_mysql_session,
        connector=mysql_client,
    )

    # PgSQL session provider
    # 注意: lambda connector: (async with connector.session(): yield session),  # 方式不合法
    postgres_session_provider = providers.Callable(
        provide_postgres_session,
        connector=postgres_client,
    )