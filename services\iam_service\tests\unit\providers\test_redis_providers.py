"""
Redis Provider测试

测试Redis相关的providers和装饰器
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from dependency_injector import providers
from commonlib.core.containers.infra_container import InfraContainer
from domain_common.interface.infra_redis.decorators.cache import RedisCacheDecorator
from domain_common.interface.infra_redis.decorators.token_bucket import TokenBucketDecorator
from domain_common.interface.infra_redis.scripts.script import RedisScriptManager


class TestRedisProviders:
    """Redis Provider测试类"""

    @pytest.mark.unit
    def test_redis_script_manager_provider(self):
        """测试Redis脚本管理器provider"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.decorator_redis_repo.override(mock_redis_repo)
        
        # 测试Redis脚本管理器创建
        with patch.object(RedisScriptManager, '__init__', return_value=None) as mock_init:
            script_manager = container.redis_script_manager()
            
            # 验证RedisScriptManager被正确初始化
            mock_init.assert_called_once_with(redis_repo=mock_redis_repo)

    @pytest.mark.unit
    def test_redis_cache_decorator_provider(self):
        """测试Redis缓存装饰器provider"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis脚本管理器
        mock_script_manager = MagicMock()
        container.redis_script_manager.override(mock_script_manager)
        
        # 测试Redis缓存装饰器创建
        with patch.object(RedisCacheDecorator, '__init__', return_value=None) as mock_init:
            cache_decorator = container.redis_cache_decorator()
            
            # 验证RedisCacheDecorator被正确初始化
            mock_init.assert_called_once_with(script_manager=mock_script_manager)

    @pytest.mark.unit
    def test_redis_token_bucket_decorator_provider(self):
        """测试Redis令牌桶装饰器provider"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis脚本管理器
        mock_script_manager = MagicMock()
        container.redis_script_manager.override(mock_script_manager)
        
        # 测试Redis令牌桶装饰器创建
        with patch.object(TokenBucketDecorator, '__init__', return_value=None) as mock_init:
            token_bucket_decorator = container.redis_token_bucket_decorator()
            
            # 验证TokenBucketDecorator被正确初始化
            mock_init.assert_called_once_with(script_manager=mock_script_manager)

    @pytest.mark.unit
    def test_redis_provider_types(self):
        """测试Redis provider类型"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证Redis相关providers都是Factory类型
        assert isinstance(container.decorator_redis_repo, providers.Factory)
        assert isinstance(container.redis_script_manager, providers.Factory)
        assert isinstance(container.redis_cache_decorator, providers.Factory)
        assert isinstance(container.redis_token_bucket_decorator, providers.Factory)

    @pytest.mark.unit
    def test_redis_provider_dependency_chain(self):
        """测试Redis provider依赖链"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock整个依赖链
        mock_redis_client = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_script_manager = MagicMock()
        
        container.redis_client.override(mock_redis_client)
        container.decorator_redis_repo.override(mock_redis_repo)
        container.redis_script_manager.override(mock_script_manager)
        
        # 测试缓存装饰器依赖链
        with patch.object(RedisCacheDecorator, '__init__', return_value=None) as mock_cache_init:
            cache_decorator = container.redis_cache_decorator()
            mock_cache_init.assert_called_once_with(script_manager=mock_script_manager)
        
        # 测试令牌桶装饰器依赖链
        with patch.object(TokenBucketDecorator, '__init__', return_value=None) as mock_bucket_init:
            token_bucket_decorator = container.redis_token_bucket_decorator()
            mock_bucket_init.assert_called_once_with(script_manager=mock_script_manager)

    @pytest.mark.unit
    def test_redis_repository_configuration(self):
        """测试Redis仓库配置"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app_prefix"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis客户端
        mock_redis_client = AsyncMock()
        container.redis_client.override(mock_redis_client)
        
        # 测试Redis仓库配置
        with patch('commonlib.storages.persistence.redis.repository.RedisRepository') as mock_repo_class:
            mock_repo_instance = AsyncMock()
            mock_repo_class.return_value = mock_repo_instance
            
            redis_repo = container.decorator_redis_repo()
            
            # 验证Redis仓库使用正确的配置
            mock_repo_class.assert_called_once_with(
                key_prefix="test_app_prefix",
                redis_connector=mock_redis_client
            )

    @pytest.mark.unit
    def test_redis_script_manager_functionality(self):
        """测试Redis脚本管理器功能"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.decorator_redis_repo.override(mock_redis_repo)
        
        # 创建真实的脚本管理器实例进行功能测试
        with patch.object(RedisScriptManager, '__init__', return_value=None):
            script_manager = container.redis_script_manager()
            
            # 验证脚本管理器被正确创建
            assert script_manager is not None

    @pytest.mark.unit
    def test_redis_decorator_integration(self):
        """测试Redis装饰器集成"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock完整的Redis依赖链
        mock_redis_client = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_script_manager = MagicMock()
        
        container.redis_client.override(mock_redis_client)
        container.decorator_redis_repo.override(mock_redis_repo)
        container.redis_script_manager.override(mock_script_manager)
        
        # 测试缓存装饰器和令牌桶装饰器可以同时工作
        with patch.object(RedisCacheDecorator, '__init__', return_value=None) as mock_cache_init, \
             patch.object(TokenBucketDecorator, '__init__', return_value=None) as mock_bucket_init:
            
            cache_decorator = container.redis_cache_decorator()
            token_bucket_decorator = container.redis_token_bucket_decorator()
            
            # 验证两个装饰器都被正确初始化
            mock_cache_init.assert_called_once_with(script_manager=mock_script_manager)
            mock_bucket_init.assert_called_once_with(script_manager=mock_script_manager)

    @pytest.mark.unit
    def test_redis_provider_error_handling(self):
        """测试Redis provider错误处理"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis仓库抛出异常
        container.decorator_redis_repo.override(Exception("Redis connection failed"))
        
        # 测试脚本管理器在Redis仓库异常时的处理
        with pytest.raises(Exception):
            container.redis_script_manager()

    @pytest.mark.unit
    def test_redis_provider_with_none_dependencies(self):
        """测试Redis provider处理None依赖"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis仓库返回None
        container.decorator_redis_repo.override(None)
        
        # 测试脚本管理器处理None Redis仓库
        with patch.object(RedisScriptManager, '__init__', return_value=None) as mock_init:
            script_manager = container.redis_script_manager()
            
            # 验证脚本管理器仍然被初始化，但使用None仓库
            mock_init.assert_called_once_with(redis_repo=None)

    @pytest.mark.unit
    def test_redis_client_alias_compatibility(self):
        """测试Redis客户端别名兼容性"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证redis_clint别名指向redis_client
        assert container.redis_clint is container.redis_client
        
        # Mock连接管理器
        mock_connection_manager = MagicMock()
        mock_redis_connector = AsyncMock()
        mock_connection_manager.get_connector.return_value = mock_redis_connector
        
        container.connection_manager.override(mock_connection_manager)
        
        # 测试通过别名获取Redis客户端
        redis_client_via_alias = container.redis_clint()
        redis_client_direct = container.redis_client()
        
        # 验证两种方式获取的是同一个客户端
        assert redis_client_via_alias is redis_client_direct
        assert redis_client_via_alias is mock_redis_connector

    @pytest.mark.unit
    def test_redis_provider_singleton_behavior(self):
        """测试Redis provider单例行为"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.decorator_redis_repo.override(mock_redis_repo)
        
        # 测试脚本管理器是否表现为单例（Factory provider每次都创建新实例）
        with patch.object(RedisScriptManager, '__init__', return_value=None):
            script_manager1 = container.redis_script_manager()
            script_manager2 = container.redis_script_manager()
            
            # Factory provider应该创建不同的实例
            assert script_manager1 is not script_manager2
