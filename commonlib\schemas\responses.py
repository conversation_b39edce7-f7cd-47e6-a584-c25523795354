from datetime import datetime
from typing import Generic, List, Literal, Optional, TypeVar

from commonlib.core.context import recyclable_app_correlation_id
from pydantic import BaseModel, Field, conint

T = TypeVar("T")


class ErrorDetail(BaseModel):
    """
    详细错误信息结构体
    """

    type: str = Field(
        ...,
        description="错误类型，如 validation_error、database_error",
        examples=["validation_error"],
    )
    message: str = Field(..., description="具体错误信息")


class BaseResponse(BaseModel, Generic[T]):
    """
    通用响应基类，所有响应结构的基础
    """

    status: Literal["success", "fail", "error"] = Field(
        ..., description="响应状态：success-成功，fail-校验失败，error-系统异常"
    )
    code: conint(ge=100, le=599) = Field(200, description="HTTP 状态码")
    data: Optional[T] = Field(..., description="主要业务数据载体")
    message: str = Field(..., description="响应摘要信息")
    timestamp: str = Field(
        default_factory=lambda: datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
        description="响应时间戳，精确到毫秒",
    )
    request_id: Optional[str] = Field(..., description="请求唯一标识，用于链路追踪")


# ===== 成功响应 =====
class SuccessResponse(BaseResponse[T]):
    """
    成功业务响应（状态码2xx）
    """

    status: Literal["success"] = Field("success", description="响应状态：success")
    code: int = Field(200, description="HTTP 状态码，2xx 表示成功")
    message: str = Field("Request processed successfully", description="成功提示信息")

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "status": "success",
                    "code": 200,
                    "data": {},
                    "message": "Request processed successfully",
                    "timestamp": "2025-05-26T15:34:19.386550",
                    "request_id": None,
                    "errors": None,
                }
            ]
        }


# ===== 校验失败响应 =====
class FailResponse(BaseResponse[T]):
    """
    客户端错误响应（状态码4xx），如参数校验失败
    """

    status: Literal["fail"] = Field("fail", description="响应状态：fail")
    code: int = Field(
        400, ge=400, lt=500, description="HTTP 错误状态码，4xx 表示客户端错误"
    )
    message: str = Field("Request validation failed", description="错误摘要")
    errors: Optional[List[ErrorDetail]] = Field(
        None,
        description="调试信息（仅在开发环境返回）",
        examples=[{"type": "DATABASE_TIMEOUT", "message": "数据库连接超时"}],
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "description": "单字段校验失败",
                    "value": {
                        "status": "fail",
                        "code": 400,
                        "message": "Request validation failed",
                        "timestamp": "2025-05-25T15:32:00Z",
                        "errors": [
                            {
                                "type": "LENGTH_ERROR",
                                "message": "长度需在8-32字符之间",
                            }
                        ],
                    },
                }
            ]
        }


# ===== 系统错误响应 =====
class ErrorResponse(BaseResponse[T]):
    """
    服务端错误响应（状态码5xx），如系统异常、数据库错误等
    """

    status: Literal["error"] = Field("error", description="响应状态：error")
    code: int = Field(
        500, ge=500, lt=600, description="HTTP 错误状态码，5xx 表示服务端错误"
    )
    message: str = Field("System processing exception", description="错误摘要")
    errors: Optional[List[ErrorDetail]] = Field(
        None,
        description="调试信息（仅在开发环境返回）",
        examples=[{"type": "DATABASE_TIMEOUT", "message": "数据库连接超时"}],
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "description": "开发环境详细错误",
                    "value": {
                        "status": "error",
                        "code": 500,
                        "message": "System processing exception",
                        "timestamp": "2025-05-25T15:35:00Z",
                        "errors": [
                            {
                                "type": "NULL_REFERENCE",
                                "message": "Object reference not set to an instance",
                            },
                            {
                                "type": "STACK_TRACE",
                                "message": "at ApiService.Process() in /src/ApiService.cs:line 42",
                            },
                        ],
                    },
                }
            ]
        }


def get_request_id() -> str:
    """
    获取当前请求的唯一标识
    """
    try:
        return recyclable_app_correlation_id.get()
    except Exception:
        return "local_server"


def success_response(
    data: T, message: str = "Request processed successfully", code: int = 200
) -> SuccessResponse[T]:
    """
    构造标准成功响应
    """
    return SuccessResponse(
        data=data,
        message=message,
        code=code,
        request_id=get_request_id(),
        status="success",
    )


def fail_response(
    data: Optional[T] = None,
    message: str = "Bad request",
    errors: Optional[List[ErrorDetail]] = None,
    code: int = 400,
) -> FailResponse:
    """
    构造标准校验失败响应
    """
    return FailResponse(
        data=data,
        message=message,
        code=code,
        errors=errors,
        request_id=get_request_id(),
        status="fail",
    )


def error_response(
    data: Optional[T] = None,
    message: str = "Service error",
    errors: Optional[List[ErrorDetail]] = None,
    code: int = 500,
) -> ErrorResponse:
    """
    构造标准系统错误响应
    """
    return ErrorResponse(
        data=data,
        message=message,
        code=code,
        errors=errors,
        request_id=get_request_id(),
        status="error",
    )
